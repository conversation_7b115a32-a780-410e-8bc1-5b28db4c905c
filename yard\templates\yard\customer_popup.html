{% load i18n %}
{%block head%}
  {%load static%}

{%endblock%}
{% block content %}
<style>
body{border:0}
/*.dataTables_scroll{position:relative}*/
/*.dataTables_scrollHead{margin-bottom:40px;}*/
/*.dataTables_scrollFoot{position:absolute; top:38px}*/
table td{
  width:15px;
}
tfoot input {
        width: 100%;
        padding: 3px;
        box-sizing: border-box;
    }
</style>

<div class="row" style="overflow:auto;">
    <!-- <table class="table table-sm table-striped table-bordered aclist" > -->
      <table id="example" class="display" style="width:100%">
      <thead class="thead-dark" style="width:100%">
        <tr>
          <th>{% trans 'Action' %}</th>
          <th>{% trans 'Name 1' %}</th>
          <th>{% trans 'Name 2' %}</th>
          <th>{% trans 'Company' %}</th>
          <th>{% trans 'Street' %}</th>
          <th>{% trans 'Pin' %}</th>
          <th>{% trans 'Place' %}</th>
          <th>{% trans 'Country' %}</th>
          <th>{% trans 'Phone1' %}</th>
          <th>{% trans 'Website' %}</th>
          <th>{% trans 'Email1' %}</th>
        </tr>
      </thead>
      
      <tbody>
      {% for data in dataset %}
        <tr>
          <td><a href="javascript:loadCustomer('{{ data.id }}')">{% trans 'Select' %}</a></td>
          <td>{{ data.name1 }} </td>
          <td>{{ data.name2 }}</td>
          <td>{{ data.company }}</td>
          <td>{{ data.street }}</td>
          <td>{{ data.pin }}</td>
          <td>{{ data.place }}</td>
          <td>{{ data.country }}</td>
          <td>{{ data.contact_person1_phone }}</td>
          <td>{{ data.website }}</td>
          <td>{{ data.contact_person1_email }}</td>
        </tr>
      {% endfor %}
      </tbody>
      <tfoot class="thead-dark">
        <tr>
          <th>{% trans 'Action' %}</th>
          <th>{% trans 'Name 1' %}</th>
          <th>{% trans 'Name 2' %}</th>
          <th>{% trans 'Company' %}</th>
          <th>{% trans 'Street' %}</th>
          <th>{% trans 'Pin' %}</th>
          <th>{% trans 'Place' %}</th>
          <th>{% trans 'Country' %}</th>
          <th>{% trans 'Phone1' %}</th>
          <th>{% trans 'Website' %}</th>
          <th>{% trans 'Email1' %}</th>
        </tr>
      </tfoot>
    </table>
</div>
<div class="row">
  <br/>
  <br/>
</div>
<script>
   $('#example tfoot th').each( function () {
        var title = $(this).text();
        $(this).html( '<input type="text"/>' );
    } );

    // DataTable
    $(document).ready(function() {
    var table = $('#example').DataTable({
        initComplete: function () {
            // Apply the search
            this.api().columns().every( function () {
                var that = this;
 
                $( 'input', this.footer() ).on( 'keyup change clear', function () {
                    if ( that.search() !== this.value ) {
                        that
                            .search( this.value )
                            .draw();
                    }
                } );
            } );
        }
    });
    });
 
    function loadCustomer(id){
    $('#id_customer').val(id).trigger('change');
    $("#MyPopup").modal("hide");
  }
</script>
{% endblock %}


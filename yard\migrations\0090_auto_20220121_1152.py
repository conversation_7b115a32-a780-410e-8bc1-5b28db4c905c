# Generated by Django 3.1.1 on 2022-01-21 11:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0089_auto_20220117_2113'),
    ]

    operations = [
        migrations.AddField(
            model_name='transaction',
            name='deduction',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deduction', to='yard.article'),
        ),
        migrations.AddField(
            model_name='transaction',
            name='deduction_weight',
            field=models.DecimalField(blank=True, decimal_places=0, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='transaction',
            name='fruit_weight',
            field=models.DecimalField(blank=True, decimal_places=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='article',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='article', to='yard.article'),
        ),
    ]

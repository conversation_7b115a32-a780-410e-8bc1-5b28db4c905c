# Generated by Django 4.0.4 on 2022-11-21 22:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0162_transaction_approved'),
    ]

    operations = [
        migrations.CreateModel(
            name='DriverID',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ident_code', models.CharField(max_length=50, unique=True)),
                ('fahrzeug', models.CharField(blank=True, max_length=50, null=True)),
                ('spediteur', models.CharField(blank=True, max_length=50, null=True)),
                ('fahrer', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('time_type', models.CharField(blank=True, max_length=50, null=True)),
                ('start_period', models.DateTimeField(null=True)),
                ('end_period', models.DateTimeField(null=True)),
                ('always_valid', models.<PERSON>olean<PERSON>ield(default=False)),
                ('block', models.BooleanField(default=False)),
                ('one_time', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]

# Generated by Django 3.1.1 on 2021-07-15 15:20

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0052_auto_20210715_0835'),
    ]

    operations = [
        migrations.CreateModel(
            name='FirstWeightCameras',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cam1', models.BooleanField(default=False)),
                ('cam2', models.BooleanField(default=False)),
                ('cam3', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='SecondWeightCameras',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cam1', models.BooleanField(default=False)),
                ('cam2', models.BooleanField(default=False)),
                ('cam3', models.<PERSON>oleanField(default=False)),
            ],
        ),
        migrations.AlterField(
            model_name='supplier',
            name='ss_role_access',
            field=models.ManyToManyField(to=settings.AUTH_USER_MODEL),
        ),
    ]

{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{% load i18n %}
{% block head %}
    {% load static %}
{% endblock %}
{% block content %}
    <!-- /#sidebar-wrapper -->
    <div class="container">
    <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
        <i class="fas fa-align-justify"></i>
    </button>
    <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
                <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
                <p>{% if daily %} {% translate 'Daily List' %} {% else %}
                    {% translate 'Delivery Notes' %} {% endif %}</p>
            </div>
        </div>
    </div>

    <div class="container row">
        {% if messages %}
            {% for message in messages %}
                <div id="msg" class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}"
                     role="alert">{{ message }}</div>
            {% endfor %}
        {% endif %}
        <form method="POST" enctype="multipart/form-data" id="form_date_selection" class="row">
            {% csrf_token %}
            <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                 {% if not daily %}
                <div class="form-group">
                    <div class='input-group date' id='datetimepicker6'>
                        <label class="mt-2 mr-3">{% translate 'Date' %}</label>
                        <input type='date' class="form-control" id='del_from_date' name="fromdate"/>
                        <span class="input-group-addon">
                   <span class="glyphicon glyphicon-calendar"></span>
                   </span>
                    </div>
                </div>
                     {% endif %}
            </div>
            <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12" id="to-date-select">
                 {% if not daily %}
                <div class="form-group">
                    <div class='input-group date' id='datetimepicker7'>
                        <label class="mt-2 mr-3">{% translate 'To' %}</label>
                        <input type='date' class="form-control" id="del_to_date" name="todate"/>
                        <span class="input-group-addon">
                   <span class="glyphicon glyphicon-calendar"></span>
                   </span>
                    </div>
                </div>
                 {% endif %}
            </div>
            <div class="col-xl-1 col-lg-1 col-md-1 col-sm-12 col-12">
                <label class="mt-2 mr-3">{% translate 'Lieferschein nummer' %}</label>
                <input type='number' class="form-control" name="wiegescheinnummer"/>
            </div>
            <div class="col-xl-1 col-lg-1 col-md-1 col-sm-12 col-12">
                <label class="mt-2 ml-3">Externes Wiegen</label><br>
                <input type='checkbox' class="mt-2 ml-3"  name="external_weighing"/>
            </div>
            <div class="col-xl-6 col-lg-6 col-md-6 col-6 mt-4">
                <div style="width: 120%;">
                    {% if daily %}
                        {% if search == True %}
                     <button type="submit" class="btn btn-primary mt-2"
                            name="back_button">{{ "Zurück" }}</button>
                            {% endif %}
                    {% endif %}
                    <button type="submit" class="btn btn-primary mt-2" id="btn_date_selection"name="date_selection">{{ "Ausführen" }}</button>
                    <button type="submit" class="btn btn-primary mt-2" id="id_export_data_btn" name="export_data">{{ "CSV Export" }}</button>
                    <button type="submit" class="btn btn-primary mt-2" id="id_mail_data_btn" name="mail_data">{{ "E-Mail Export" }}</button>
                    <button type="submit"  class="btn btn-primary mt-2" id="export-pdf" name="export_pdf">{{ "PDF Export" }}</button>
                    
                      </form>
    <button type="button" class="btn btn-primary mt-2" id="download_delivery_notes" onclick="zip_delivery_notes()">
            <span id="delivery-notes-loader"></span> {{ "ZIP-Datei erstellen" }}</button>
                </div>
            </div>
    </div>

    <!--table strat-->
    <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <label>{% translate "Show entries" %}:</label>
            <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
            </select>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <label>{% translate "Search" %}:</label>
            <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search"
                   id="mysearch">
        </div>
        <div class="container">
          <div class="row" style="margin-left:-50px;">
            <div class=" table-responsive">
              <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="deliveryNoteTable">
                <thead>
                  <tr>
                    <th>{% translate 'Action' %}</th>
                    <th>{% translate 'Lfs.-Nr.' %}</th>
                    {% if master_container.status == False %}
                    <th>{{ "Kennz." }}</th>
                    {% else %}
                    <th>Containernummer</th>
                    {% endif %}
                    <th>{% translate 'Status' %}</th>
                    <th>{{ form.article.label }}</th>
                    {% if master_container.status == False %}
                    <th>{{ form.customer.label }}</th>
                    <th>{% if request.session.supplier %} {{ request.session.supplier }} {% else %}
                        {% translate 'Supplier' %} {% endif %}</th>
                    {% else %}
                    <th>Firma</th>
                    <th>Gebäude</th>
                    {% endif %}
                    
                   <th>{{form.forwarders.label}}</th>
                    <th>{% translate 'Erstgewicht' %} (kg)</th>
                    <th>{% translate 'Zweitgewicht' %} (kg)</th>
                    <th>{{form.net_weight.label}} (kg)</th>
                    <!--th>{{form.deduction_weight.label}}(kg)</th-->
                    <!-- <th>{{form.material_weight.label}} (kg)</th> -->
{#                    <th>{% translate 'Total Price' %} (&#128;)</th>#}
                    {% if not daily %}
{#                    <th>{{form.secondw_alibi_nr.label}}</th>#}
                  {% endif %}
                    <th>{% translate 'Created on' %}</th>
                    <th>{% translate 'Updated on' %}</th>
                  </tr>
                </thead>
                <tbody class="mt-4">
                  {% for data in dataset %}
                  <tr class="loadWD" ondblclick="javascript:loadDeliveryNoteDetails('{{ data.id }}');">
                    <td> <p> <a class="loadW" href="javascript:loadDeliveryNoteDetails('{{ data.id }}')" ><i class="fas fa-pencil-alt text-primary ml-1"></i></a>
                      </p>
                    {% if request.user.is_superuser %}
                    <a class="confirmdelete" href="{% url 'deliverynote_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-1 text-danger"></i></a> </p>
                    {% endif %}
                        <p><a class="hover-pointer-cursor" style="margin-left: -6px;" target="_blank" href="/stats/deliverynotes/{{ data.id }}/pdf"><i
                                class="fas fa-file-pdf ml-2"></i></a>
                                {% if sh_invoice.status == 1 %}
                        <a class="hover-pointer-cursor" target="_blank" href="/stats/invoice/?id={{ data.id }}&loc=1"><i
                          class="fas fa-file ml-2" title="Rechnungs"></i></a>{% endif %}
                        </p>
                    </td>
                    {% if data.lfd_nr is not None and data.lfd_nr != "" %}
                        <td>{{ data.lfd_nr }}</td>
                    {% else %}
                        <td>{{ data.id }}</td>
                    {% endif %}
                    <td class="vehicleName{{data.id}}">{{ data.vehicle }}</td>
                    {% if data.status == '1'%}
                    <td>Ausgang </td>
                    {% elif data.status == '0'%}
                    <td>Eingang </td>
                    {% else %}
                    <td></td>
                    {% endif%}
                    <td>{{ data.article|default_if_none:" " }}</td>
                    <td>{{ data.customer|default_if_none:" " }}</td>
                    <td>{{ data.supplier|default_if_none:" " }}</td>
                  <td>{{ data.forwarders|default_if_none:" " }}</td>
                    <td>{{ data.first_weight|default_if_none:"0" }}</td>
                    <td>{{ data.second_weight|default_if_none:"0" }}</td>
                    <td>{{ data.net_weight|default_if_none:"0" }}</td>
                    <!--td>{{ data.deduction_weight|default_if_none:"0" }}</td-->
                    <!-- <td>{{ data.material_weight|default_if_none:"0" }}</td> -->
{#                    <td>{{ data.total_price|default_if_none:"0" }}</td>#}
                    {% if not daily %}
{#                    <td>{{ data.secondw_alibi_nr|default_if_none:"-" }}</td>#}
                  {% endif %}
                    <td>{{ data.created_date_time|default_if_none:"-" }}</td>
                    <td>{{ data.updated_date_time|default_if_none:"-" }}</td>
                  </tr>
                  {% endfor %}
      <!--            <tfoot hidden>-->
      <!--               <tr>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text"class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text"class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--                 <th rowspan="1" colspan="1">-->
      <!--                    <input type="text" class="form-control">-->
      <!--                 </th>-->
      <!--               </tr>-->
      <!--            </tfoot>-->
                </tbody>
              </table>
            </div>
          </div>
        </div>
    </div>
{% comment %}
    <div class="container">
        <div class="row pl-3" style="margin-left:-50px;">
            <div class=" table-responsive">
                <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%"
                       id="deliveryNoteTable">
                    <thead>
                    <tr>
                        <th>{% translate 'Action' %}</th>
                        <th>{% translate 'Lfs.-Nr.' %}</th>
                        <th>{% translate 'Code' %} .1</th>
                        <th>{% translate 'Status' %}</th>
                        <th>{{ form.article.label }}</th>
                        <th>{{ form.customer.label }}</th>
                        <th>{% if request.session.supplier %} {{ request.session.supplier }} {% else %}
                            {% translate 'Supplier' %} {% endif %}</th>
                        <th>{{ form.forwarders.label }}</th>
                        <th>{{ form.first_weight.label }} (kg)</th>
                        <th>{{ form.second_weight.label }} (kg)</th>
                        <th>{{ form.net_weight.label }} (kg)</th>
                        <!-- <th>{{ form.deduction_weight.label }}(kg)</th> -->
                        <!-- <th>{{ form.material_weight.label }} (kg)</th> -->
                        <th>{% translate 'Total Price' %} (&#128;)</th>
                        <th>{{ form.secondw_alibi_nr.label }}</th>
                        <th>{% translate 'Created on' %}</th>
                        <th>{% translate 'Updated on' %}</th>
                    </tr>
                    </thead>
                    <tbody class="mt-4">
                    {% for data in dataset %}
                        <tr class="loadWD" ondblclick="javascript:loadDeliveryNoteDetails('{{ data.id }}');">
                            <td><p><a class="loadW" href="javascript:loadDeliveryNoteDetails('{{ data.id }}')"><i
                                    class="fas fa-pencil-alt text-primary ml-1"></i></a>
                                {% if request.user.is_superuser %} {% else %} </p> {% endif %}
                                {% if request.user.is_superuser %}
                                    {% if not daily %}
                                        <a class="confirmdelete"
                                           href="{% url 'deliverynote_delete' identifier=data.id %}"><i
                                                class="fas fa-trash-alt ml-1 text-danger"></i></a> </p>
                                    {% endif %}
                                {% endif %}
                                <p><a class="hover-pointer-cursor" target="_blank"
                                      href="/stats/deliverynotes/{{ data.id }}/pdf"><i
                                        class="fas fa-file-pdf ml-2"></i></a>
                                    {% if sh_invoice.status == 1 %}
                                        <a class="hover-pointer-cursor" target="_blank"
                                           href="/stats/invoice/?id={{ data.id }}&loc=1"><i
                                                class="fas fa-file ml-2" title="Rechnungs"></i></a>{% endif %}
                                </p>
                            </td>
                            <td>{{ data.id }}</td>
                            <td class="vehicleName{{ data.id }}">{{ data.vehicle }}</td>
                            {% if data.status == '1' %}
                                <td>Ausgang</td>
                            {% elif data.status == '0' %}
                                <td>Eingang</td>
                            {% else %}
                                <td></td>
                            {% endif %}
                            <td>{{ data.article|default_if_none:" " }}</td>
                            <td>{{ data.customer|default_if_none:" " }}</td>
                            <td>{{ data.supplier|default_if_none:" " }}</td>
                            <td>{{ data.forwarders|default_if_none:" " }}</td>
                            <td>{{ data.first_weight|default_if_none:"0" }}</td>
                            <td>{{ data.second_weight|default_if_none:"0" }}</td>
                            <td>{{ data.net_weight|default_if_none:"0" }}</td>
                            <!-- <td>{{ data.deduction_weight|default_if_none:"0" }}</td> -->
                            <!-- <td>{{ data.material_weight|default_if_none:"0" }}</td> -->
                            <td>{{ data.total_price|default_if_none:"0" }}</td>
                            <td>{{ data.secondw_alibi_nr|default_if_none:"-" }}</td>
                            <td>{{ data.created_date_time|default_if_none:"-" }}</td>
                            <td>{{ data.updated_date_time|default_if_none:"-" }}</td>
                        </tr>
                    {% endfor %}
                    <!--            <tfoot hidden>-->
                    <!--               <tr>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text"class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text"class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--                 <th rowspan="1" colspan="1">-->
                    <!--                    <input type="text" class="form-control">-->
                    <!--                 </th>-->
                    <!--               </tr>-->
                    <!--            </tfoot>-->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endcomment %}
    <div class="container">
        <div class="row mb-5">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                <!-- Material form login -->
                <div class="card p-3 mt-4" id="div_delivery_form">
                    <h5 class="card-header info-color white-text py-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18" class=""
                                   aria-expanded="true">
                                    <div class="row">
                                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 text-left"
                                             style="display: flex;">
                                            <p class="mb-0 pt-2 mr-4 text-color text_color">{{ "Lieferschein" }}</p>
                                        </div>
                                    </div>
                                </a>
                            </h4>
                        </div>
                    </h5>

                    <div id="collapse_18" class="collapse show" style="">
                        <div class="panel-body">
                            <div class="contanier">
                                <form class="form-group" method="POST" enctype="multipart/form-data"
                                      id="form_delivery_note" target="_blank">
                                    {% csrf_token %}
                                    <input type="hidden" name="secondw_alibi_nr" id="id_secondw_alibi_nr">
                                    <input type="hidden" name="firstw_alibi_nr" id="id_firstw_alibi_nr">
                                    <input type="hidden" name="firstw_date_time" id="id_firstw_date_time">
                                    <input type="hidden" name="secondw_date_time" id="id_secondw_date_time">
                                    <input type="hidden" name="vehicle_weight_flag" id="id_vehicle_weight_flag">
                                    <input type="hidden" name="vehicle_second_weight_flag"
                                           id="id_vehicle_second_weight_flag">
                                    <input type="hidden" name="trans_flag" id="id_trans_flag">
                                    <input type="hidden" name="price_per_item" id="id_article_price">
                                    <input type="hidden" name="fruit_weight" id="id_fruit_weight">
                                    {% comment %} <input type="hidden" name="deduction_weight" id="id_deduction_weight" > {% endcomment %}
                                    <input type="hidden" name="status" id="id_status">
                                    <div class="row">
                                        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                                            <div class="md-form mb-3">
                                                {% if master_container.status == False %}
                                                <label>{{ form.vehicle.label }}</label>
                                                {% else %}
                                                <label>Containernummer</label>
                                                {% endif %}
                                                <input type="text" class="form-control" id="id_vehicle" readonly>
                                                <input type="hidden" id="vehicle_id" name="vehicle">
                                            </div>
                                            <div class="md-form mb-3">
                                                {% if master_container.status == False %}
                                                <label>{{ form.customer.label }}</label>
                                                {% else %}
                                                <label>Firma</label>
                                                {% endif %}
                                                {{ form.customer }}
                                            </div>

                                            <div class="md-form mb-3">
                                                <label>{{ "Erstgewicht [kg]" }}</label>
                                                {{ form.first_weight }}
                                            </div>
                                            <div class="md-form mb-3">
                                                <label>{{ "Zweitgewicht [kg]" }}</label>
                                                {{ form.second_weight }}
                                            </div>
                                            <div class="md-form mb-3">
                                                <label>Richtung</label>
                                                <select class="form-control" name="status" id="id_status">
                                                    <option value="0">Eingang</option>
                                                    <option value="1">Ausgang</option>
                                                </select>
                                            </div>

                                            <div class="md-form mb-3">
                                                <label>{{ form.forwarders.label }}</label>
                                                {{ form.forwarders }}
                                            </div>

                                        </div>
                                        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">

                                            <div class="md-form mb-3">
                                                <label>{% translate 'Auftrag' %}</label>
                                                <select name="contract_number" class="form-control contract_number"
                                                        data-contract-value="0" id="id_contract_number">
                                                    <option value="">---------</option>
                                                    {% for contract in contracts %}
                                                        <option value="{{ contract.contract_number }}">{{ contract.name }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>

                                            <div class="md-form mb-3">
                                                <label>{{ form.article.label }}</label>
                                                {{ form.article }}
                                            </div>
                                            <div class="md-form mb-3">
                                                {% if master_container.status == False %}
                                                <label>{% if request.session.supplier %}
                                                    {{ request.session.supplier }} {% else %}
                                                    {% translate 'Supplier' %} {% endif %}</label>
                                                {% else %}
                                                    <label>Gebäude</label>
                                                {% endif %}
                                                {{ form.supplier }}
                                            </div>
                                            <div class="md-form mb-3">
                                                <label>{{ form.net_weight.label }}</label>
                                                {% comment %} {{ form.net_weight}} {% endcomment %}
                                                <input type="text" class="form-control" id="id_net_weight"
                                                       name="net_weight" readonly>
                                            </div>
                                            <div class="md-form mb-3">
                                                <label>{{ "Lfs.-Nr." }}</label>
                                                <input type="text" name="id" id="id" class="form-control" readonly>
                                            </div>
                                        {% if wps.comment == 1 or wps.comment == 2 %}
                                         <div class="md-form mb-3">
                                                <label>Bemerkung</label>
                                                <textarea name="comment" id="id_comment" rows="5" class="form-control"></textarea>
                                            </div>
                                        </div>
                                      {% endif %}

                                    </div>
                                    {% if sh_invoice.status == 1 %}
                                        <hr/>
                                        <p style="font-weight:600;">Artikel im Detail:</p style="font-weight:600;">
                                        <input type="hidden" id="ded_item" name="ded_remove" value=""/>
                                        <div class="contain" id="abzugs_div">
                                        <div class="row">

                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">

                                            <div class="md-form mb-3">
                                                <label>{% translate 'Material' %}</label>
                                                <select class="form-control" name="deduction" id="id_deduction">
                                                    <option value="" selected="">---------</option>
                                                    {% for i in art %}
                                                        <option value={{ i.id }}>{{ i.name }}</option>
                                                    {% endfor %}
                                                </select>

                                            </div>
                                        </div>
                                        <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12 text-left">
                                            <div class="md-form mb-2">
                                                <label>{% translate 'Quantity' %} (kg)</label>
                                                <input type="number" name="deduction_weight" step="1" value="0"
                                                       id="id_deduction_weight" class="form-control">

                                            </div>
                                        </div>
                                        <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12 text-left">
                                            <div class="md-form mb-2">
                                                <label>Einzelpreis</label>
                                                <input type="text" class="form-control" value="0.0"
                                                       id="id_deduction_price" name="deduction_price">
                                            </div>
                                        </div>
                                        <div class="col-xl-1 col-lg-1 col-md-1 col-sm-10 col-12 text-left">
                                            <div class="md-form mb-2">
                                                <label>Mwst</label>
                                                <input type="text" class="form-control" value="0" id="id_deduction_vat"
                                                       name="deduction_vat">
                                            </div>
                                        </div>
                                        <div class="col-xl-1 col-lg-1 col-md-1 col-sm-2 col-4 text-left">
                                            <div class="md-form mb-2">
                                                <label>Neuen</label>
                                                <button id="new_deduction" type="button" class="btn btn-success"><i
                                                        class="fa fa-plus"></i></button>
                                            </div>
                                        </div>
                                    {% endif %}
                                    </div>

                                    </div>
                                    <div class="row">
                                        <div class="col-xl-9 col-lg-9 col-md-9 col-sm-12 col-12 text-left">
                                            <div class="ml-2">
                                                <a id="load_images_btn" class="btn btn-primary ml-2"
                                                   style="color: white;">{{ "Bilder-Lieferschein" }}</a>
                                                <button id="save_delivery_note" type="button" name="save_button"
                                                        class="btn btn-primary ml-1"><i
                                                        class="fas fa-save ml-2"></i> {% translate 'Save2' %}</button>
                                                <button id="print_delivery_note" name="print_button"
                                                        class="btn btn-primary ml-2"><i
                                                        class="fas fa-print ml-2"></i> {% translate 'Print' %}</button>
                                                <button id="send_email_btn" name="send_email"
                                                        class="btn btn-primary ml-2"><i
                                                        class="fas fa-paper-plane ml-2"></i> {{ "E-Mail-Versand" }}
                                                </button>
                                                <button id="cancel_delivery_note"
                                                        class="btn btn-primary ml-2"> {% translate 'Cancel' %}</button>
                                                {% if sh_invoice.status == 1 %}
                                                    <a id="anchor_inv" href="stats/invoice/?id=">
                                                        <button id="rechnung_btn" name='print_in'
                                                                onClick="javascript:print_invoice()" type="button"
                                                                class="btn btn-primary ml-2">{% translate 'Invoices' %}</button>
                                                    </a>{% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
    <div id="MyModal" style="overflow-y: auto;" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content  p-4">
                <div class="modal-body">
                    <img class="img-responsive" src="" style="width:100%"/>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div id="MyPopupImg" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <div class="modal-content p-4">
                <div class="modal-header modal-header-custom">
                    <h4 class="modal-title">{% translate 'Captured Images' %}</h4>
                    <button type="button" class="close" data-dismiss="modal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <center></center>
                </div>
                <div class="modal-footer">
                    <input type="button" id="btnClosePopup" value="Close" class="btn btn-secondary"/>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block scripts %}
    <script src="{% static 'stats/js/stats_custom.js' %}"></script>
    <script type="text/javascript" src="{% static 'stats/js/jszip.js' %}"></script>
    <script>

        $(document).ready(function () {

            var contract_id = $(".contract_number").val()
            $(".contract_number").change(function () {

                if ($(this).attr("data-contract-value") == 1) {

                    if (confirm("sind Sie sicher, dass Sie den Kontakt ändern wollen. Es kann auch Auswirkungen auf die anderen Felder haben")) {


                        $.ajax({
                            type: "GET",
                            url: "/contract/" + $(this).val(),
                            success: function (data) {
                                $("#id_customer").val(data.customer.id).trigger('change')
                                $("#id_supplier").val(data.supplier[0].id).trigger('change')
                                $("#id_article").val(data.required_materials[0].material.id).trigger('change')
                            }
                        });

                    } else {
                        window.location.reload()
                    }
                }

            })
        })

        $(document).ready(function () {

             if (window.location.pathname == "/stats/daily_delivery_list/") {
                $("#export-pdf").hide();
                $("#download_delivery_notes").hide();
                // $("#to-date-select").hide();
            }
        })

        // DataTable
        $(document).ready(function () {
            var table = $('#deliveryNoteTable').DataTable(
                {
                    "bLengthChange": false,
                    initComplete: function () {
                        // Apply the search
                        this.api().columns().every(function () {
                            var that = this;
                            // // console.log(that)
                            // <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
                            // <!--                       if ( that.search() !== this.value ) {-->
                            // <!--                           that.search( this.value ).draw();-->
                            // <!--                       }-->
                            // <!--                   } );-->
                        });
                    }
                });

            $("#deliveryNoteTable_filter").hide()

            $("#deliveryNoteTable_previous").html("Zurück");

            // custom search filter
            $('#mysearch').on('keyup', function () {
                table.search(this.value).draw();
            });

            //  custom show entries
            $('#showentries').change(function () {
                table.page.len(this.value).draw();
            });
            let data = $("#deliveryNoteTable_previous").html();
            data.replace("züruck","zurück")
        });

        $("#id_first_weight").keyup(function () {
            first = $(this).val();
            second = $("#id_second_weight").val();
            net = Math.abs(second - first);
            $("#id_net_weight").val(net);
        })

        $("#id_second_weight").keyup(function () {
            first = $("#id_first_weight").val();
            second = $(this).val();
            net = Math.abs(second - first);
            $("#id_net_weight").val(net);
        });

        $(".loadW").click(function (e) {
            $('.collapse_18').addClass('show');
            window.location = "#div_delivery_form";
        });
        $(".loadWD").dblclick(function (e) {
            $('.collapse_18').addClass('show');
            window.location = "#div_delivery_form";
        });

        function zip_delivery_notes() {
            $("#delivery-notes-loader").html("<span style='height:15px;width:15px;' class='delivery-loader spinner-border'></span> ");
            $("#download_delivery_notes").attr('disabled', true);
            from_date = $("#del_from_date").val();
            to_date = $("#del_to_date").val();
            $.ajax({
                url: "/stats/get_delivery_notes",
                type: 'get',
                data: {fromdate: from_date, todate: to_date},
                success: function (data) {
                    console.log(data);
                    // return
                    if (data.length > 0) {
                        var zip = new JSZip();
                        data.forEach((fileData, i) => {
                            zip.file(fileData?.id+".pdf", fileData?.pdf.replace(/data:.*?;base64,/, ""), {base64: true})
                        })
                        zip.generateAsync({
                            type: "base64"
                        }).then(data => window.location.href = "data:application/zip;base64," + data)
                    } else {
                        alert("Keine daten gefunden")
                    }
                    $(".delivery-loader").removeClass("spinner-border");
                    $("#download_delivery_notes").attr('disabled', false);
                }
            });
        }

        function remove_ded() {
            that = event.target
            arr = []
            arr.push($(that).parent().parent().siblings('.ded').children().children('#id_deduction').val())
            $("#ded_item").val(arr)

            $(that).parent().parent().parent().remove()
        };

        $("#new_deduction").click(function () {
            html = `<div class="row">
    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left ded">
      <div class="md-form mb-3">
        <label>{% translate 'Material' %}</label>
        {{ form.deduction }}
      </div>
</div>
<div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12 text-left">
  <div class="md-form mb-2">
    <label>{% translate 'Quantity' %} (kg)</label>
    <input type="number" name="deduction_weight" step="1" id="id_deduction_weight" class="form-control">
</div>
</div>
<div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12 text-left">
    <div class="md-form mb-2">
      <label>Preis</label>
      <input type="text" class="form-control" id="id_deduction_price" name="deduction_price" >
    </div>
</div><div class="col-xl-1 col-lg-1 col-md-1 col-sm-10 col-12 text-left">
  <div class="md-form mb-2">
      <label>Mwst</label>
      <input type="text" class="form-control" id="id_deduction_vat" name="deduction_vat" >
    </div>
  </div>
  <div class="col-xl-1 col-lg-1 col-md-1 col-sm-2 col-4 text-left">
    <div class="md-form mb-2">
      <label>Neuen</label>
    <button onclick=javascript:remove_ded() type="button" class="btn btn-danger"><i class="fa fa-minus" style="pointer-events:none;"></i></button>
    </div>
</div>
</div>`

            $("#abzugs_div").append(html);
        });

        function print_invoice(event) {
            var sum = 0
            var href = $("#anchor_inv").attr('href')
            var net = parseInt($("#id_net_weight").val())
            $("input[id=id_deduction_weight]").each(function (i, v) {
                sum += parseInt(this.value)
                console.log(sum)
            })
            if (sum > net) {
                alert("You have Entered Wrong Values, Deduction must be Smaller Than Net Weight")
                $("#anchor_inv").attr('href', '#')
                return false
            } else {
                $("#anchor_inv").attr('href', href)
                $("#anchor_inv").attr('target', '_blank')

                id = $("#id").val()
                form = $('#form_delivery_note');
                //form.attr('action','')
                form.attr('target', '')
                form.submit()
            }
        }


    </script>
{% endblock %} 

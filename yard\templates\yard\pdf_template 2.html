<!DOCTYPE html>
<html lang="en">
   <head>
       {%block head%}
          {%load static%}
        {%endblock%}
        {% load i18n %}
        {% load l10n %}
      {%load custom_template_filters%}
      <meta charset="utf-8">
      <title>Delivery Note</title>
      <style>
      .sp{
         font-size:14px;
      }
          .Row {
    display: table;
    width: 100%; /*Optional*/
    table-layout: fixed; /*Optional*/
    border-spacing: 40px; /*Optional*/
}
          .Column {
            display: table-cell;
              border-left: 10px;
        }
        table { -pdf-keep-with-next: true; }
         p { margin: 0; -pdf-keep-with-next: true; }
         p.separator { -pdf-keep-with-next: false; font-size: 6pt; }
      </style>
   </head>
   <body>
      <div class="a4_sheet">
         <div class="row" style="padding-top:20%;">
            <br><br><br>
            <table border="1" style="width:100%">
               <tr style="height: 100px;">
                  <td colspan="3" style="width:50%;text-align:left;padding-left:5px;font-size: 14pt;height:50%">
                     
                     {% for head in logo %}
                     {{head}}<br>
                     {% endfor %} <br>  
                     {% comment %} <b>{%if request.session.customer %}{% trans request.session.customer %} {%else%}{% trans "Customer" %}{%endif%}:</b><br> {{context}} {% endcomment %}
                     <b>{{customer}}:</b><br> {{context}}
                     
                     {% comment %} {{dataset.customer.salutation}}  {% endcomment %}
                     
                     {{ dataset.customer.name1|space }} {% if dataset.customer.name2 %} {{ dataset.customer.name2|space }} {% endif %} <br>

                     {% if dataset.customer.description is not None %}
                     {{ dataset.customer.description|space }}<br>
                     {%endif%}

                     {% if dataset.customer.street is not None %}
                     {{ dataset.customer.street|space }}<br>
                     {%endif%}

                     {% if dataset.customer.pin is not None %}
                     {{ dataset.customer.pin|space }}
                     {%endif%}
                     
                     {% if dataset.customer.place is not None %}
                      {{ dataset.customer.place|space }}<br>
                      {%endif%}
                  </td>
                  <td colspan="3" style="text-align:center;">
                   {% for img in header_image %}
                   {% if img.logo.url is not None %}
                   <img src="{{ img.logo.url }}" style="width:200%;height:10%;"/>
                   {% endif %}
                  {% endfor %}

                  </td>
               </tr>
               <tr style="height: 80px;">
                  <td style="font-size:10pt;text-align:center;width:25%;padding-left:2px;">
                     <b>Wiegeschein<br>
                     Lieferschein</b> 
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     lfd. Nr<br><br>
                    {% if dataset.lfd_nr is not None and dataset.lfd_nr != "" %}
                        {{ dataset.lfd_nr }}
                    {% else %}
                        {{ dataset.id }}
                    {% endif %}
                  </td>
                  <td style="padding-left:5px;text-align:center;" >
                     Datum <br><br>
                     {% if tara_date == '1' or dataset.status == "0" %}
                        {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|date:"d.m.y" }}
                        {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|date:"d.m.y" }}
                        {% else %} {{ dataset.secondw_date_time|date:"d.m.y" }} {% endif %}
                      {% elif dataset.status == "1" %}
                        {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|date:"d.m.y" }}
                        {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|date:"d.m.y" }}
                        {% else %} {{ dataset.secondw_date_time|date:"d.m.y" }} {% endif %}
                      {% else %}
                        {{ dataset.secondw_date_time|date:"d.m.y" }}
                      {% endif %}
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     Uhrzeit<br><br>
                     {% if tara_date == '1' or dataset.status == "0" %}
                      {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|time:"H:i" }}
                      {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|time:"H:i" }}
                      {% else %} {{ dataset.secondw_date_time|time:"H:i" }} {% endif %}
                     {% elif dataset.status == "1" %}
                      {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|time:"H:i" }}
                      {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|time:"H:i" }}
                      {% else %} {{ dataset.secondw_date_time|time:"H:i" }} {% endif %}
                     {% else %}
                      {{ dataset.secondw_date_time|time:"H:i" }}
                      {% endif %}
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     Werk<br><br>
                     -----
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     Zufuhrart<br><br>
                     -----
                  </td>
               </tr>
               <tr style="height: 100px;">
               {% if io.status == 1 %}
                  <td colspan="2" style="width:50%;font-size:12pt;padding-left:5px;">
                     Beladestelle : <br>
                     {% if dataset.supplier.supplier_name is not None %}
                        {{ dataset.supplier.supplier_name|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.addition1 is not None %}
                        {{ dataset.supplier.addition1|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.addition2 is not None %}
                        {{ dataset.supplier.addition2|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.addition3 is not None %}
                        {{ dataset.supplier.addition3|space }} <br>
                     {% endif %}
                  </td>
                  <td colspan="1" style="width:50%;text-align:center;font-size:11pt;padding-bottom:2px;">
                  Richtung : {% if dataset.status == "0" %}
                              <br><br> Eingang 
                           {% elif dataset.status == '1' %}
                              <br><br> Ausgang
                        {% elif dataset.status == '2' %}
                              <br><br> Fremdwiegung
                           {% else %}
                              <br><br><br>
                           {% endif %}
                  </td>
                  {% else %}
                  <td colspan="3" style="width:50%;font-size:12pt;padding-left:5px;">
                     Beladestelle : <br>
                     {% if dataset.supplier.supplier_name is not None %}
                        {{ dataset.supplier.supplier_name|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.addition1 is not None %}
                        {{ dataset.supplier.addition1|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.addition2 is not None %}
                        {{ dataset.supplier.addition2|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.addition3 is not None %}
                        {{ dataset.supplier.addition3|space }} <br>
                     {% endif %}
                  </td>
                  {% endif %}
                  <td colspan="3" style="width:50%;font-size:12pt;padding-top: 6px">
                     <p style="padding-left:5px;">Projektnummer : {{dataset.contract_number.project_number}}</p><hr>
                     <p style="padding-left:5px;">Kennzeichen Nr. : &nbsp;{{ dataset.vehicle.license_plate|upper }}</p>
                     
                  </td>
               </tr>
            </table>
         </div>
         <div class="row">
            <table style="height:100px;width:100%;">
            
               <tr style="height:30px;font-size:10pt;padding-top:5px;border-top: 1px solid black;border-bottom: 1px solid black;">
                  <td style="border-left: 1px solid black;"></td>
                  <td >Datum</td>
                  <td style="">Uhrzeit</td>
                  <td style="width:5%;"></td>
                  <td >Gewicht</td>
                  <td >{{article}}</td>
                  <td style="border-right: 1px solid black;">AlibiNr.</td>
               </tr>
               {% if dataset.container_weighing %}
               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;border-left: 1px solid black;padding-left: 5px" >Bruttogewicht</td>
                  <td style="font-size:15px;">{{ dataset.secondw_date_time|date:"d.m.y" }}</td>
                  <td style="font-size:15px;width:10%;"> {{ dataset.secondw_date_time|time:"H:i" }}</td>
                  <td style="font-size:15px;width:5%;">{% if dataset.vehicle_second_weight_flag %}{% if dataset.vehicle_second_weight_flag == 1 %} PT {% elif dataset.vehicle_second_weight_flag == 2 %} H {%endif%} {%endif%}</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.second_weight }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.second_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td style="border-right: 1px solid black;"> {{ dataset.secondw_alibi_nr }}</td>
               </tr>
               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;border-left: 1px solid black;padding-left: 5px" >Fahrzeuggewicht</td>
                  <td style="font-size:15px;">{{ dataset.vehicle.vehicle_weight_date|date:"d.m.y" }}</td>
                  <td style="font-size:15px;width:10%;"> {{ dataset.vehicle.vehicle_weight_time|time:"H:i" }}</td>
                  <td style="font-size:15px;width:5%;"></td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.vehicle.vehicle_weight }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.vehicle.vehicle_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td style="border-right: 1px solid black;"> {{ dataset.vehicle.vehicle_weight_id }}</td>
               </tr>
               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;border-left: 1px solid black;padding-left: 5px" >Containergewicht</td>
                  <td style="font-size:15px;"></td>
                  <td style="font-size:15px;width:10%;"> </td>
                  <td style="font-size:15px;width:5%;"></td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{dataset.container.container_weight|unlocalize}}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.container_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td style="border-right: 1px solid black;"> </td>
               </tr>
               
               {% else %}
               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;border-left: 1px solid black;padding-left:5px;" >{% if dataset.vehicle_weight_flag == 1 or dataset.vehicle_second_weight_flag == 1 %} {% if dataset.vehicle_weight_flag == 1%} Tarawiegung {% else %} Bruttowiegung {% endif %} {% else %} {% trans "First Weighing" %} {% endif %}</td>
                  <td style="font-size:15px;">{{ dataset.firstw_date_time|date:"d.m.y" }}</td>
                  <td style="font-size:15px;">{{ dataset.firstw_date_time|time:"H:i" }}</td>
                  <td style="font-size:15px;">{% if dataset.vehicle_weight_flag == 1 %} PT {% elif dataset.vehicle_weight_flag == 2 %} H {%endif%}</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.first_weight|format_number }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.first_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td style="border-right: 1px solid black;">{{ dataset.firstw_alibi_nr }}</td>
               </tr> 
               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;border-left: 1px solid black;padding-left: 5px" >{% if dataset.vehicle_weight_flag == 1 or dataset.vehicle_second_weight_flag == 1 %} {% if dataset.vehicle_second_weight_flag == 1 %} Tarawiegung {% else %} Bruttowiegung {% endif %} {% else %} {% trans "Second Weighing" %} {% endif %}</td>
                  <td style="font-size:15px;">{{ dataset.secondw_date_time|date:"d.m.y" }}</td>
                  <td style="font-size:15px;width:10%;"> {{ dataset.secondw_date_time|time:"H:i" }}</td>
                  <td style="font-size:15px;width:5%;">{% if dataset.vehicle_second_weight_flag %}{% if dataset.vehicle_second_weight_flag == 1 %} PT {% elif dataset.vehicle_second_weight_flag == 2 %} H {%endif%} {%endif%}</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.second_weight|format_weight }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.second_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td style="border-right: 1px solid black;"> {{ dataset.secondw_alibi_nr }}</td>
               </tr>
               {% endif %}
               <tr style="height:30px;padding-top:5px;border-bottom:1px solid black">
                  <td colspan="3" style="font-size:15px;border-left: 1px solid black;padding-left: 5px" >{% trans "Net Weight" %}</td>
                  <td style="font-size:15px;">E</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.net_weight|format_weight }}&nbsp;kg</td>
                  {%else%}
                  <td style="font-size:15px;">{{dataset.net_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {%endif%}
                  <td style="font-size:15px;">{{dataset.article.name|space}}</td>
                  <td style="border-right: 1px solid black;"></td>
               </tr> {% if show_price == 'true' %}
                  <tr style="height:30px;padding-top:5px;">
                  <td colspan="2" style="font-size:15px;text-align:left;padding-left:4px;border-left:1px solid black;" >Barbezahler-Preis :</td>
                  <td  colspan="2" style="font-size:15px;text-align:left;">Preis pro ({% if article_unit == 0 %}{{ "kg" }}{% else %} {{ "t" }}{% endif %})</td>
                  <td style="font-size:15px;text-align:left; ">€ {{ dataset.article.price1 }}</td>
                  <td  style="font-size:15px;text-align:left;">Nettopreis :</td>
                  <td style="font-size:15px;text-align:left; border-right: 1px solid black;">€ {% if price_unit == 0 %} {{dataset.total_price|format_number|unlocalize}} {% else %} {{ price_total|format_number|unlocalize }} {% endif %}</td>
               </tr>
               <tr style="height:30px;padding-top:5px;">
                  <td colspan="2" style="font-size:15px;text-align:left;padding-left:4px;border-left:1px solid black;" ></td>
                   <td style="font-size:15px;"></td>
                  <td colspan="1" ></td>
                  <td colspan="2"  style="font-size:15px;text-align:left;padding-left:21px;">Mehrwertsteuer ({{dataset.article.vat}}%):</td>
                  <td  style="font-size:15px;text-align:left; border-right: 1px solid black;">   € {{tax|format_number|unlocalize}}</td>
               </tr>
               <tr style="height:30px;padding-top:5px;border-bottom:1px solid black">
                  <td colspan="2" style="font-size:15px;text-align:left;padding-left:4px;border-left:1px solid black;" ></td>
                    <td style="font-size:15px;"></td>
                  <td colspan="2"></td>
                  <td style="font-size:15px;text-align:left;">Bruttopreis:</td>
                  <td style="font-size:15px;text-align:left; border-right: 1px solid black;">   € {{price_after_tax|format_number|unlocalize}}</td>
               </tr>
               {% endif %}
               
            </table>
            {% if images.image1 or images.image2 or images.image3 %}
            <br>
            <span style="font-size:12px;border-left: 1px solid black;padding-left: 5px">
            Bilder : </span>
            {% endif %}
         </div>
         <br>

        <div class="Row">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          {% if images.image1 %}
              {% with absolute_url|add:images.image1.url as url_string1 %}
                  <div class="Column"><img style="width:200px;height: 200px" src="{{ url_string1|to_base64 }}" alt="">&nbsp;&nbsp;&nbsp;</div>
              {% endwith %}
          {% endif %} 
        {% if images.image2 %}
            {% with absolute_url|add:images.image2.url as url_string2 %}
                <div class="Column"><img style="width:200px;height: 200px" src="{{ url_string2|to_base64 }}" alt="">&nbsp;&nbsp;&nbsp;</div>
            {% endwith %}
        {% endif %}
        {% if images.image3 %}
            {% with absolute_url|add:images.image3.url as url_string3 %}
                <div class="Column"><img style="width:200px;height: 200px" src="{{ url_string3|to_base64 }}" alt="">&nbsp;&nbsp;&nbsp;</div>
            {% endwith %}
        {% endif %}
         {% if images.image1 or images.image2 or images.image3 %}
               <br><br><br><br><br><br><br><br><br>
         {% else %}
         {% if show_price == 'true' %}
               <br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br> {% else %}
               <br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
         {% endif %}
         {% endif %}
        </div>
          <br>
          <div class="row">
            <table border="1" style="height:100px;width:100%">
               <tr style="height:25px;padding-top:5px;text-align:center;">
                  <td style="padding-left: 5px">Unterschrift Deponie</td>
                  <td style="padding-left: 5px">Unterschrift des Fahrers</td>
                  {% comment %} <td style="border-width:1px;padding-left: 5px">Unterschrift des Empfängers</td> {% endcomment %}
               </tr>
               <tr >
                  <td style="height:125px;font-size:15pt;padding-bottom:5px;text-align:center;"><img src="{{sign.signature.url}}" style="width:100%;height:10%;"/><br> {{user_name}} <br> <span class="sp">(digital signiert)</span></td>
                  <td style="height:125px;font-size:15pt;padding-bottom:5px;text-align:center;"><img src="{{driver_sign.image.url}}" style="width:100%;height:10%;"/> </td>
                  {% comment %} <td ></td> {% endcomment %}
               </tr>
            </table>
         </div>
         <div class="row">
         <br>
            E: errechnet, PT: Preset Tara (voreingegebens Tara) H: Handeingabe. <br>
            Messwerte aus frei programmierbarer Zusatzeinrichung. Die geeichten Messwerte können eingesehen werden.
            Fur Überladungen haftet der Fahrzeuglenker.
         </div>
      </div>
      <input type="hidden" value="20/7204" name="customer code">
   </body>
</html>

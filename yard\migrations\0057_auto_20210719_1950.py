# Generated by Django 3.1.1 on 2021-07-19 19:50

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0056_transaction_contract_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='contract',
            name='allow_tara',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='contract',
            name='contract_status',
            field=models.CharField(choices=[('Active', 'ACTIVE'), ('Inactive', 'INACTIVE')], default='INACTIVE', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='contract',
            name='project_number',
            field=models.CharField(default='Test123', max_length=1000),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='contract',
            name='reserved_date',
            field=models.DateField(default=django.utils.timezone.now),
            preserve_default=False,
        ),
    ]

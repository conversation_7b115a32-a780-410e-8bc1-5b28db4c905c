{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}
<div class="container">
  <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
  <i class="fas fa-align-justify"></i>
  </button>
  <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
      <div class="content_text">
        <p class="mb-0">{% translate 'OVERVIEW' %}</p>
      </div>
      <div class="heding">
        <p>{% translate 'Forwarders' %}</p>
      </div>
    </div>
  </div>
  <!--table strat-->
  <div class="row">
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
      <label>{% translate "Show entries" %}:</label>
      <select class="form-control w-30" id="showentries">
        <option>10</option>
        <option>25</option>
        <option>50</option>
        <option>100</option>
        entries
      </select>
    </div>
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
      <label>{% translate "Search" %}:</label>
      <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="forwardersTable">
      <thead>
        <tr> {% if request.user.is_superuser %}
          <th>{% translate 'Action' %}</th> {% endif %}
          <th>{{ "Firma" }}</th>
          <th>{{form.firstname.label}}</th>
          <th>{{form.second_name.label}}</th>
          <th>{{form.street.label}}</th>
          <th>{{form.pin.label}}</th>
          <th>{{form.place.label}}</th>
          <th>{{form.country.label}}</th>
          <th>{{form.telephone.label}}</th>
<!--          <th>{{form.contact_person.label}}</th>-->
        </tr>
      </thead>
      <tbody class="mt-4">
        {% for data in dataset %}
        <tr class="loadFD" ondblclick="javascript:loadForwardersDetails('{{ data.id }}')"> {% if request.user.is_superuser %}
          <td><a class="loadF" href="javascript:loadForwardersDetails('{{ data.id }}')" ><i class="fas fa-pencil-alt text-primary  ml-4"></i></a><a class="confirmdelete" href="{% url 'forwarders_delete' identifier=data.id  %}" ><i class="fas fa-trash-alt ml-2 text-danger"></i></a></td>
          {% endif %}
          <td>{{ data.name|default_if_none:"-" }}</td>
          <td>{{ data.firstname|default_if_none:"-" }}</td>
          <td>{{ data.second_name|default_if_none:"-" }}</td>
          <td>{{ data.street|default_if_none:"-" }}</td>
          <td>{{ data.pin|default_if_none:"-" }}</td>
          <td>{{ data.place|default_if_none:"-" }}</td>
          <td>{{ data.country|default_if_none:"-" }}</td>
          <td>{{ data.telephone|default_if_none:"-" }}</td>
<!--          <td>{{ data.contact_person|default_if_none:"-" }}</td>-->
        </tr>
        {% endfor %}
<!--      <tfoot hidden>-->
<!--        <tr>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text"class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text"class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--        </tr>-->
<!--      </tfoot>-->
      </tbody>
    </table>
  </div>
</div>
<!--table end-->
<div class="container">
  <div class="row mb-5">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <!-- Material form login -->
      <div class="card p-3 mt-4">
           {% if request.user.is_superuser %}
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3">
              <div class="panel-heading">
                <h4 class="panel-title">
                  {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse_19"> {% endcomment %}
                    <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        <p class="mb-0 pt-2 mr-4 text-color text_color float-left">{% translate 'Forwarder' %}</p>
                        <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill" style="float:right; ">Neue Eingabe</button>
                      </div>
                    </div>
                  {% comment %} </a> {% endcomment %}
                </h4>
              </div>
            </h5>
          </div>
        </div>
          {% endif %}
        <div id="collapse_19" class="collapse " >
          <div class="panel-body">
            <form method="POST" enctype="multipart/form-data">
              {% csrf_token %}
              <input type="hidden" name="id" id="id">
              <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  <div>
                    <label>{{ "Firma" }}</label>
                    <!--                    <input type="Short name" class="form-control" name="Short name" placeholder="Short name">-->
                    {{ form.name}}{{ form.name.errors}}
                  </div>
                  <div>
                    <label>{{form.firstname.label}}</label>
                    <!--                    <input type="Name" class="form-control" name="Name" placeholder="Name">-->
                    {{ form.firstname}}{{ form.firstname.errors}}
                  </div>
                  <div>
                    <label>{{form.second_name.label}}</label>
                    <!--                    <input type="First name" class="form-control" name="First name" placeholder="First name">-->
                    {{ form.second_name}}{{ form.second_name.errors}}
                  </div>
                  <div>
                    <label>{{form.telephone.label}}</label>
                    {{ form.telephone}}{{ form.telephone.errors }}
                  </div>
<!--                    <div>-->
<!--                      <label>{{form.contact_person.label}}</label>-->
<!--                      &lt;!&ndash;                    <input type="Contact person" class="form-control" name="Contact person" placeholder="Contact person">&ndash;&gt;-->
<!--                      {{ form.contact_person }}{{ form.contact_person.errors }}-->
<!--                    </div>-->
                </div>
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  <div>
                    <label>{{form.street.label}}</label>
                    <!--                    <input type="Street" class="form-control" name="Street" placeholder="Street">-->
                    {{ form.street }}{{ form.street.errors }}
                  </div>
                  <div>
                    <label>{{form.pin.label}}</label>
                    <!--                    <input type="Pin" class="form-control" name="Pin" placeholder="Pin">-->
                    {{ form.pin }}{{ form.pin.errors }}
                  </div>
                  <div>
                    <label>{{form.place.label}}</label>
                    <!--                    <input type="Place" class="form-control" name="Place" placeholder="Place">-->
                    {{ form.place }}{{ form.place.errors }}
                  </div>
                  <div>
                    <label>{{form.country.label}}</label>
                    <!--                    <input type="Place" class="form-control" name="Place" placeholder="Place">-->
                    {{ form.country }}{{ form.country.errors }}
                  </div>

                </div>
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  <button type="submit" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate 'Save2' %}</button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
{% block scripts %}
<script>
  // DataTable
  $(document).ready(function() {
    var table = $('#forwardersTable').DataTable(
       {
          "bLengthChange": false,
         initComplete: function () {
             // Apply the search
             this.api().columns().every( function () {
                 var that = this;
                 console.log(that)
<!--                 $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
<!--                     if ( that.search() !== this.value ) {-->
<!--                         that.search( this.value ).draw();-->
<!--                     }-->
<!--                 } );-->
             } );
         }
        });
  
    $("#forwardersTable_filter").hide()
  
    // custom search filter
    $('#mysearch').on( 'keyup', function () {
        table.search( this.value ).draw();
    } );
  
    //  custom show entries
    $('#showentries').change(function() {
        table.page.len(this.value).draw();
    } );
  
  });

  {% if request.user.is_superuser %}
      $(".loadF").click(function () {
          window.location = "#forwarder-form-section";
          $("#collapse_19").addClass('show');
      });
      $(".loadFD").click(function () {
          window.location = "#forwarder-form-section";
          $("#collapse_19").addClass('show');
      });
  {% endif %}

   $("#new_entry").click(function(e){
     $("#collapse_19").addClass('show');
    
    return false;
  })
</script>
{% endblock %}

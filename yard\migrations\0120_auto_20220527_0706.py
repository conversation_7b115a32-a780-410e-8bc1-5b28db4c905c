# Generated by Django 3.1.1 on 2022-05-27 07:06

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0119_customer_tele_phone_number'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='article',
            options={'ordering': ('name',), 'verbose_name': 'Article'},
        ),
        migrations.AlterModelOptions(
            name='buildingsite',
            options={'verbose_name': 'Building Site'},
        ),
        migrations.AlterModelOptions(
            name='combination',
            options={'verbose_name': 'Combination'},
        ),
        migrations.AlterField(
            model_name='article',
            name='account',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Account'),
        ),
        migrations.AlterField(
            model_name='article',
            name='article_number',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='Article number'),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='article',
            name='avv_num',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Avv Num'),
        ),
        migrations.AlterField(
            model_name='article',
            name='balance_weight',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Balance weight'),
        ),
        migrations.AlterField(
            model_name='article',
            name='deduct_weight',
            field=models.DecimalField(blank=True, decimal_places=0, max_digits=10, null=True, verbose_name='Deduct Weight'),
        ),
        migrations.AlterField(
            model_name='article',
            name='density',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Density'),
        ),
        migrations.AlterField(
            model_name='article',
            name='description',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='article',
            name='entry_weight',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Entry Weight'),
        ),
        migrations.AlterField(
            model_name='article',
            name='list_price_gross',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='List Price Gross'),
        ),
        migrations.AlterField(
            model_name='article',
            name='list_price_net',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='List Price Net'),
        ),
        migrations.AlterField(
            model_name='article',
            name='min_quantity',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='Min Quantity'),
        ),
        migrations.AlterField(
            model_name='article',
            name='minimum_amount',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Minimum Amount'),
        ),
        migrations.AlterField(
            model_name='article',
            name='outgoing_weight',
            field=models.IntegerField(blank=True, default=0, null=True, verbose_name='Outgoing Weight'),
        ),
        migrations.AlterField(
            model_name='article',
            name='price1',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price 1'),
        ),
        migrations.AlterField(
            model_name='article',
            name='price2',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price 2'),
        ),
        migrations.AlterField(
            model_name='article',
            name='price3',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price 3'),
        ),
        migrations.AlterField(
            model_name='article',
            name='price4',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price 4'),
        ),
        migrations.AlterField(
            model_name='article',
            name='price5',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price 5'),
        ),
        migrations.AlterField(
            model_name='article',
            name='short_name',
            field=models.CharField(blank=True, max_length=40, null=True, verbose_name='Short Name'),
        ),
        migrations.AlterField(
            model_name='article',
            name='vat',
            field=models.DecimalField(blank=True, choices=[(Decimal('7.00'), '7%'), (Decimal('13.00'), '13%'), (Decimal('19.00'), '19%')], decimal_places=2, max_digits=10, null=True, verbose_name='Vat'),
        ),
        migrations.AlterField(
            model_name='user',
            name='address',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Address'),
        ),
        migrations.AlterField(
            model_name='user',
            name='telephone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Telephone'),
        ),
    ]

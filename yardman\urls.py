from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import routers
from yard.api import CustomAuthToken, DisplayTexttView, GetCMR, GetSampleYardTicketView, HandTransmitterView, \
    LoadingBoxView, MaxVehicleView, RouteImageView, UserCreateAPIView, VehicleView, CustomerView, \
    SupplierView, ArticleView, BuildingSiteView, \
    WarehouseView, \
    TransactionView, TransactView, CombinationView, CombinationReadView, ForwarderView, SaveImageView, Pdf_print, \
    DriverSignView, DetailCombinationView, ContractView, Pdf_print_lieferscheine, BackendPrintQRCode, \
    Pdf_print_external, Pdf_print_lieferscheine_internal, Pdf_woimg, Pdf_sebald, dump_db_database, import_data, \
    dump_media_files, import_media, PrintPdfDoc, YardTicket, MasterYardTicketView, TransactPageView, DriverIDView, \
    BarriersView, camerafeed, OfficeNameTimingView, users_without_auth, NotificationView, VehicleTypeView, \
    fire_alarm, FireAlarmView, getPhoneNumberRegistered, SendMessage, OfficeNameView, TransactTabletView, \
    camerafeed_type2, TransactFilterView, RemoveAllNotification, TourApproveView, RemoveAllTourApprove, \
    TransactPageHoflisteView, get_barrier_status, ContractPageView
from rest_framework.authtoken.views import obtain_auth_token

router = routers.DefaultRouter()

router.register("Vehicle-View", VehicleView, basename='vehicle')
router.register("customer-View", CustomerView, basename='customer')
router.register("supplier-View", SupplierView, basename='supplier')
router.register("article-View", ArticleView, basename='article')
router.register("buildingsite-View", BuildingSiteView, basename='buildingsite')
router.register("Warehouse-View", WarehouseView, basename='warehouse')
router.register("TourApprove-View", TourApproveView, basename='tourApprove')
router.register("vehicle-type", VehicleTypeView, basename='VehicleType')
router.register("office-name", OfficeNameView, basename=' OfficeName')
router.register("Transactions", TransactionView, basename='transaction')
router.register("transact", TransactView, basename='transact')
router.register("transactTablet", TransactTabletView, basename='transactTablet')
router.register("transactFilter", TransactFilterView, basename='transactFilter')
router.register("transactPage", TransactPageView, basename='transactPage')
router.register("transactHoflistePage", TransactPageHoflisteView, basename='transactHoflistePage')
router.register("ID", CombinationView, basename='Combination')
router.register("IDRead", CombinationReadView, basename='CombinationRead')
router.register("DetailCombView", DetailCombinationView,
                basename='DetailCombinationRead')
router.register("Forwarders", ForwarderView, basename='ForwarderView')
router.register("officenametiming", OfficeNameTimingView, basename='OfficeNameTiming')
router.register("SaveBase64", SaveImageView, basename='SaveImageView')
router.register("DriverSign", DriverSignView, basename='DriverSign')
router.register("Contract", ContractView, basename='Contract')
router.register("ContractPage", ContractPageView, basename='ContractPage')
router.register("hand_transmitter", HandTransmitterView, basename='HandTransmitterView')
router.register("printpdfdoc", PrintPdfDoc, basename='PrintPdfDoc')
router.register("yardticket", YardTicket, basename='YardTicket')
router.register("masteryardticket", MasterYardTicketView, basename='MasterYardTicketView')
router.register("driverid", DriverIDView, basename='DriverID')
router.register("barriers", BarriersView, basename='Barriers')
router.register("routeimage", RouteImageView, basename='RouteImageView')
router.register("notification", NotificationView, basename='NotificationView')
router.register("users", UserCreateAPIView, basename='UserCreateAPIView')
router.register("getPhoneNumberRegistered", getPhoneNumberRegistered, basename='getPhoneNumberRegistered')
router.register("sendmessage", SendMessage, basename='SendMessage')
router.register("displaytext", DisplayTexttView, basename='DisplayTexttView')
router.register("loading_box", LoadingBoxView, basename='LoadingBoxView')
router.register("max_vehicle", MaxVehicleView, basename='MaxVehicleView')
router.register("get_sample_yard_ticket", GetSampleYardTicketView, basename='GetSampleYardTicketView')
router.register("get_cmr", GetCMR, basename='GetCMR')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('yard.urls')),
    path('', include('container.urls')),
    path('yard/', include('django.contrib.auth.urls')),
    path('stats/', include('stats.urls')),
    path('', include('scale_app.urls')),
    path('api/', include(router.urls)),
    path('api/Pdf_print/', Pdf_print.as_view()),
    path('api/Pdf_woimg/', Pdf_woimg.as_view()),
    path('api/Pdf_sebald/', Pdf_sebald.as_view()),
    path('api/Pdf_lieferscheine_print/', Pdf_print_lieferscheine.as_view()),
    path('api/export_database/', dump_db_database.as_view()),
    path('api/export_media/', dump_media_files.as_view()),
    path('api/import_media/', import_media.as_view()),
    path('api/fire_alarm/', FireAlarmView.as_view()),
    path('api/remove_notifications/', RemoveAllNotification.as_view()),
    path('api/remove_tour_approves/', RemoveAllTourApprove.as_view()),
    path('api/import_database/', import_data.as_view()),
    path('api/Pdf_lieferscheine_print_internal/', Pdf_print_lieferscheine_internal.as_view()),
    path('api/Pdf_print_external/', Pdf_print_external.as_view()),
    path('camera_feed', camerafeed, name='camera_feed'),
    path('camerafeed_type2', camerafeed_type2, name='camerafeed_type2'),
    path('api/pdf_backend',BackendPrintQRCode.as_view()),
    # path('api-token-auth', obtain_auth_token, name='api_token_auth'),
    path('api-token-auth', CustomAuthToken.as_view(), name='api_token_auth'),
    path('auth/', include('rest_framework.urls')),
    path('api/tablet_users', users_without_auth),
    path('api/get_barrier_status', get_barrier_status),
]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

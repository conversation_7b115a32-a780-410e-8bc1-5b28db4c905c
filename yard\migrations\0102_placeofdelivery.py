# Generated by Django 3.1.1 on 2022-03-10 18:40

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0101_showtaraweight'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlaceOfDelivery',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('pin', models.CharField(blank=True, max_length=10, null=True)),
                ('place', models.CharField(blank=True, max_length=100, null=True)),
                ('addition1', models.CharField(blank=True, max_length=100, null=True)),
                ('addition2', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('addition3', models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('country', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('contact_person1_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person1_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('ss_role_access', models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ('name',),
            },
        ),
    ]

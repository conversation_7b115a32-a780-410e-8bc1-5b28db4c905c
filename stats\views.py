import io

from reportlab.lib.units import cm
from reportlab.lib.pagesizes import letter
import logging
import logging.handlers as handlers

from reportlab.lib.utils import ImageReader

# logging.basicConfig(
#     format='%(asctime)s,%(msecs)d %(levelname)-8s [%(pathname)s:%(lineno)d in  function %(funcName)s] %(message)s',
#     datefmt='%Y-%m-%d:%H:%M:%S',
#     level=logging.DEBUG,
#     filename="yardman.log"
# )

logHandler = handlers.RotatingFileHandler("yardman.log", maxBytes=9999999, backupCount=0)
logging.basicConfig(format='%(asctime)s,%(msecs)d %(levelname)-8s [%(pathname)s:%(lineno)d in  function %(funcName)s] %(message)s', handlers=[logHandler])
logger = logging.getLogger()
logger.setLevel(level=logging.DEBUG)

import shlex
import shutil
from django.contrib.humanize.templatetags.humanize import intcomma
import subprocess
import xml.etree.cElementTree as ET
import re
import base64
import zipfile
import decimal
# from cStringIO import StringIO
from django.core import management
from django.contrib import messages
from django.shortcuts import redirect
# from django.utils.translation import Trans
import xlwt
from email.encoders import encode_base64
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from django.conf import settings
from django.core.files.storage import default_storage
from django.db.models import Q, Sum, When, Exists
from django.shortcuts import (get_object_or_404, render, HttpResponseRedirect)
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt

from yard.render import Render

from yard.models import *
from yard.forms import *
from django.http import JsonResponse, HttpResponse, StreamingHttpResponse
from django.forms.models import model_to_dict
from django.core.serializers import serialize
import json
from datetime import datetime, date, timedelta
from yard.utils.field_statistics import field_func
from yard.utils.smtp_ops import send_mail
from yard.utils.view_handling import create_excel, ready_to_csv
from django.http import FileResponse, Http404
import os
import datetime
from datetime import datetime
from django.db import connection
import random
import csv

LOGO_DIR = "/var/www/html/pdfdemo"
PDF_DIR = "/var/www/html/pdfdemo"
PDF_BASE_NAME = "delivery_note"
PDF_LATEX = "pdflatex"


@login_required(redirect_field_name=None)
def std_evaluation1(request):
    context = {}
    absolute_url = request.build_absolute_uri('?')
    context["absolute_url"] = "http://" + request.get_host()

    if request.POST:
        note_type = request.POST.getlist('note_type')
        grouping = request.POST.get('grouping')
        fromdate = request.POST.get('fromdate')

        if fromdate:
            context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
            fromdate = fromdate + " 00:00:00"
        else:
            fromdate = str(date.today()) + " 00:00:00"

        todate = request.POST.get('todate')
        if todate:
            context['to'] = datetime.strptime(todate, "%Y-%m-%d")
            todate = todate + " 23:59:59"
        else:
            todate = str(date.today()) + " 23:59:59"

        article_from = request.POST.get('article_from')
        article_to = request.POST.get('article_to')
        print("Grouping:", grouping)
        obj = Transaction.objects.filter(created_date_time__range=(fromdate, todate))
        # obj = Transkation.objects.raw('SELECT *,SUM(net_weight) as ttl FROM yard_transkation WHERE created_date_time BETWEEN %s and %s GROUP BY(article_id)',[fromdate,todate])
        sum_kg = 0
        if obj:
            if grouping == 'article':
                # context["data"] = get_article_groupset(obj)
                context['date'] = datetime.now()
                context["data"] = obj
                context["art_list"] = obj.values_list("article", "article__description").distinct()
                art_sum = []
                context["art_list"] = [list(i) for i in context["art_list"]]
                for i in context["art_list"]:
                    i.append(sum(j.net_weight for j in obj if j.article.pk == i[0]))
                context["art_sum"] = art_sum
                return Render.render("stats/pdf/article_report.html", context)
            elif grouping == 'art-cus':
                print("article customer")
                context["data"] = obj
                context['date'] = datetime.now()
                context["art_list"] = obj.values_list("article", "article__description").distinct()
                context["cus_list"] = obj.values_list("customer", "customer__name").distinct()
                art_sum = []
                context["art_list"] = [list(i) for i in context["art_list"]]
                context["cus_list"] = [list(i) for i in context["cus_list"]]
                context["summ"] = []
                for i in context["art_list"]:
                    for k in context["cus_list"]:
                        temp = {}
                        for j in obj:
                            summ = 0
                            temp["article"] = i[0]
                            temp["customer"] = k[0]
                            # if j.article.pk==i[0] and j.customer.pk==k[0]:
                            # summ=summ+j.net_weight
                            temp["sum"] = sum(
                                j.net_weight for j in obj if j.article.pk == i[0] and j.customer.pk == k[0])
                        context["summ"].append(temp)
                return Render.render("stats/pdf/art_cus_report.html", context)
            elif grouping == 'cus-art':
                print("customer article")
                context["data"] = obj
                context['date'] = datetime.now()
                context["art_list"] = obj.values_list("article", "article__description").distinct()
                context["cus_list"] = obj.values_list("customer", "customer__name").distinct()
                context["art_list"] = [list(i) for i in context["art_list"]]
                context["cus_list"] = [list(i) for i in context["cus_list"]]
                context["summ"] = []
                for i in context["cus_list"]:
                    for k in context["art_list"]:
                        temp = {}
                        for j in obj:
                            summ = 0
                            temp["article"] = k[0]
                            temp["customer"] = i[0]
                            # if j.article.pk==i[0] and j.customer.pk==k[0]:
                            # summ=summ+j.net_weight
                            temp["sum"] = sum(
                                j.net_weight for j in obj if j.article.pk == k[0] and j.customer.pk == i[0])
                        context["summ"].append(temp)
                    # context["sum"].append(sum(j.net_weight for j in obj if j.article.pk==i[0] and j.customer.pk==k[0]))
                return Render.render("stats/pdf/cus_art_report.html", context)
                context["data"] = obj
        else:
            context['error'] = "No Transkations found!"
            return render(request, "stats/std_evaluation2.html", context)
    return render(request, "stats/std_evaluation2.html", context)


@login_required(redirect_field_name=None)
def daily_delivery_list(request):
    context = {}
    form = TransactionForm(request.POST or None)

    if request.POST:
        if 'print_button' in request.POST:
            context_transaction = transaction_update(request)
            return Render.render("yard/pdf_template.html", context_transaction)
        elif 'save_button' in request.POST:
            id = request.POST.get('id')
            obj = Transaction.objects.get(id=id)
            form = TransactionForm(request.POST, instance=obj)
            if form.is_valid():
                form.save()
            else:
                print("error", form.errors)
    fromdate = request.POST.get('fromdate')
    if fromdate is not None and fromdate != '':
        todate = fromdate + " 23:59:59.000000"
        fromdate = fromdate + " 00:00:00.000000"
    else:
        todate = str(date.today()) + " 23:59:59.000000"
        fromdate = str(date.today()) + " 00:00:00"

    context["form"] = form
    context['daily'] = True
    context["dataset"] = Transaction.objects.filter(updated_date_time__range=(fromdate, todate), trans_flag__in=[1, 2],
                                                    yard=request.user.yard)

    if request.POST.get("wiegescheinnummer") is not None and len(request.POST.get("wiegescheinnummer")) > 0:
        context["dataset"] = Transaction.objects.filter(id=int(request.POST.get("wiegescheinnummer")),
                                                        updated_date_time__range=(fromdate, todate),
                                                        trans_flag__in=[1, 2],
                                                        yard=request.user.yard).order_by('-updated_date_time')
        context["search"] = True

    if request.POST.get("external_weighing") is not None:
        context["dataset"] = Transaction.objects.filter(external_weighing=True,
                                                        updated_date_time__range=(fromdate, todate),
                                                        trans_flag__in=[1, 2],
                                                        yard=request.user.yard).order_by('-updated_date_time')
        context["search"] = True
    return render(request, "stats/deliverynotes2.html", context)


@login_required(redirect_field_name=None)
def send_delivery_note(request, trans_id):
    try:
        trans_obj = Transaction.objects.get(id=trans_id)
        cust_obj = trans_obj.customer
        settings = Settings.objects.first()
        if settings is None:
            return JsonResponse({"status": "Settings Not Found"}, status=404)
        if not settings.smtp_support:
            return JsonResponse({"status": "SMTP Credentials Not Found"}, status=404)
        smtp_obj = settings.smtp_creds
        if cust_obj is None:
            return JsonResponse({"status": "Customer Not Found"}, status=404)
        else:
            if cust_obj.contact_person1_email:
                message_text = f'''Sehr geehrte Damen und Herren,

anbei senden wir Ihnen Ihren Lieferschein.


Achtung: Diese Mail wurde automatisch erzeugt. Bei Fragen oder Anmerkungen wenden Sie sich bitte an Ihren Ansprechpartner.

Mit freundlichen Grüßen

Waagensoftware RW65'''
                pdf = Render.static_render("yard/pdf_template.html",
                                           trans_obj.get_context_transaction(request))

                message = MIMEMultipart()
                message['From'] = smtp_obj.sender_address
                message['To'] = cust_obj.contact_person1_email
                message['Subject'] = f"Lieferschein {trans_obj.id}"
                message.attach(MIMEText(message_text, 'plain'))
                pdf_attach = MIMEApplication(
                    pdf, _subtype="pdf", _encoder=encode_base64)
                pdf_attach.add_header(
                    'content-disposition', 'attachment', filename=f"Lfs-{trans_obj.id}.pdf")
                message.attach(pdf_attach)
                send_mail(smtp_obj, message.as_string(), {
                    cust_obj.contact_person1_email, })
                return JsonResponse({"status": "Email Sent"})
            else:
                return JsonResponse({"status": "Customer Does not have email"}, status=404)
    except Transaction.DoesNotExist as e:
        return JsonResponse({"status": "Transaction Not Found"}, status=404)


@login_required(redirect_field_name=None)
def deliverynotes(request):
    context = {}
    form = TransactionForm(request.POST or None)
    if request.POST:
        if 'print_button' in request.POST:
            context_transaction = transaction_update(request)
            delivery_template = DeliveryNoteTemplate.objects.all().last()
            print(delivery_template.option)
            if delivery_template is not None:
                if delivery_template.option == 2:
                    return Render.render("yard/pdf_template 2.html", context_transaction)
                elif delivery_template.option == 3:
                    return Render.render("yard/pdf_template 3.html", context_transaction)
                elif delivery_template.option == 4:
                    return Render.render("yard/pdf_template 4.html", context_transaction)
                elif delivery_template.option == 5:
                    return Render.render("yard/pdf_template_without_images.html", context_transaction)
                elif delivery_template.option == 6:
                    return Render.render("yard/pdf_template_sebald.html", context_transaction)
                elif delivery_template.option == 7:
                    return Render.render("yard/pdf_template_without_table_border.html", context_transaction)
                elif delivery_template.option == 8:
                    return Render.render("yard/pdf_template_fees.html", context_transaction)
                elif delivery_template.option == 9:
                    return Render.render("yard/pdf_template_volume.html", context_transaction)
                elif delivery_template.option == 10:
                    ident_data = Combination.objects.filter(ident=context_transaction["dataset"].combination_id)
                    for i in ident_data:
                        context_transaction["id_driver_name"] = i.short_name
                    return Render.render("yard/pdf_template_sebald_two.html", context_transaction)
                elif delivery_template.option == 11:
                    return Render.render("yard/pdf_template_rds.html", context_transaction)
                elif delivery_template.option == 12:
                    return Render.render("yard/pdf_template_vorlage.html", context_transaction)
                elif delivery_template.option == 13:
                    return Render.render("yard/pdf_template_container.html", context_transaction)
                elif delivery_template.option == 14:
                    response = HttpResponse(content_type='application/pdf')
                    response['Content-Disposition'] = 'inline; filename="Lieferscheine.pdf"'

                    buffer = canvas_pdf_generate(context_transaction["dataset"], context_transaction["sign"])
                    pdf = buffer.getvalue()
                    buffer.close()
                    response.write(pdf)

                    return response
                else:
                    return Render.render("yard/pdf_template.html", context_transaction)
            else:
                return Render.render("yard/pdf_template.html", context_transaction)
        elif 'date_selection' in request.POST:
            fromdate = request.POST.get('fromdate')
            if fromdate:
                context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
                fromdate = fromdate + " 00:00:00"
            else:
                fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

            todate = request.POST.get('todate')
            if todate:
                context['to'] = datetime.strptime(todate, "%Y-%m-%d")
                todate = todate + " 23:59:59"
            else:
                todate = str(date.today()) + " 23:59:59"

            context["form"] = form
            if request.POST.get("wiegescheinnummer") is not None and len(request.POST.get("wiegescheinnummer")) > 0:
                context["dataset"] = Transaction.objects.filter(id=int(request.POST.get("wiegescheinnummer")),
                                                                updated_date_time__range=(fromdate, todate),
                                                                trans_flag__in=[1, 2],
                                                                yard=request.user.yard).order_by('-updated_date_time')
            else:
                context["dataset"] = Transaction.objects.filter(updated_date_time__range=(fromdate, todate),
                                                                trans_flag__in=[1, 2],
                                                                yard=request.user.yard).order_by('-updated_date_time')

            if request.POST.get("external_weighing") is not None:
                context["dataset"] = Transaction.objects.filter(external_weighing=True,
                                                                updated_date_time__range=(fromdate, todate),
                                                                trans_flag__in=[1, 2],
                                                                yard=request.user.yard).order_by('-updated_date_time')
            return render(request, "stats/deliverynotes2.html", context)
        elif 'mail_data' in request.POST:
            fromdate = request.POST.get('fromdate')
            if fromdate:
                context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
                fromdate = fromdate + " 00:00:00"
            else:
                fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

            todate = request.POST.get('todate')
            if todate:
                context['to'] = datetime.strptime(todate, "%Y-%m-%d")
                todate = todate + " 23:59:59"
            else:
                todate = str(date.today()) + " 23:59:59"

            Data = Transaction.objects.filter(updated_date_time__range=(fromdate, todate), trans_flag__in=[1, 2],
                                              yard=request.user.yard).select_related(
                'vehicle', 'article', 'deduction',
                'customer', 'supplier', 'yard',
                'contract_number', 'driver'
            )

            if request.POST.get("external_weighing") is not None:
                Data = Transaction.objects.filter(external_weighing=True,
                                                  updated_date_time__range=(fromdate, todate),
                                                  trans_flag__in=[1, 2],
                                                  yard=request.user.yard).order_by('-updated_date_time')
            from_date = fromdate.split(" ")[0].split("-")
            from_date = from_date[2] + "." + from_date[1] + "." + from_date[0]
            to_date = todate.split(" ")[0].split("-")
            to_date = to_date[2] + "." + to_date[1] + "." + to_date[0]
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="Lieferschein.csv"'
            columns, rows = ready_to_csv(Data)
            writer = csv.writer(response, delimiter=';')
            response.write(u'\ufeff'.encode('utf8'))
            writer.writerow(columns)
            for row in rows:
                row[6] = format_currency(row[6])
                writer.writerow(row)
            settings = Settings.objects.first()
            smtp_obj = settings.smtp_creds
            message = MIMEMultipart()
            if smtp_obj is not None:
                message['From'] = smtp_obj.sender_address
                message['To'] = settings.company_email
                message['Subject'] = f'Auswertung'
                # att = MIMEText(response.content, 'base64', 'utf-8')
                # att["Content-Type"] = 'application/ms-excel'
                # att['Content-Disposition'] = 'attachment; filename ="%s"' % "test.xls"
                # message.attach(att)

                message_text = f'''Sehr geehrte Damen und Herren,

anbei senden wir Ihnen die Auswertung zwischen {from_date} und {to_date} zu.



Achtung: Diese Mail wurde automatisch erstellt. Bei Fragen oder Anmerkungen wenden Sie sich bitte an Ihren Ansprechpartner.

Mit freundlichen Grüßen

Waagensoftware RW65'''
                message.attach(MIMEText(message_text, 'plain'))
                xls_attach = MIMEApplication(response.content, _subtype="csv", _encoder=encode_base64)
                xls_attach.add_header('content-disposition', 'attachment', filename="Auswertung.csv")
                message.attach(xls_attach)
                try:
                    send_mail(smtp_obj, message.as_string(), {settings.company_email, })
                    return response
                except Exception as e:
                    messages.error(request, str(e))
            else:
                messages.error(request, "Bitte richten Sie E-Mail in den Einstellungen ein")
                # return HttpResponseRedirect('/stats/deliverynotes')

        elif 'export_data' in request.POST:
            fromdate = request.POST.get('fromdate')
            if fromdate:
                fromdate = fromdate + " 00:00:00"
            else:
                fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

            todate = request.POST.get('todate')
            if todate:
                todate = todate + " 23:59:59"
            else:
                todate = str(date.today()) + " 23:59:59"

            Data = Transaction.objects.filter(updated_date_time__range=(fromdate, todate), trans_flag__in=[1, 2],
                                              yard=request.user.yard)

            if request.POST.get("external_weighing") is not None:
                Data = Transaction.objects.filter(external_weighing=True,
                                                  updated_date_time__range=(fromdate, todate),
                                                  trans_flag__in=[1, 2],
                                                  yard=request.user.yard).order_by('-updated_date_time')

            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="Lieferschein.csv"'
            columns, rows = ready_to_csv(Data)
            writer = csv.writer(response, delimiter=';')
            response.write(u'\ufeff'.encode('utf8'))
            writer.writerow(columns)
            for row in rows:
                row[6] = format_currency(row[6])
                writer.writerow(row)
            return response

        elif "export_pdf" in request.POST:
            fromdate = request.POST.get('fromdate')
            if fromdate:
                fromdate = fromdate + " 00:00:00"
            else:
                fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

            todate = request.POST.get('todate')
            if todate:
                todate = todate + " 23:59:59"
            else:
                todate = str(date.today()) + " 23:59:59"

            absolute_url = 'http://' + request.get_host()
            context["absolute_url"] = absolute_url
            context['date'] = datetime.now()
            context['from'] = fromdate
            context['to'] = todate
            context['dataset'] = Transaction.objects.filter(created_date_time__range=(fromdate, todate),
                                                            trans_flag__in=[1, 2],
                                                            yard=request.user.yard)
            if request.POST.get("external_weighing") is not None:
                context["dataset"] = Transaction.objects.filter(external_weighing=True,
                                                                updated_date_time__range=(fromdate, todate),
                                                                trans_flag__in=[1, 2],
                                                                yard=request.user.yard).order_by('-updated_date_time')
            return Render.render("stats/pdf/delivery_notes.html", context)

        else:
            id = request.POST.get('id')
            if id:
                obj = Transaction.objects.get(id=id)
                obj.status = request.POST["status"]
                obj.save()
                data = request.POST
                data._mutable = True
                deduction = data.getlist('deduction')
                ded_price = data.getlist('deduction_price')
                ded_vat = data.getlist('deduction_vat')
                ded_weight = data.getlist('deduction_weight')
                for i in range(len(deduction)):
                    if deduction[i]:
                        art = Article.objects.get(id=deduction[i])
                        if ded_price[i]:
                            art.price1 = decimal.Decimal(ded_price[i])
                        if ded_vat[i]:
                            art.vat = float(ded_vat[i])
                        if ded_weight[i]:
                            art.deduct_weight = float(ded_weight[i])
                        art.save()

                if data.get('deduction_weight') is not None and data.get('deduction_weight') != '':
                    net_weight = float(data['net_weight']) - float(sum([float(i) for i in ded_weight]))
                    data['material_weight'] = int(net_weight)
                data['deduction_weight'] = decimal.Decimal(0.0)
                for i in ded_weight:
                    data['deduction_weight'] += decimal.Decimal(i) if i != '' else decimal.Decimal(0.0)
                data['deduction_item'] = deduction
                data["deduction"] = []
                print(data)
                form = TransactionForm(data, instance=obj)
                if form.is_valid():
                    form.save()
                    logger.info(
                        f"method: {request.method}  message: Transaction with id {obj.id} updated successfully \n data: {request.POST}")
                else:
                    logger.error(
                        f"method: {request.method} message: Transaction with id {obj.id} is not updated \n errors: {form.errors}")
                    print("error", form.errors)

    fromdate = request.POST.get('fromdate')
    if fromdate:
        context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
        fromdate = fromdate + " 00:00:00"
    else:
        fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

    todate = request.POST.get('todate')
    if todate:
        context['to'] = datetime.strptime(todate, "%Y-%m-%d")
        todate = todate + " 23:59:59"
    else:
        todate = str(date.today()) + " 23:59:59"

    context["form"] = form
    context['art'] = Article.objects.all()
    context['contracts'] = Contract.objects.all()
    context["dataset"] = Transaction.objects.filter(updated_date_time__range=(fromdate, todate), trans_flag__in=[1, 2],
                                                    yard=request.user.yard).order_by('-updated_date_time')
    return render(request, "stats/deliverynotes2.html", context)


def format_currency(dollars):
    if dollars is None:
        return ""
    dollars = round(float(dollars), 2)
    return ",".join(("%s%s" % (intcomma(int(dollars)), ("%0.2f" % dollars)[-3:])).split("."))


def get_delivery_notes_pdf(request, id):
    context_transaction = transaction_get(request, id)
    delivery_template = DeliveryNoteTemplate.objects.all().last()
    if delivery_template is not None:
        if delivery_template.option == 2:
            return Render.render("yard/pdf_template 2.html", context_transaction)
        elif delivery_template.option == 3:
            return Render.render("yard/pdf_template 3.html", context_transaction)
        elif delivery_template.option == 4:
            return Render.render("yard/pdf_template 4.html", context_transaction)
        elif delivery_template.option == 5:
            return Render.render("yard/pdf_template_without_images.html", context_transaction)
        elif delivery_template.option == 6:
            return Render.render("yard/pdf_template_sebald.html", context_transaction)
        elif delivery_template.option == 7:
            return Render.render("yard/pdf_template_without_table_border.html", context_transaction)
        elif delivery_template.option == 8:
            return Render.render("yard/pdf_template_fees.html", context_transaction)
        elif delivery_template.option == 9:
            return Render.render("yard/pdf_template_volume.html", context_transaction)
        elif delivery_template.option == 10:
            ident_data = Combination.objects.filter(ident=context_transaction["dataset"].combination_id)
            for i in ident_data:
                context_transaction["id_driver_name"] = i.short_name
            return Render.render("yard/pdf_template_sebald_two.html", context_transaction)
        elif delivery_template.option == 11:
            return Render.render("yard/pdf_template_rds.html", context_transaction)
        elif delivery_template.option == 12:
            return Render.render("yard/pdf_template_vorlage.html", context_transaction)
        elif delivery_template.option == 13:
            return Render.render("yard/pdf_template_container.html", context_transaction)
        elif delivery_template.option == 14:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = 'inline; filename="Lieferscheine.pdf"'

            buffer = canvas_pdf_generate(context_transaction["dataset"], context_transaction["sign"])
            pdf = buffer.getvalue()
            buffer.close()
            response.write(pdf)

            return response
        else:
            return Render.render("yard/pdf_template.html", context_transaction)
    else:
        return Render.render("yard/pdf_template.html", context_transaction)


def canvas_pdf_generate(data, sign):
    from reportlab.pdfgen import canvas
    buffer = io.BytesIO()
    can = canvas.Canvas(buffer, pagesize=letter)
    # drawMyRule(can)
    if data.customer is not None:
        can.drawString(45, 382, f"{str(data.customer.pin)} {str(data.customer.place)}")  # postal_code place
        can.drawString(45, 397, str(data.customer.street))  # street
        can.drawString(45, 412, str(data.customer.name1))  # customer name
        can.drawString(45, 427, "Kunde:")
    # can.drawString(400, 385, str(data.customer.debitor_number))  # deboitnummber

    if data.forwarders is not None:
        can.drawString(400, 382, f"{str(data.forwarders.pin)} {str(data.forwarders.place)}")  # postal_code place
        can.drawString(400, 397, str(data.forwarders.street))  # street
        can.drawString(400, 412, str(data.forwarders.name))  # forwarder name
        can.drawString(400, 427, "Spediteur:")

    can.drawString(135, 330, str(data.id))  # lefischine id
    time = data.secondw_date_time.strftime("%H:%M")
    can.drawString(421, 292, str(time))  # second weighing time
    date = data.secondw_date_time.strftime("%d.%m.%y")
    can.drawString(527, 292, str(date))  # second weighing date

    if data.supplier is not None:
        can.drawString(80, 230, f"{str(data.supplier.pin)} {str(data.supplier.place)}")  # postal_code place
        can.drawString(80, 245, str(data.supplier.street))  # street
        can.drawString(80, 260, str(data.supplier.supplier_name))  # suplier name

    can.drawString(420, 245, str(data.vehicle.license_plate))
    status = ""
    if data.status == 0:
        status = "Eingang"
    elif data.status == 1:
        status = "Ausgang"
    elif data.status == 2:
        status = "Fremdwiegung"

    can.drawString(420, 215, status)

    can.drawString(75, 200, f"{str(data.first_weight)} kg")  # First weight with unit(kg)
    can.drawString(20, 180, str(data.firstw_alibi_nr))  # first weight alibi nr.
    date_first_weight = data.firstw_date_time.strftime("%d.%m.%y")
    time_first_weight = data.firstw_date_time.strftime("%H:%M")
    can.drawString(55, 180, f"{date_first_weight} {time_first_weight}")  # first weight datetime with format(german)
    can.drawString(173, 200, f"{str(data.second_weight)} kg")  # Second weight with unit(kg)
    can.drawString(150, 180, str(data.secondw_alibi_nr))  # Second weight alibi nr.
    can.drawString(175, 180, f"{date} {time}")  # Second weight datetime

    can.drawString(270, 200, f"{str(data.net_weight)} kg")  # Net weight

    if data.comment is not None:
        can.drawString(80, 110, data.comment)  # Comment

    if data.article is not None:
        can.drawString(80, 156, data.article.name)  # Material weight
    # can.drawString(30, 140, str(data.article.id))  # Material number
    can.drawString(490, 156, "AE-Nummer:")  # No changes keep heading
    if data.supplier is not None:
        can.drawString(560, 156, str(data.supplier.project_number))  # Project number from supplier

    if sign is not None:
        logo = ImageReader(sign.signature.url)
        can.drawImage(logo, 55, 70)

    can.drawString(200, 70, "")
    can.drawString(430, 70, "")
    can.setPageSize((24.10 * cm, 20.3 * cm))
    can.showPage()
    can.save()

    return buffer


def get_delivery_notes(request):
    from django.db import connection
    print("Queries:", len(connection.queries))

    if "export_special_ev" in request.GET:
        customer = int(request.GET.get('customer'))
        vehicle = int(request.GET.get('vehicle'))
        supplier = int(request.GET.get('supplier'))
        article = int(request.GET.get('article'))
        fromdate = request.GET.get('fromdate')
        todate = request.GET.get('todate')
        if fromdate:
            fromdate = fromdate + " 00:00:00"
        else:
            fromdate = str(date.today()) + " 00:00:00"
        if todate:
            todate = todate + " 23:59:59"
        else:
            todate = str(date.today()) + " 23:59:59"
        try:
            if customer != 0 and vehicle != 0 and supplier != 0 and article != 0:
                transaction_ids = Transaction.objects.filter(
                    Q(customer=customer) & Q(vehicle=vehicle) & Q(supplier=supplier) & Q(article=article)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0 and vehicle != 0 and supplier != 0:
                transaction_ids = Transaction.objects.filter(
                    Q(customer=customer) & Q(vehicle=vehicle) & Q(supplier=supplier)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0 and supplier != 0 and article != 0:
                transaction_ids = Transaction.objects.filter(
                    Q(customer=customer) & Q(supplier=supplier) & Q(article=article)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0 and vehicle != 0 and article != 0:
                transaction_ids = Transaction.objects.filter(
                    Q(customer=customer) & Q(vehicle=vehicle) & Q(article=article)).filter(
                    created_date_time__range=(fromdate, todate))

            elif vehicle != 0 and supplier != 0 and article != 0:
                transaction_ids = Transaction.objects.filter(
                    Q(vehicle=vehicle) & Q(supplier=supplier) & Q(article=article)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0 and vehicle != 0:
                transaction_ids = Transaction.objects.filter(Q(customer=customer) & Q(vehicle=vehicle)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0 and supplier != 0:
                transaction_ids = Transaction.objects.filter(Q(customer=customer) & Q(supplier=supplier)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0 and article != 0:
                transaction_ids = Transaction.objects.filter(Q(customer=customer) & Q(article=article)).filter(
                    created_date_time__range=(fromdate, todate))

            elif vehicle != 0 and supplier != 0:
                transaction_ids = Transaction.objects.filter(Q(vehicle=vehicle) & Q(supplier=supplier)).filter(
                    created_date_time__range=(fromdate, todate))

            elif article != 0 and supplier != 0:
                transaction_ids = Transaction.objects.filter(Q(supplier=supplier) & Q(article=article)).filter(
                    created_date_time__range=(fromdate, todate))

            elif customer != 0:
                # obj = Transaction.objects.filter(Q(customer=customer) & Q(vehicle=None) & Q(supplier=None) & Q(article=None)).filter(created_date_time__range=(fromdate, todate))
                transaction_ids = Transaction.objects.filter(customer=customer).filter(
                    created_date_time__range=(fromdate, todate))

            elif vehicle != 0:
                # obj = Transaction.objects.filter(Q(customer=None) & Q(vehicle=vehicle) & Q(supplier=None) & Q(article=None)).filter(created_date_time__range=(fromdate, todate))
                transaction_ids = Transaction.objects.filter(vehicle=vehicle).filter(
                    created_date_time__range=(fromdate, todate))

            elif supplier != 0:
                # obj = Transaction.objects.filter(Q(customer=None) & Q(vehicle=None) & Q(supplier=supplier) & Q(article=None)).filter(created_date_time__range=(fromdate, todate))
                transaction_ids = Transaction.objects.filter(supplier=supplier).filter(
                    created_date_time__range=(fromdate, todate))

            elif article != 0:
                # obj = Transaction.objects.filter(Q(customer=None) & Q(vehicle=None) & Q(supplier=None) & Q(article=article)).filter(created_date_time__range=(fromdate, todate))
                transaction_ids = Transaction.objects.filter(article=article).filter(
                    created_date_time__range=(fromdate, todate))
            else:

                transaction_ids = Transaction.objects.filter(created_date_time__range=(fromdate, todate))
        except Exception as e:
            return JsonResponse({"status": 0})

    else:
        fromdate = request.GET.get('fromdate')
        if fromdate:
            fromdate = fromdate + " 00:00:00"
        else:
            fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

        todate = request.GET.get('todate')
        if todate:
            todate = todate + " 23:59:59"
        else:
            todate = str(date.today()) + " 23:59:59"

        transaction_ids = Transaction.objects.filter(updated_date_time__range=(fromdate, todate), trans_flag__in=[1, 2],
                                                     yard=request.user.yard).select_related(
            'customer', 'supplier', 'vehicle', 'container', 'yard', 'forwarders', 'article'
        )
        # import ipdb
        # ipdb.set_trace()
    print(transaction_ids)
    if transaction_ids != None:
        pdfs = []
        last_logo = Logo.objects.all().last()
        last_signature = Signature.objects.filter(
            user__name="").last()
        last_showt = ShowTonne.objects.all().last()
        last_io = Io.objects.all().last()

        for transaction_obj in transaction_ids:
            context = {}
            try:
                context['dataset'] = transaction_obj
            except Exception as e:
                print("error:", e)
            context["absolute_url"] = "http://" + request.get_host()
            context['logo'] = last_logo
            context['user_name'] = ""
            context['sign'] = last_signature
            context['customer'] = 'Erzeuger'
            context['article'] = 'Frucht'
            context['show_price'] = 'false'
            context['show_container'] = 'false'
            context['showt'] = last_showt
            context['io'] = last_io
            context['tara_date'] = '1'
            context['driver_sign'] = DriverSignature.objects.filter(
                transaction_id=transaction_obj.id).last()
            context['images'] = context['dataset'].images_base64_set.first()

            response = Render.render("yard/api_pdf_template.html", context)
            stream = base64.b64encode(response._container[0])
            base_64_data = json.dumps(stream.decode('utf-8'))
            lfs_number = "0"
            if transaction_obj.lfd_nr is not None and transaction_obj.lfd_nr != "":
                lfs_number = transaction_obj.lfd_nr
            else:
                lfs_number = transaction_obj.id
            pdfs.append({"id": lfs_number, "pdf": str(base_64_data)})

        return JsonResponse(pdfs, safe=False)
    else:
        return JsonResponse({"status": 0})


# API for loading details from ajax to editform
glb_trans_data = ''


@login_required(redirect_field_name=None)
def deliverynote_detail(request, identifier):
    try:
        obj = Transaction.objects.get(id=identifier)
    except:
        obj = None
    if obj:
        data = model_to_dict(obj)
        if isinstance(data['deduction'], list):
            arts = []
            for i in data['deduction']:
                art = Article.objects.get(id=int(i.id))
                dic = {
                    "id": i.id,
                    'name': i.name,
                    "weight": art.deduct_weight,
                    "price": art.price1,
                    "vat": art.vat
                }
                arts.append(dic)
            data['deduction'] = arts
        global glb_trans_data
        glb_trans_data = data
    else:
        data = {}
    return JsonResponse(data)


# @login_required(redirect_field_name=None)
def view_images_base64(request, identifier):
    try:
        obj = images_base64.objects.get(transaction_id=identifier)
    except:
        obj = None
    if obj:
        serialized_obj = serialize('json', [obj, ])
        data = json.loads(serialized_obj)[0]['fields']
        return JsonResponse(data)
    else:
        return JsonResponse({'status': False, 'msg': 'No Images'})


def transaction_get(request, id):
    context = {}
    # absolute_url = request.build_absolute_uri('?')
    absolute_url = 'http://' + request.get_host()
    context["absolute_url"] = absolute_url
    heading = []
    context["header_image"] = Logo.objects.all()
    if Logo.objects.all().last() is not None:
        if Logo.objects.all().last().heading is not None:
            heading = Logo.objects.all().last().heading.split("|")
    context['logo'] = heading

    context['role'] = request.user.role
    context['user_name'] = request.user.name
    context['sign'] = Signature.objects.filter(user=request.user).last()
    context['customer'] = request.session['customer'] if request.session.get('customer') is not None else 'Kunde'
    context['article'] = request.session['article'] if request.session.get('article') is not None else 'Artikel'
    context['showt'] = ShowTonne.objects.all().last()
    context['io'] = Io.objects.all().last()
    obj = Transaction.objects.get(id=id)
    context["images"] = obj.images_base64_set.first()
    context['driver_sign'] = DriverSignature.objects.filter(transaction_id=id).last()
    context["dataset"] = obj
    price_total = 0
    if obj.article is not None:
        if obj.article.price1 is not None:
            context['show_price'] = 'true'
            price_total = (float(obj.article.price1) * float(obj.net_weight))

        context["price_total"] = price_total
        default_unit = 0
        default_vat = 0.0
        if obj.article.unit is not None:
            context["article_unit"] = int(obj.article.unit)

        if obj.article.unit is not None:
            default_unit = int(obj.article.unit)
            context['price_unit'] = default_unit

        if obj.article.vat is not None:
            default_vat = obj.article.vat

        if int(default_unit) == 0:
            context["price_total"] = price_total
            context['tax'] = (((price_total * float(default_vat)) / 100))
            # ShowTonne.objects.all().last()
            context['price_after_tax'] = (((price_total * float(
                default_vat)) / 100 + price_total))
        else:
            context["price_total"] = ((price_total) / 1000)
            context['tax'] = ((((price_total * float(default_vat)) / 100)) / 1000)
            # ShowTonne.objects.all().last()
            context['price_after_tax'] = ((((price_total * float(
                default_vat)) / 100 + price_total)) / 1000)
    if obj.article is None:
        context["volume"] = 0.0
    else:
        if obj.article.density is None:
            context["volume"] = 0.0
        else:
            context["volume"] = (obj.net_weight / int(obj.article.density))
    return context


@login_required(redirect_field_name=None)
def transaction_update(request):
    context = {}
    # absolute_url = request.build_absolute_uri('?')
    absolute_url = 'http://' + request.get_host()
    context["absolute_url"] = absolute_url
    heading = []
    if Logo.objects.all().last() is not None:
        if Logo.objects.all().last().heading is not None:
            heading = Logo.objects.all().last().heading.split("|")
    context['logo'] = heading
    context["header_image"] = Logo.objects.all()
    context['role'] = request.user.role
    context['user_name'] = request.user.name
    context['sign'] = Signature.objects.filter(user=request.user).last()
    context['customer'] = request.session['customer'] if request.session.get('customer') is not None else 'Kunde'
    context['article'] = request.session['article'] if request.session.get('article') is not None else 'Artikel'
    context['showt'] = ShowTonne.objects.all().last()
    context['io'] = Io.objects.all().last()

    if request.POST:

        id = request.POST.get('id')
        try:
            obj = Transaction.objects.get(id=id)
            price_total = 0
            if obj.article is not None:
                if obj.article.price1 is not None:
                    context['show_price'] = 'true'
                    price_total = (float(obj.article.price1) * float(obj.net_weight))

                context["price_total"] = price_total
                default_unit = 0
                default_vat = 0.0
                if obj.article.unit is not None:
                    context["article_unit"] = int(obj.article.unit)

                if obj.article.unit is not None:
                    default_unit = int(obj.article.unit)
                    context['price_unit'] = default_unit

                if obj.article.vat is not None:
                    default_vat = obj.article.vat

                if int(default_unit) == 0:
                    context["price_total"] = price_total
                    context['tax'] = (((price_total * float(default_vat)) / 100))
                    # ShowTonne.objects.all().last()
                    context['price_after_tax'] = (((price_total * float(
                        default_vat)) / 100 + price_total))
                else:
                    context["price_total"] = ((price_total) / 1000)
                    context['tax'] = ((((price_total * float(default_vat)) / 100)) / 1000)
                    # ShowTonne.objects.all().last()
                    context['price_after_tax'] = ((((price_total * float(
                        default_vat)) / 100 + price_total)) / 1000)
            form = TransactionForm(request.POST, instance=obj)
            context["images"] = obj.images_base64_set.first()
            context['driver_sign'] = DriverSignature.objects.filter(transaction_id=id).last()
            if form.is_valid():
                obj = form.save()
                context["dataset"] = obj
                context["images"] = obj.images_base64_set.first()
            else:
                print("error", form.errors)
        except:
            obj = None
    return context


@login_required(redirect_field_name=None)
def std_evaluation(request):
    context = {}
    absolute_url = request.build_absolute_uri('?')
    context["absolute_url"] = "http://" + request.get_host()

    if request.POST:
        note_type = request.POST.getlist('note_type')
        stat_type = request.POST.get('stat_type')
        fromdate = request.POST.get('fromdate')
        todate = request.POST.get('todate')

        if fromdate:
            context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
            fromdate = fromdate + " 00:00:00"
        else:
            fromdate = str(date.today()) + " 00:00:00"

        if todate:
            context['to'] = datetime.strptime(todate, "%Y-%m-%d")
            todate = todate + " 23:59:59"
        else:
            todate = str(date.today()) + " 23:59:59"
        obj = field_func[stat_type]((fromdate, todate))
        # obj = Transkation.objects.raw('SELECT *,SUM(net_weight) as ttl FROM yard_transkation WHERE created_date_time BETWEEN %s and %s GROUP BY(article_id)',[fromdate,todate])
        if obj:
            if stat_type == 'material' or 'vehicle' or 'supplier' or 'customer':
                # context["data"] = get_article_groupset(obj)
                context['stat_type'] = stat_type
                context['date'] = datetime.now()
                context["data"] = obj
                context["summ"] = sum(
                    j.net_weight if j.net_weight is not None else decimal.Decimal('0') for j in obj["transactions"])
                context['head_m'] = request.session['article'] if request.session.get(
                    'article') is not None else 'Artikel'
                context['head_c'] = request.session['customer'] if request.session.get(
                    'customer') is not None else 'Kunde'
                context['vehicle'] = 'Fahrzeug'
                context['head_s'] = request.session['supplier'] if request.session.get(
                    'supplier') is not None else 'Lieferant'
                context['article'] = request.session['article'] if request.session.get(
                    'article') is not None else 'Artikel'
                return Render.render("stats/pdf/material_stat.html", context)
        else:
            context['error'] = "No Transkations found!"
    # 	article_from = request.POST.get('article_from')
    # 	article_to = request.POST.get('article_to')
    # 	obj = Transaction.objects.filter(created_date_time__range=(fromdate,todate))
    return render(request, "stats/standard_evaluation2.html", context)


@login_required(redirect_field_name=None)
def daily_closing(request):
    context = {}
    absolute_url = request.build_absolute_uri('?')
    context["absolute_url"] = "http://" + request.get_host()
    fromdate = str(date.today()) + " 00:00:00"
    todate = str(date.today()) + " 23:59:59"
    trans = Transaction.objects.filter(trans_flag=1, yard=request.user.yard)
    # trans = Transaction.objects.filter(created_date_time__range=(fromdate,todate),trans_flag=1)
    if len(trans) > 0:
        trans.update(trans_flag=2)
        message = str(len(trans)) + " Transcations Updated"
    else:
        message = "No transcations found!"
    return JsonResponse({'status': True, 'msg': message})


# @login_required(redirect_field_name=None)
# def all_closing(request):
#     context = {}
#     absolute_url = request.build_absolute_uri('?')
#     context["absolute_url"] = "http://" + request.get_host()
#     fromdate = str(date.today()) + " 00:00:00"
#     todate = str(date.today()) + " 23:59:59"
#     trans = Transaction.objects.filter(trans_flag=1, yard=request.user.yard)
#     # trans = Transaction.objects.filter(created_date_time__range=(fromdate,todate),trans_flag=1)
#     if len(trans) > 0:
#         trans.update(trans_flag=2)
#         message = str(len(trans)) + " Transcations Updated"
#     else:
#         message = "No transcations found!"
#     return JsonResponse({'status': True, 'msg': message})


@login_required(redirect_field_name=None)
def site_list(request):
    context = {}

    if request.method == "POST":
        try:
            transaction = Transaction.objects.get(id=request.POST.get("id"))
            form = TransactionForm(request.POST, instance=transaction)
            print(form.is_valid())
            print(form.errors)
            if form.is_valid():
                form.save()
                logger.info(
                    f"method: {request.method}  message: Transaction with id {transaction.id} updated successfully \n data: {request.POST}")
            else:
                logger.error(
                    f"method: {request.method} message: Transaction with id {transaction.id} is not updated \n errors: {form.errors}")
        except:
            pass
    queryObj = Transaction.objects.filter(trans_flag=0, yard=request.user.yard)
    form = TransactionForm(request.POST or None)
    context["form"] = form

    direction = request.POST.get("material-direction")
    if direction != None and direction != "":
        direction = request.POST.get("material-direction")
        queryObj = Transaction.objects.filter(trans_flag=0, yard=request.user.yard, status=direction)

    context["dataset"] = queryObj
    if request.method == "POST":
        print(request.POST)
        if request.POST.get("external_weighing") is not None:
            context["dataset"] = Transaction.objects.filter(external_weighing=True, trans_flag=0,
                                                            yard=request.user.yard)

    return render(request, "stats/site_list.html", context)


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def site_list_delete(request, identifier):
    context = {}
    obj = get_object_or_404(Transaction, id=identifier)
    object_id = obj.id
    if obj.delete():
        logger.info(f"method: {request.method} message: Transaction with id {object_id} destroyed successfully.")
    else:
        logger.error(f"method: {request.method} message: Transaction with id {object_id} is not destroyed.")
    return HttpResponseRedirect("/stats/site_list")


def deliverynote_delete(request, identifier):
    obj = get_object_or_404(Transaction, id=identifier)
    object_id = obj.id
    if obj.delete():
        logger.info(f"method: {request.method} message: Transaction with id {object_id} destroyed successfully.")
    else:
        logger.error(f"method: {request.method} message: Transaction with id {object_id} is not destroyed.")
    return HttpResponseRedirect("/stats/deliverynotes")


@login_required(redirect_field_name=None)
def deliverynotes1(request):
    if 'print_button' in request.POST:
        return pdf_view(request)
    context = {}
    form = TransactionForm(request.POST or None)
    context["form"] = form
    context["dataset"] = Transaction.objects.all().order_by('-updated_date_time')
    return render(request, 'stats/deliverynotes2.html', context)


def pdf_view(request):
    def R(str):
        if str != None:
            str = str.replace('\\', '\\')
            str = str.replace('$', '\$')
            str = str.replace("`", '``')
            str = str.replace("´", "''")
            str = str.replace('"', "''")
            str = str.replace('<', '$<$')
            str = str.replace('>', '$>$')
            str = str.replace('_', '\\_')
            str = str.replace('#', '\\#')
            str = str.replace('{', '\\{')
            str = str.replace('}', '\\}')
            str = str.replace('^', '\\textasciicircum{}')
            str = str.replace("°", '$^{\\circ}$')
            str = str.replace('€', '\\euro{}')
            str = str.replace('&', '\\&')
            str = str.replace('%', '\\%')
            str = str.replace('Ω', '$\\Omega$')
            str = str.replace('½', '\\sfrac{1}{2}')
            str = str.replace('¾', '\\sfrac{3}{4}')
            str = str.replace('¼', '\\sfrac{1}{4}')
        return str

    def create_sheet(kunde_name, lieferanten_name, artikel_name, kennzeichen, zufahrt_art, weight_info, with_time_var):
        global LOGO_DIR, PDF_LATEX
        global PDF_DIR, PDF_BASE_NAME
        kunden_adresse = R(kunde_name) + ","
        fahrzeugnummer = R(kennzeichen)
        op_ok = True
        op_name = R("my_name")
        op_strasse = R("my_stgreet")
        op_bezeichnung = R("my_description")
        op_plz = R("my_zip")
        op_ort = R("my_town")
        lfd_nr = 1
        the_id = weight_info[0]
        the_weight = int(weight_info[1])
        the_date = weight_info[2]
        if with_time_var:
            the_time = weight_info[3]
        else:
            the_time = ""
        lieferant = "Ab-/Beladestelle\nLieferant:"
        tex_text = "\\documentclass[12pt,oneside,a4paper]{article}\n"
        tex_text += "\\usepackage[utf8]{inputenc}\n"
        tex_text += "\\usepackage[german]{babel}\n"
        tex_text += "\\usepackage{color}\n"
        tex_text += "\\usepackage{eurosym}\n"
        # tex_text += "\\usepackage{xfrac}\n"
        tex_text += "\\pagestyle{empty}\n"
        tex_text += "\\usepackage{graphicx}\n"
        tex_text += "\\usepackage{hyperref}\n"
        tex_text += "\\usepackage[a4paper,left=2cm,right=16mm,top=1cm,bottom=1cm]{geometry}\n"
        tex_text += "\\setlength{\\parindent}{0pt}\n"
        tex_text += "\\begin{document}\n"
        tex_text += "\\begin{sf}\n"
        tex_text += "\\vspace*{4cm}\n"
        tex_text += "\\begin{tabular}{|c|c|c|c|c|c|}\\hline\n"
        tex_text += "\\multicolumn{3}{|p{9cm}|}{\n"
        tex_text += "\\parbox[t]{7cm}{\n"
        tex_text += "\\textbf{\\tiny{" + op_name + "}} \n"
        tex_text += "\\tiny{" + op_bezeichnung + "} "
        tex_text += "\\tiny{" + op_strasse + "}\\\\\n"
        tex_text += "\\tiny{" + op_plz + "} "
        tex_text += "\\tiny{" + op_ort + "}}\n"
        tex_text += "\\parbox[t]{5cm}{\n"
        tex_text += "\\vspace*{5mm}\n"
        tex_text += "\\textbf{\\large %s:}\\\\\n" % ("Kunde")
        if len(kunde_name) > 0:
            print('glb_trans_data', glb_trans_data)
            customer_id = glb_trans_data['customer']
            # cursor = connection.cursor()
            # conn = sqlite3.connect(OPERATOR_DATABASE)
            c = connection.cursor()
            sql_command = "SELECT name, company, street, perm_pin, perm_place FROM yard_customer WHERE id = '%s'" % (
                customer_id)
            #   sql_command = "SELECT vorname, bezeichnung, strasse, PLZ, ort FROM kunden WHERE name = '%s'"%(kunde_name)
            c.execute(sql_command)
            result = c.fetchall()
            connection.close()
            # result = [["Hans", "firma1", "xstreet", "98299", "Boston"]]
            if len(result) > 0:
                tex_text += R(result[0][0]) + " " + R(kunde_name) + "\\\\\n"
                if result[0][1] == None:
                    tex_text += R(str(result[0][1])) + "\\\\\n"
                    kunden_adresse += R(str(result[0][1])) + ","
                else:
                    tex_text += R(result[0][1]) + "\\\\\n"
                    kunden_adresse += R(result[0][1]) + ","
                if result[0][3] == None:
                    tex_text += R(str(result[0][3])) + "\\\\\n"
                    kunden_adresse += R(str(result[0][3])) + ","
                else:
                    tex_text += R(result[0][3]) + "\\\\\n"
                    kunden_adresse += R(result[0][3]) + ","
                if result[0][4] == None:
                    tex_text += R(str(result[0][4])) + "\n"
                    kunden_adresse += R(str(result[0][4])) + "."
                else:
                    tex_text += R(result[0][4]) + "\n"
                    kunden_adresse += R(result[0][4]) + "."
            else:
                tex_text += R(kunde_name) + "\n"
        else:
            tex_text += "\n"
        tex_text += "\\vspace*{5mm}\n"
        tex_text += "}}\n"
        tex_text += "&\\multicolumn{3}{|p{7cm}|}{\n"
        tex_text += "\\vspace*{2mm}\\raisebox{-90pt}{\\includegraphics[width=170pt]{" + LOGO_DIR + "/operator_logo.png}}\n"
        tex_text += "}\\\\ \\hline \n"
        tex_text += "&lfd. Nr.&Datum&Uhrzeit&Werk&Zufuhrart\\\\ \n"
        tex_text += "\parbox{4cm}{"
        tex_text += "\Large Wiegeschein\\\\Lieferschein\\\n\\vspace{2mm}}&%d&%s&%s&&%s\\\\ \\hline\n" % (
            lfd_nr, the_date, the_time, zufahrt_art)
        tex_text += "\\multicolumn{3}{|l|}{%s}&\multicolumn{3}{|l|}{Fahrzeug-Nr.}\\\\ \n" % (lieferant)
        tex_text += "\\multicolumn{3}{|l|}{\n"
        tex_text += "\\parbox{5cm}{\n"
        if len(lieferanten_name) > 0:
            print('glb_trans_data', glb_trans_data)
            supplier_id = glb_trans_data['supplier']
            # cursor = connection.cursor()
            # conn = sqlite3.connect(OPERATOR_DATABASE)
            c = connection.cursor()
            sql_command = "SELECT street, pin, place FROM yard_supplier WHERE id = '%s'" % (supplier_id)
            #        sql_command = "SELECT strasse, plz, ort FROM lieferanten WHERE lieferanten_name = '%s'"%(lieferanten_name)
            c.execute(sql_command)
            result = c.fetchall()
            connection.close()
            # result = [["ystreet", "98932", "berlin"]]
            if len(result) > 0:
                tex_text += "\\vspace{1cm}\n\n"
                tex_text += R(lieferanten_name) + "\\\\\n"
                tex_text += R(result[0][0]) + "\\\\\n"
                tex_text += R(result[0][1]) + " "
                tex_text += R(result[0][2]) + "\\\\\n\n"
            else:
                tex_text += R(lieferanten_name) + "\n"
        tex_text += "}}&\\multicolumn{3}{|l|}{\n"
        tex_text += "\\parbox{5cm}{\n"
        tara = 0
        the_tara_id = "?"
        the_tara_date = "??"
        the_tara_time = "??"
        wstate = ""
        if len(kennzeichen) > 0:
            tex_text += R(kennzeichen) + "\\\\\n\n"
            tara_in_court = False
            print('glb_trans_data', glb_trans_data)
            transaction_id = glb_trans_data['id']
            # cursor = connection.cursor()
            # conn = sqlite3.connect(OPERATOR_DATABASE)
            c = connection.cursor()
            sql_command = "SELECT first_weight, firstw_date_time, firstw_alibi_nr FROM yard_transaction WHERE id = '%s'" % (
                transaction_id)
            # sql_command = "SELECT vehicle_weight, created_date_time, vehicle_weight_id FROM yard_vehicle WHERE id = '%s'"%(vehicle_id)
            # sql_command = "SELECT tara, tara_date, tara_time, tara_id FROM yardlist WHERE kennung = '%s'"%(kennzeichen)
            c.execute(sql_command)
            result = c.fetchall()
            connection.close()
            print('result', result)
            # result = [[1234, "11.02.2021", "11:00", "90"]]
            if len(result) > 0:
                tara_in_court = True
                tara = result[0][0]
                print('tara', tara)
                tara_date = (result[0][1]).date()
                print('tara_date', tara_date)
                the_tara_date = datetime.strftime(tara_date, '%d.%m.%Y')
                print('the_tara_date', the_tara_date)
                the_tara_time = datetime.strftime(result[0][1], "%H:%M")
                print('the_tara_time', the_tara_time)
                the_tara_id = result[0][2]
                if the_tara_date == None:
                    the_tara_date = '???'
                    tara_in_court = False
                if the_tara_time == None:
                    the_tara_time = '???'
                    tara_in_court = False
                if the_tara_id == None:
                    the_tara_id = '?'
                    tara_in_court = False
        tex_text += "\\vspace*{5mm}\n"
        tex_text += "}}\\\\ \\hline\n"
        tex_text += "\\end{tabular}\n\n"
        tex_text += "\\vspace*{2cm}\n"
        diff = the_weight - tara
        tara_s = str(tara)
        kosten = 0.0
        weight_s = ("%d" % (the_weight)).replace('.', ',')
        if diff >= 0:
            diff_s = ("%d" % (diff)).replace('.', ',')
        else:
            diff_s = ("%d" % (-diff)).replace('.', ',')
        tex_text += "\\begin{tabular}{|p{24mm}p{25mm}p{15mm}p{3mm}rp{1cm}p{40mm}p{14mm}|} \\hline \n"
        tex_text += "&Datum&Uhrzeit&&Gewicht&&%s&Alibi-Nr.\\\\ \\hline \n" % ("Material")
        if diff >= 0:
            tex_text += "Erstwiegung&%s&%s&%s&%s&kg&&%s\\\\ \n" % (
                the_tara_date, the_tara_time, wstate, tara_s, the_tara_id)
            tex_text += "Zweitwiegung&%s&%s&&%s&kg&&%s\\\\ \n" % (the_date, the_time, weight_s, the_id)
        else:
            tex_text += "Zweitwiegung&%s&%s&&%s&kg&&%s\\\\ \n" % (the_date, the_time, weight_s, the_id)
            tex_text += "Erstwiegung&%s&%s&%s&%s&kg&&%s\\\\ \n" % (
                the_tara_date, the_tara_time, wstate, tara_s, the_tara_id)
        tex_text += "Nettogewicht&&&E&%s&kg&%s&\\\\ \\hline \n" % (diff_s, R(artikel_name))
        tex_text += "\\end{tabular}"
        tex_text += "\\vfill\n"
        tex_text += "\\begin{tabular}{|p{53mm}|p{53mm}|p{53mm}|} \\hline\n"
        tex_text += "Unterschrift des Wägers&Unterschrift des Fahrers&Unterschrift des Empfängers\\\\ \hline\n"
        tex_text += "\\rule{0pt}{15mm}&\\rule{0pt}{15mm}&\\rule{0pt}{15mm}\\\\ \\hline\n"
        tex_text += "\\end{tabular}\n\n"
        tex_text += "\\footnotesize{E: errechnet, PT: Preset Tara (voreingegebens Tara)}\\\\\n"
        tex_text += "\\footnotesize{Messwerte aus frei programmierbarer Zusatzeinrichung. Die geeichten Messwerte können eingesehen werden.}\\\\\n"
        tex_text += "\\footnotesize{Für Überladungen haftet der Fahrzeuglenker.}\n"
        tex_text += "\\end{sf}\n"

        tex_text += "\\end{document}\n"
        tex_path = PDF_DIR + "/" + PDF_BASE_NAME + ".tex"
        tfile = open(tex_path, "wb")
        tfile.write(tex_text.encode('utf-8'))
        tfile.close()
        os.system(PDF_LATEX + " -output-directory=" + PDF_DIR + " " + tex_path)

    print('glb_trans_data', glb_trans_data)
    transaction_id = glb_trans_data['id']
    c = connection.cursor()
    sql_command = "SELECT  second_weight, secondw_date_time, secondw_alibi_nr, lfd_nr FROM yard_transaction WHERE id = '%s'" % (
        transaction_id)
    # sql_command = "SELECT tara, tara_date, tara_time, tara_id FROM yardlist WHERE kennung = '%s'"%(kennzeichen)
    c.execute(sql_command)
    result_2 = c.fetchall()
    connection.close()
    print('result_2', result_2)
    # result = [[1234, "11.02.2021", "11:00", "90"]]
    if len(result_2) > 0:
        tara_in_court = True
        tara_2 = result_2[0][0]
        print('tara_2', tara_2)
        tara_date_2 = (result_2[0][1]).date()
        print('tara_date_2', tara_date_2)
        the_tara_date_2 = datetime.strftime(tara_date_2, '%d.%m.%Y')
        print('the_tara_date_2', the_tara_date_2)
        the_tara_time_2 = datetime.strftime(result_2[0][1], "%H:%M")
        print('the_tara_time_2', the_tara_time_2)
        the_tara_id_2 = result_2[0][2]
        lfd_nr = result_2[0][3]

        if the_tara_date_2 == None:
            the_tara_date_2 = '???'
            tara_in_court = False
        if the_tara_time_2 == None:
            the_tara_time_2 = '???'
            tara_in_court = False
        if the_tara_id_2 == None:
            the_tara_id_2 = '?'
            tara_in_court = False

    customer_id = glb_trans_data['customer']
    mycustomer = str(Customer.objects.get(id=customer_id))
    print('mycustomer', mycustomer)

    supplier_id = glb_trans_data['supplier']
    my_supplier = str(Supplier.objects.get(id=supplier_id))
    print('my_supplier', my_supplier)

    article_id = glb_trans_data['article']
    my_article = str(Article.objects.get(id=article_id))
    print('my_article', my_article)

    vehicle_id = glb_trans_data['vehicle']
    my_vehicle = str(Vehicle.objects.get(id=vehicle_id))
    print('my_vehicle', my_vehicle)

    create_sheet(mycustomer, my_supplier, my_article, my_vehicle, lfd_nr,
                 [the_tara_id_2, tara_2, the_tara_date_2, the_tara_time_2], True)
    return FileResponse(open('/var/www/html/pdfdemo/delivery_note.pdf', 'rb'), content_type='application/pdf')


@login_required()
def dump_db_data(request):
    apps = [
        "yard",
        "stats",
        'scale_app',
    ]
    static_dir = os.path.join(os.getcwd(), "stats", "static")
    try:
        os.mkdir(os.path.join(static_dir, "dump"))
    except:
        print('*******************')
        check_path = static_dir + '\dump'
        ex = os.path.isdir(check_path)
        if ex:
            pass
        else:
            os.makedirs(os.path.join(static_dir, "dump"), exist_ok=True)
        print('*******************')

    base_dir = os.path.join(static_dir, "dump")
    for app_name in apps:
        output_file = os.path.join(base_dir, f"{app_name}.json")
        file = open(output_file, 'w', encoding="utf-8")
        management.call_command('dumpdata', app_name, format='json', indent=3, stdout=file)
    zip_path = os.path.join(static_dir, "dump.zip")
    zipf = zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED)
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            zipf.write(os.path.join(base_dir, file),
                       os.path.relpath(os.path.join(root, file),
                                       os.path.join(base_dir)))
    zipf.close()

    zip_file = open(zip_path, 'rb')
    response = FileResponse(zip_file)

    return response


def dump_media_data(request):
    static_dir = os.path.join(os.getcwd(), "media")

    base_dir = os.path.join(static_dir, "drivers_sign")
    base_dir_images = os.path.join(static_dir, "trans_images")
    base_dir_logo = os.path.join(static_dir, "logo")
    base_dir_signatures = os.path.join(static_dir, "signatures")
    zip_path = os.path.join(static_dir, "media_dump.zip")
    zipf = zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED)

    for root, dirs, files in os.walk(base_dir):
        for file in files:
            zipf.write(os.path.join(base_dir, file), os.path.basename(os.path.normpath("drivers_sign")) + "\\" + file)

    for root, dirs, files in os.walk(base_dir_images):
        for file in files:
            zipf.write(os.path.join(base_dir_images, file),
                       os.path.basename(os.path.normpath("trans_images")) + "\\" + file)

    for root, dirs, files in os.walk(base_dir_logo):
        for file in files:
            zipf.write(os.path.join(base_dir_logo, file), os.path.basename(os.path.normpath("Logo")) + "\\" + file)

    for root, dirs, files in os.walk(base_dir_signatures):
        for file in files:
            zipf.write(os.path.join(base_dir_signatures, file),
                       os.path.basename(os.path.normpath("signatures")) + "\\" + file)

    import base64
    with open(zip_path, "rb") as f:
        bytes = f.read()
        encoded = base64.b64encode(bytes)

    zipf.close()
    zip_file = open(zip_path, 'rb')
    response = FileResponse(zip_file)

    return response


@csrf_exempt
def import_db(request):
    apps = [
        "yard",
        "stats",
        'scale_app',
    ]
    if request.method == 'POST':
        file = request.FILES["dump_data"]
        file_name = default_storage.save(file.name, file)
        # print(file_name)
        base_dir = os.path.join(os.getcwd(), "media")
        target_dir = os.path.join(base_dir, "dump")
        zip_path = os.path.join(base_dir, file.name)
        with zipfile.ZipFile(os.path.join(base_dir, file_name), 'r') as zip_ref:
            zip_ref.extractall(target_dir)
        for root, dirs, files in os.walk(target_dir):
            for file in files:
                file_path = os.path.join(target_dir, file)
                management.call_command('loaddata', file_path)

        os.remove(zip_path)
        shutil.rmtree(target_dir)
        return JsonResponse({"status": "Success"}, safe=False)


@csrf_exempt
def import_media_files(request):
    if request.method == 'POST':
        import base64

        file = request.FILES["media_dump"]
        # file_name = default_storage.save(file.name, file)
        # decoded = base64.b64decode(data["zip_base64"])
        # file = ContentFile(decoded, "media_dump.zip")
        file_name = default_storage.save(file.name, file)
        # print(file_name)
        base_dir = os.path.join(os.getcwd(), "media")
        target_dir = os.path.join(base_dir)
        zip_path = os.path.join(base_dir, file.name)
        with zipfile.ZipFile(os.path.join(base_dir, file_name), 'r') as zip_ref:
            print(target_dir)
            zip_ref.extractall(target_dir)

        os.remove(zip_path)
        # shutil.rmtree(target_dir)
        return JsonResponse({"status": "Success"}, safe=False)


def special_evaluation(request):
    context = {}

    if request.POST:
        customer = int(request.POST.get('customer'))
        vehicle = int(request.POST.get('vehicle'))
        supplier = int(request.POST.get('supplier'))
        article = int(request.POST.get('article'))
        contract = request.POST.get('contract')
        forwarders = int(request.POST.get('forwarders'))
        fromdate = request.POST.get('fromdate')
        todate = request.POST.get('todate')
        if fromdate:
            context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
            fromdate = fromdate + " 00:00:00"
        else:
            context['from'] = datetime.strptime(str(date.today()), "%Y-%m-%d")
            fromdate = str(date.today()) + " 00:00:00"
        if todate:
            context['to'] = datetime.strptime(todate, "%Y-%m-%d")
            todate = todate + " 23:59:59"
        else:
            context['to'] = datetime.strptime(str(date.today()), "%Y-%m-%d")
            todate = str(date.today()) + " 23:59:59"
        try:
            obj = None

            if customer == -1 or supplier == -1 or vehicle == -1 or article == -1 or contract == '-1':
                obj = Transaction.objects.filter(created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])

            if customer != 0 and customer != -1:
                if obj is None:
                    obj = Transaction.objects.filter(customer=customer).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])
                else:
                    obj = obj.filter(customer=customer).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])

            if supplier != 0 and supplier != -1:
                if obj is None:
                    obj = Transaction.objects.filter(supplier=supplier).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])
                else:
                    obj = obj.filter(supplier=supplier).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])

            if vehicle != 0 and vehicle != -1:
                if obj is None:
                    obj = Transaction.objects.filter(vehicle=vehicle).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])
                else:
                    obj = obj.filter(vehicle=vehicle).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])

            if forwarders != 0 and forwarders != -1:
                if forwarders is None:
                    obj = Transaction.objects.filter(forwarders=forwarders).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])
                else:
                    obj = obj.filter(forwarders=forwarders).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])

            if article != 0 and article != -1:
                if article is None:
                    obj = Transaction.objects.filter(article=article).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])
                else:
                    obj = obj.filter(article=article).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])

            if contract != '0' and contract != '-1':
                if obj is None:
                    obj = Transaction.objects.filter(contract_number=str(contract)).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])
                else:
                    obj = obj.filter(contract_number=str(contract)).filter(
                        created_date_time__range=(fromdate, todate), trans_flag__in=[1, 2])


            if MasterContainer.objects.last() is None:
                context["master_container"] = False
            else:
                context["master_container"] = MasterContainer.objects.last().status

            # obj = Transaction.objects.filter(Q(customer=customer) & Q(vehicle=vehicle) & Q(supplier=supplier) & Q(article=article)).filter(created_date_time__range=(fromdate, todate))            
            # net_weight = Transaction.objects.filter(customer=int(customer)) | vehicle=int(vehicle)) | supplier=int(supplier)) | article=int(article)).aggregate(Sum('net_weight'))
            net_weight = obj.aggregate(Sum('net_weight'))
            net_weight = net_weight['net_weight__sum']
            context['cust'] = obj
            context['customer'] = request.session['customer'] if request.session.get(
                'customer') is not None else 'Kunde'
            context['vehicle'] = 'Fahrzeug'
            context['supplier'] = request.session['supplier'] if request.session.get(
                'supplier') is not None else 'Lieferant'
            context['article'] = request.session['article'] if request.session.get('article') is not None else 'Artikel'
            context['total_weight'] = net_weight
            context['date'] = datetime.now()
        except Exception as e:
            print(e)
            pass
        if 'pdf' in request.POST:
            context['cust'] = obj.order_by('-secondw_date_time')
            return Render.render("stats/pdf/special.html", context)
        if 'excel' in request.POST:
            # wb = xlwt.Workbook(encoding='utf-8')
            # ws = wb.add_sheet('Sonderauswertung')
            # row_num = 0
            customer = request.session['customer'] if request.session.get('customer') is not None else 'Kunde'
            vehicle = 'Fahrzeug'
            supplier = request.session['supplier'] if request.session.get('supplier') is not None else 'Lieferant'
            article = request.session['article'] if request.session.get('article') is not None else 'Artikel'
            # heading = ['------', '------', '------', 'Sonderauswertung', '------', '------', '------']
            # for col_num in range(len(heading)):
            #     ws.write(row_num, col_num, heading[col_num])
            # row_num += 2
            columns = ['Datum', 'Uhrzeit', 'id', customer, vehicle, supplier, article, 'Artikelnummer', 'AVV Nummer',
                       'Preis', 'Gewicht [kg]']
            # for col_num in range(len(columns)):
            #     ws.write(row_num, col_num, columns[col_num])
            if obj is not None:
                values = obj.order_by('updated_date_time').values_list('secondw_date_time', str('id'),
                                                                       'customer__name1', 'vehicle__license_plate',
                                                                       'supplier__supplier_name', 'article__name',
                                                                       'article__article_number', 'article__avv_num',
                                                                       'article__price1',
                                                                       'net_weight', "vehicle_second_weight_flag",
                                                                       "firstw_date_time", "lfd_nr")

                list_value = [list(rows) for rows in values]
                for rows in list_value:
                    # status = rows[2]
                    # if status == '0':
                    #     rows[2] = 'Eingang'
                    # elif status == "1":
                    #     rows[2] = 'Ausgang' 
                    lfd = rows[-1]
                    if lfd is not None and lfd != "":
                        rows[1] = lfd
                        rows.pop()
                    else:
                        rows.pop()

                    if str(rows[-2]) == "1":
                        if rows[-1] != None:
                            date_time = rows[-1]
                            rows[0] = date_time.strftime("%d.%m.%Y")
                            rows.insert(1, date_time.strftime("%H:%M"))
                    else:
                        if rows[0] != None:
                            date_time = rows[0]
                            rows[0] = date_time.strftime("%d.%m.%Y")
                            rows.insert(1, date_time.strftime("%H:%M"))
                    rows.pop()
                    rows.pop()
                    print(rows)
                list_value.append([None, None, None, None, None, None, None, None])
                list_value.append([None, None, None, None, None, None, 'SUM', net_weight])
                # for row in values:
                #     row_num += 1
                #     for col_num in range(len(row)):
                #         if col_num == 0:
                #             ws.write(row_num, col_num, datetime.strftime(row[col_num], "%d.%m.%Y %H:%M"))
                #         else:
                #             ws.write(row_num, col_num, row[col_num])

                # row = 7
                # row_num += 2
                # for col_num in range(row):
                #     if col_num == 5:
                #         ws.write(row_num, col_num, 'SUM')
                #     if col_num == 6:
                #         ws.write(row_num, col_num, net_weight)
            filename = "sonderauswertung_%s.csv" % str(date.today())
            # response = HttpResponse(content_type='application/ms-excel')
            # response['Content-Disposition'] = 'attachment; filename="%s"' % filename
            # wb.save(response)
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            writer = csv.writer(response, delimiter=';')
            response.write(u'\ufeff'.encode('utf8'))
            writer.writerow(columns)
            for row in list_value:
                writer.writerow(row)
            return response
    context['customer'] = Customer.objects.all()
    context['vehicle'] = Vehicle.objects.all()
    context['supplier'] = Supplier.objects.all()
    context['article'] = Article.objects.all()
    context["contract"] = Contract.objects.all()
    context["forwarders"] = Forwarders.objects.all()
    return render(request, "stats/special_evaluation.html", context)


def invoice_pdf(request):
    context = {}
    if request.POST:
        ids = request.POST['multiple_ids'].split(',')
        obj = []
        lst = []
        vat = []
        total_price = []
        price_after_tax = []
        cust_id = None
        for i in range(len(ids)):

            dic = {}

            T = Transaction.objects.get(id=int(ids[i]))
            obj.append(T)
            cust_id = T.customer.id

            if T.material_weight and T.material_weight > 0.0:
                material_weight = T.material_weight
            else:
                material_weight = None

            if T.article and not T.article.price1:
                article_price = None
            else:
                article_price = 0

            dic = {'id': T.id,
                   'article': T.article.name if T.article else None,
                   'vat': '0.0',
                   'customer': T.customer.name1 if T.customer else None,
                   'supplier': T.supplier.name if T.supplier else None,
                   'net_weight': T.net_weight,
                   'material_weight': material_weight,
                   'total_price': T.total_price if T.total_price else 0.0,
                   'price_per_item': T.price_per_item if T.price_per_item else 0.0,
                   'created': T.created_date_time,
                   }
            if dic['price_per_item'] == 0.0:
                dic['price_per_item'] = 0.0 if article_price is None else float(T.article.price1)
                if material_weight:
                    dic['total_price'] = round(float(material_weight) * dic['price_per_item'], 2)
                    total_price.append(dic['total_price'])
                else:
                    dic['total_price'] = round(float(T.net_weight) * dic['price_per_item'], 2)
                    total_price.append(dic['total_price'])

            if (dic['article'] and T.article.vat) and (T.article.vat != 0.0):
                vat.append(int(T.article.vat))
            else:
                vat.append(0)

            lst.append(dic)

            ######################################
            # CREATING RANDOM INVOICE NUMBER 
            ######################################
            try:
                invoice = Invoice.objects.get(invoice=T)
            except Exception as e:
                invoice = None
            if not invoice:
                while True:
                    number = random.randrange(1000, 9999)
                    try:
                        uniq = Invoice.objects.get(invoice_no=number)
                    except Invoice.DoesNotExist as e:
                        uniq = None
                    if not uniq:
                        if not invoice:
                            invoice = Invoice.objects.create(invoice_no=number, invoice=T)
                            invoice.save()
                            break
                        else:
                            invoice = invoice
                            break
            dic['invoice_no'] = invoice.invoice_no

        vat = max(vat)
        total_price = sum(total_price)
        context['vat'] = vat
        context['total_price'] = total_price
        context['tax_price'] = (vat * total_price) / 100
        context['price_after_tax'] = (vat * total_price) / 100 + total_price

        context['obj'] = invoice
        context['data'] = lst
        context['cust'] = Customer.objects.get(id=int(cust_id))
        context['heading'] = Logo.objects.last()
        context['current_date'] = datetime.now().date
        context['trans_data'] = T
        return Render.render('stats/invoice_pdf_multiple.html', context)

    if request.GET.get('id') and request.GET.get('loc'):
        dic = {}
        i = Transaction.objects.get(id=int(request.GET['id']))

        if i.material_weight and i.material_weight > 0.0:
            material_weight = i.material_weight
        else:
            material_weight = None
        created = i.created_date_time
        net_weight = i.net_weight
        status = i.status
        lst = []
        context['total'] = 0
        total_sum = 0
        t = 0
        ded = 0
        if len(i.deduction.all()):
            for d in i.deduction.all():
                dic = {'id': i.id,
                       'deduction': d.name,
                       'vat': d.vat if d.vat else 0.0,
                       'deduction_weight': d.deduct_weight,
                       'total_price': i.total_price if i.total_price else 0.0,
                       'price_per_item': d.price1 if d.price1 else 0.0,
                       }
                if dic['deduction_weight']:
                    total = float(dic['deduction_weight']) * float(dic['price_per_item'])
                else:
                    total = 0

                if dic['vat'] > 0.0:
                    dic['total_price'] = round((total * float(dic['vat'])) / 100 + total, 2)
                else:
                    dic['total_price'] = round(total, 2)

                total_sum += float(dic['total_price'])
                context['total'] = round(total_sum, 2)

                lst.append(dic)
                if t == 2:
                    dic['net_weight'] = float(net_weight) - (float(d.deduct_weight) if d.deduct_weight else float(0.0))
                    dic['material_weight'] = float(dic['net_weight']) - (
                        float(dic['deduction_weight']) if dic['deduction_weight'] else float(0.0))
                elif t == 1:
                    dic['net_weight'] = float(net_weight) - float(ded)
                    dic['material_weight'] = float(dic['net_weight']) - (
                        float(dic['deduction_weight']) if dic['deduction_weight'] else float(0.0))
                    t = 2
                else:
                    ded = dic['deduction_weight']
                    dic['net_weight'] = net_weight
                    dic['material_weight'] = float(dic['net_weight']) - (
                        float(dic['deduction_weight']) if dic['deduction_weight'] else float(0.0))
                    t = 1

                net_weight = dic['net_weight']

        try:
            invoice = Invoice.objects.get(invoice=i)
        except Exception as e:
            invoice = None
        if not invoice:
            while True:
                number = random.randrange(1000, 9999)
                try:
                    uniq = Invoice.objects.get(invoice_no=number)
                except Invoice.DoesNotExist as e:
                    uniq = None
                if not uniq:
                    if not invoice:
                        invoice = Invoice.objects.create(invoice_no=number, invoice=i)
                        invoice.save()
                        break
                    else:
                        invoice = invoice
                        break

        context['obj'] = invoice
        context['status'] = status
        context['data'] = lst
        context['created'] = created
        context['cust'] = Customer.objects.get(id=i.customer.id) if i.customer else None
        context['heading'] = Logo.objects.all().last()
        context['current_date'] = datetime.now().date
        context['trans_data'] = i
        return Render.render('stats/invoice_pdf_abzugs.html', context)

    if request.GET.get("id"):
        dic = {}
        i = Transaction.objects.get(id=int(request.GET['id']))

        if i.material_weight and i.material_weight > 0.0:
            material_weight = i.material_weight
        else:
            material_weight = None

        if i.article and not i.article.price1:
            article_price = None
        else:
            article_price = 0

        dic = {'id': i.id,
               'article': i.article.name if i.article else None,
               'vat': '0.0',
               'customer': i.customer.name1 if i.customer else None,
               'supplier': i.supplier.name if i.supplier else None,
               'first_weight': i.first_weight,
               'second_weight': i.second_weight,
               'net_weight': i.net_weight,
               'material_weight': material_weight,
               'total_price': i.total_price if i.total_price else 0.0,
               'price_per_item': i.price_per_item if i.price_per_item else 0.0,
               'price_after_tax': i.total_price,
               'created': i.created_date_time,
               }
        if dic['price_per_item'] == 0.0:
            dic['price_per_item'] = 0.0 if article_price is None else float(i.article.price1)
            if material_weight:
                dic['total_price'] = float(material_weight) * dic['price_per_item']
            else:
                dic['total_price'] = float(i.net_weight) * dic['price_per_item']
        if i.article.vat:
            print("New")
        if (dic['article'] and i.article.vat) and (i.article.vat != 0.0):
            context['tax'] = i.article.vat
            dic['vat'] = (dic['total_price'] * float(i.article.vat)) / 100
            dic['price_after_tax'] = (dic['total_price'] * float(i.article.vat)) / 100 + dic['total_price']
        else:
            context['tax'] = 0.0
            dic['price_after_tax'] = dic['total_price']

        try:
            invoice = Invoice.objects.get(invoice=i)
        except Exception as e:
            invoice = None
        if not invoice:
            while True:
                number = random.randrange(1000, 9999)
                try:
                    uniq = Invoice.objects.get(invoice_no=number)
                except Invoice.DoesNotExist as e:
                    uniq = None
                if not uniq:
                    if not invoice:
                        invoice = Invoice.objects.create(invoice_no=number, invoice=i)
                        invoice.save()
                        break
                    else:
                        invoice = invoice
                        break

        context['obj'] = invoice
        context['data'] = dic
        context['cust'] = Customer.objects.get(id=i.customer.id) if i.customer else None
        context['heading'] = Logo.objects.last()
        return Render.render('stats/invoice_pdf.html', context)

    return render(request, 'stats/invoice.html', context)


def get_invoices(request):
    context = {}
    context['customer'] = Customer.objects.all()
    context['article'] = Article.objects.all()
    if request.POST:
        fromdate = request.POST.get('fromdate')
        todate = request.POST.get('todate')
        customer = request.POST.get('customer') if request.POST.get('customer') != '0' else None
        article = request.POST.get('article') if request.POST.get('article') != '0' else None

        if fromdate:
            newfrom = fromdate + " 00:00:00"
        else:
            newfrom = str(date.today()) + " 00:00:00"

        if todate:
            newto = todate + " 23:59:59"
        else:
            newto = str(date.today()) + " 23:59:59"
        transaction = None

        if fromdate and todate:
            if customer and article:
                transaction = Transaction.objects.filter(customer=int(customer), article=int(article),
                                                         created_date_time__range=(newfrom, newto))
            if article:
                transaction = Transaction.objects.filter(article=int(article),
                                                         created_date_time__range=(newfrom, newto))
            if customer:
                transaction = Transaction.objects.filter(customer=int(customer),
                                                         created_date_time__range=(newfrom, newto))
        else:
            if customer and article:
                transaction = Transaction.objects.filter(customer=int(customer), article=int(article))
            if article:
                transaction = Transaction.objects.filter(article=int(article))
            if customer:
                transaction = Transaction.objects.filter(customer=int(customer))

        if transaction is not None:
            context['data'] = transaction
        return render(request, 'stats/invoice.html', context)
    return render(request, 'stats/invoice.html', context)

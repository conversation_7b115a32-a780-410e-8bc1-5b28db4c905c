import os
import time

import math
import cv2
from collections import OrderedDict
import requests
import simplejson
from django.core.paginator import Paginator
from django.http import StreamingHttpResponse, HttpResponse
import django_filters
from django.conf import settings
from django.core import management
import zipfile
import shutil
import logging
import logging.handlers as handlers
from django.http import QueryDict
from django.db.models import Q

from rest_framework.pagination import PageNumberPagination
from rest_framework import filters

logHandler = handlers.RotatingFileHandler("yardman.log", maxBytes=9999999, backupCount=0)
logging.basicConfig(
    format='%(asctime)s,%(msecs)d %(levelname)-8s [%(pathname)s:%(lineno)d in  function %(funcName)s] %(message)s',
    handlers=[logHandler])
logger = logging.getLogger()
logger.setLevel(level=logging.DEBUG)

# logging.basicConfig(
#     format='%(asctime)s,%(msecs)d %(levelname)-8s [%(pathname)s:%(lineno)d in  function %(funcName)s] %(message)s',
#     datefmt='%Y-%m-%d:%H:%M:%S',
#     level=logging.DEBUG,
#     filename="yardman.log"
# )
import xmltodict
from django.db.models import Q
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.renderers import BaseRenderer, JSONRenderer, BrowsableAPIRenderer
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet, ViewSet
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView
from rest_framework.parsers import JSONParser, FileUploadParser, MultiPartParser, FormParser

from .models import DisplayText, HandTransmitter, LoadingBox, MaxVehicle, Notification, RouteImage, Vehicle, Customer, \
    Supplier, Article, BuildingSite, \
    Warehouse, Transaction, Combination, Forwarders, \
    images_base64, Logo, Signature, ShowTonne, Io, DriverSignature, Contract, ApiDeliveryNoteOption, DriverID, Barriers, \
    OfficeName, User, VehicleType, FireAlarm, WeightPageSetting, Phone, TourApprove

from .serializers import HandTransmitterSerializer, LoadingBoxSerializer, MaxVehicleSerializer, NotificationSerializer, \
    RouteImageSerializer, VehicleSerializer, \
    CustomerSerializer, SupplierSerializer, \
    ArticleSerializer, \
    BuildingSiteSerializer, WarehouseSerializer, TransactionSerializer, CombinationSerializer, \
    TransactSerializer, CombinationReadSerializer, ForwarderSerializer, SaveImageSerializer, \
    DriverSignSerializer, GetDetailCombinationSerializer, ContractSerializer, DriverIDSerializer, BarriersSerializer, \
    OfficeNameTimingSerializer, UserSerializer, VehicleTypeSerializer, FireAlarmSerializer, OfficeNameSerializer, \
    TourApproveSerializer

from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.authtoken.models import Token
from rest_framework.authentication import BasicAuthentication, SessionAuthentication
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
import io
import json
from .render import Render
import random

from .utils.view_handling import generate_qr_code

from django.conf import settings
from reportlab.pdfgen import canvas
import qrcode
from rest_framework import status
import base64, codecs
from PIL import Image
from io import StringIO, BytesIO
import pyotp
from django.core.exceptions import ObjectDoesNotExist
import datetime


class BinaryFileRenderer(BaseRenderer):
    media_type = 'application/pdf'
    format = None
    charset = None
    render_style = 'binary'

    def render(self, data, media_type=None, renderer_context=None):
        return data


class VehicleView(ModelViewSet):
    queryset = Vehicle.objects.all()
    serializer_class = VehicleSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = VehicleSerializer(data=request.data,
                                       context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class CustomerView(ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = CustomerSerializer(data=request.data,
                                        context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class SupplierView(ModelViewSet):
    queryset = Supplier.objects.all()
    serializer_class = SupplierSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = SupplierSerializer(data=request.data,
                                        context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class ArticleView(ModelViewSet):
    queryset = Article.objects.all()
    serializer_class = ArticleSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = ArticleSerializer(data=request.data,
                                       context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class BuildingSiteView(ModelViewSet):
    queryset = BuildingSite.objects.all()
    serializer_class = BuildingSiteSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = BuildingSiteSerializer(data=request.data,
                                            context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class TourApproveView(ModelViewSet):
    queryset = TourApprove.objects.all()
    serializer_class = TourApproveSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = TourApproveSerializer(data=request.data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class WarehouseView(ModelViewSet):
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        serializer = WarehouseSerializer(data=request.data,
                                         context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class TransactionView(ModelViewSet):
    queryset = Transaction.objects.all()
    serializer_class = TransactionSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    def create(self, request):
        last_transact = Transaction.objects.last()
        if last_transact.lfd_nr != None and last_transact.lfd_nr != "":
            request.data["lfd_nr"] = int(last_transact.lfd_nr) + 1
        else:
            request.data["lfd_nr"] = int(last_transact.id) + 1
        serializer = TransactionSerializer(data=request.data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        if serializer.save():
            logger.info(
                f"method: {request.method}  message: Transaction with id {serializer.data['id']} created successfully \n data: {request.POST}")
        else:
            logger.error(
                f"method: {request.method} message: Transaction with id {serializer.data['id']} is not created")

        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        if serializer.save():
            logger.info(
                f"method: {request.method}  message: Transaction with id {serializer.data['id']} updated successfully \n data: {request.POST}")
        else:
            logger.error(
                f"method: {request.method} message: Transaction with id {serializer.data['id']} is not updated")

        return Response(serializer.data)


### READ ONLY VIEW ###


class TransactView(ReadOnlyModelViewSet):
    queryset = Transaction.objects.all()
    serializer_class = TransactSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['vehicle__license_plate']
    filterset_fields = '__all__'
    # filterset_fields = {
    #     'id': ["in", "exact"],  # note the 'in' field
    #     'gate': ["in"],
    #     'vehicle': ["in"]
    # }


class CustomPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'


class TransactPageCustomFilterBackend(filters.BaseFilterBackend):
    """
    Filter that only allows users to see their own objects.
    """

    def filter_queryset(self, request, queryset, view):
        trans_flag = request.query_params.get('trans_flag')
        vehicle = request.query_params.get('vehicle')
        article = request.query_params.get('article')
        supplier = request.query_params.get('supplier')
        gate = request.query_params.get('gate')
        place_of_delivery = request.query_params.get('place_of_delivery')
        container = request.query_params.get('container')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        if trans_flag is not None:
            queryset = queryset.filter(trans_flag__in=trans_flag.split(","))

        if vehicle is not None:
            queryset = queryset.filter(vehice__in=vehicle.split(","))

        if article is not None:
            queryset = queryset.filter(article__in=article.split(","))

        if supplier is not None:
            queryset = queryset.filter(supplier__in=supplier.split(","))

        if place_of_delivery is not None:
            queryset = queryset.filter(place_of_delivery__in=place_of_delivery.split(","))

        if container is not None:
            queryset = queryset.filter(container__in=container.split(","))

        if gate is not None:
            query = Q()
            for gateObject in gate.split(","):
                query |= Q(gate__contains=gateObject)
            queryset = queryset.filter(query)

        if start_date is not None and end_date is None:
            start_date = start_date + " 00:00:00.000000"
            end_date = str(datetime.datetime.now().date()) + " 23:59:59.000000"
            queryset = queryset.filter(secondw_date_time__range=(start_date, end_date))

        if start_date is None and end_date is not None:
            start_date = "2000-01-01 00:00:00.000000"
            end_date = end_date + " 23:59:59.000000"
            queryset = queryset.filter(secondw_date_time__range=(start_date, end_date))

        if start_date is not None and end_date is not None:
            start_date = start_date + " 00:00:00.000000"
            end_date = end_date + " 23:59:59.000000"
            queryset = queryset.filter(secondw_date_time__range=(start_date, end_date))

        return queryset


class BarriersView(ModelViewSet):
    queryset = Barriers.objects.all().order_by("id")
    serializer_class = BarriersSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'


class TransactPageView(ReadOnlyModelViewSet):
    page_size = 2
    queryset = Transaction.objects.all().order_by('-updated_date_time')
    serializer_class = TransactSerializer
    pagination_class = CustomPagination
    filter_backends = [TransactPageCustomFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = '__all__'
    search_fields = ['vehicle__license_plate', 'combination_id', 'project_number']
    ordering_fields = (
    'project_number', 'vehicle', 'first_weight', 'firstw_date_time', 'secondw_date_time', 'second_weight', 'net_weight',
    'supplier', 'gate'
    , 'export_goods')


class TransactPageHoflisteView(ReadOnlyModelViewSet):
    page_size = 2
    queryset = Transaction.objects.all().order_by('-firstw_date_time')
    serializer_class = TransactSerializer
    pagination_class = CustomPagination
    filter_backends = [TransactPageCustomFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['vehicle__license_plate', 'combination_id', 'project_number']
    filterset_fields = {
        'gate': ["in", "exact"],  # note the 'in' field
        'trans_flag': ["exact"]
    }
    ordering_fields = ('project_number', 'vehicle', 'first_weight', 'firstw_date_time', 'supplier', 'gate'
                       , 'export_goods')


class TransactTabletView(ReadOnlyModelViewSet):
    page_size = 2
    queryset = Transaction.objects.all().order_by('-updated_date_time')
    serializer_class = TransactSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, TransactPageCustomFilterBackend, filters.SearchFilter]
    search_fields = ['vehicle__license_plate']
    filterset_fields = {
        'id': ["in", "exact"],  # note the 'in' field
        'gate': ["in"],
        'vehicle': ["in"]
    }


class TransactFilterView(ReadOnlyModelViewSet):
    queryset = Transaction.objects.all().order_by('-updated_date_time')
    serializer_class = TransactSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['vehicle__license_plate']
    filterset_fields = {
        'id': ["in", "exact"],  # note the 'in' field
        'gate': ["in"],
        'vehicle': ["in"],
        'trans_flag': ["in"],
        'temp': ["in"]
    }


##### END #####


class CombinationReadView(ReadOnlyModelViewSet):
    queryset = Combination.objects.all()
    serializer_class = CombinationReadSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'


class DetailCombinationView(ReadOnlyModelViewSet):
    queryset = Combination.objects.all()
    serializer_class = GetDetailCombinationSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'


class CombinationView(ModelViewSet):
    queryset = Combination.objects.all()
    serializer_class = CombinationSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def create(self, request):
        data = request.data
        # data._mutable = True
        if data.get('ident') is None or data.get('ident') == '':
            obj = Combination.objects.filter(ident__contains='RW')
            lst = []
            if len(obj) > 0:
                for i in obj:
                    lst.append(int(i.ident.replace('RW', '')))
            if len(lst) > 0:
                number = random.randrange(1000, 9999)
                for i in lst:
                    if i != number:
                        data['ident'] = 'RW' + str(number)
                        break
            else:
                data['ident'] = 'RW0001'
        serializer = CombinationSerializer(data=data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class ForwarderView(ModelViewSet):
    queryset = Forwarders.objects.all()
    serializer_class = ForwarderSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'


class SaveImageView(ModelViewSet):
    queryset = images_base64.objects.all()
    serializer_class = SaveImageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['id', 'transaction']


class DriverSignView(ModelViewSet):
    queryset = DriverSignature.objects.all()
    serializer_class = DriverSignSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['id', 'transaction_id']


class VehicleTypeView(ModelViewSet):
    queryset = VehicleType.objects.all()
    serializer_class = VehicleTypeSerializer
    filter_backends = [DjangoFilterBackend]


class OfficeNameView(ModelViewSet):
    queryset = OfficeName.objects.all()
    serializer_class = OfficeNameSerializer
    filter_backends = [DjangoFilterBackend]


class FireAlarmView(APIView):
    queryset = FireAlarm.objects.all()
    serializer_class = FireAlarmSerializer

    def get(self, request):
        alarm = FireAlarm.objects.last()
        if alarm is None:
            return Response({'status': 'false'})
        else:
            if alarm.status:
                return Response({'status': 'true'})
            else:
                return Response({'status': 'false'})

    def post(self, request):
        alarm = FireAlarm.objects.last()

        if request.data.get('status') == 'true':
            if alarm is None:
                FireAlarm.objects.create(status=True)
                return Response({'status': 'true'})
            else:
                alarm.status = True
                alarm.save()
                return Response({'status': 'true'})
        else:
            if alarm is None:
                FireAlarm.objects.create(status=False)
                return Response({'status': 'false'})
            else:
                alarm.status = False
                alarm.save()
                return Response({'status': 'false'})


class RemoveAllTourApprove(APIView):
    def post(self, request):
        approves = TourApprove.objects.filter(deleted=False)

        for approve in approves:
            approve.deleted = True
            approve.save()

        return Response({'msg': 'updated'}, status=status.HTTP_200_OK)


class RemoveAllNotification(APIView):
    def post(self, request):
        notifications = Notification.objects.filter(status=False)

        for notification in notifications:
            notification.status = True
            notification.save()

        return Response({'msg': 'updated'}, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
def fire_alarm(request):
    alarm = FireAlarm.objects.last()

    if request.method == 'POST':
        if alarm is None:
            FireAlarm.objects.create(status=True)
            return JsonResponse({'status': 'true'})
        else:
            print(request.POST)
            if request.POST.get('status') == 'false':
                alarm.status = False
                alarm.save()
                return JsonResponse({'status': 'false'})
            else:
                alarm.status = True
                alarm.save()
                return JsonResponse({'status': 'true'})


@method_decorator(csrf_exempt, name='dispatch')
class BackendPrintQRCode(APIView):
    authentication_classes = []
    renderer_classes = [BrowsableAPIRenderer, BinaryFileRenderer]

    def post(self, request):
        # {"id": 1286, "username": "admin"}
        if os.name == 'nt':
            import win32print
            import win32api

        data = self.request.data
        context = {}
        try:
            transaction = Transaction.objects.get(id=data['id'])
            context['dataset'] = transaction
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        transaction_process_id = None
        if transaction.trans_flag == 0:
            transaction_process_id = f"FW{transaction.id}"
        else:
            transaction_process_id = f"SW{transaction.id}"
        context["transaction_process_id"] = transaction_process_id
        context['logo'] = generate_qr_code("Test", 6)
        response = Render.render("yard/api_pdf_backend_print.html", context)
        if settings.MEDIA_LOCATION:
            if not os.path.exists(data['id']):
                os.mkdir(os.path.join(settings.MEDIA_LOCATION, data["id"]))
            file_path = os.path.join(settings.MEDIA_LOCATION, data["id"], "{}.pdf".format(data['id']))
        else:
            file_path = os.path.join(os.getcwd(), "media", "transaction_pdf", "{}.pdf".format(data['id']))
        file = open(file_path, "wb")
        file.write(response.content)
        file.close()
        if settings.PRINTING:
            os.system('lp -d {0} {1}'.format(settings.PRINTING, file_path))
        return Response({"message": "Success"})
        # response = Render.render("yard/api_pdf_template.html", context)
        # import base64
        # stream = base64.b64encode(response._container[0])
        # return Response({'pdf': json.dumps(stream.decode('utf-8'))})


def get_barrier_status(request):
    barrier_ip = request.GET['barrier_ip']
    url = 'http://' + barrier_ip + '/status_vehicle.xml'
    response = requests.get(url=url, verify=False, timeout=10)
    data = xmltodict.parse(response.content)

    if data['response']:
        if int(data['response']['out4']) == 1:
            return HttpResponse(simplejson.dumps({"status": "open"}))
        elif int(data['response']['out5']) == 1:
            return HttpResponse(simplejson.dumps({"status": "closed"}))
        else:
            return HttpResponse(simplejson.dumps({"status": "invalid"}))
    else:
        return HttpResponse(simplejson.dumps({"status": "invalid"}))


@method_decorator(csrf_exempt, name='dispatch')
class Pdf_print(APIView):

    def get(self, request, format=None):
        return Response({'Format': {"id": 0, "username": ""}})

    def post(self, request, format=None):
        data = self.request.data
        context = {}
        try:
            context['dataset'] = Transaction.objects.get(id=data['id'])
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()

        heading = []
        context["header_image"] = Logo.objects.all()
        if Logo.objects.all().last() is not None:
            if Logo.objects.all().last().heading is not None:
                heading = Logo.objects.all().last().heading.split("|")
        context['logo'] = heading
        context['user_name'] = request.user.name
        context['sign'] = Signature.objects.filter(
            user__name=data['username']).last()
        context['customer'] = 'Kunde'
        context['article'] = 'Artikel'
        context['show_price'] = 'false'
        context['show_container'] = 'false'
        context['showt'] = ShowTonne.objects.all().last()
        context['io'] = Io.objects.all().last()
        context['tara_date'] = '1'
        context["wps"] = WeightPageSetting.objects.last()
        context['driver_sign'] = DriverSignature.objects.filter(
            transaction_id=data['id']).last()
        context['images'] = context['dataset'].images_base64_set.first()

        response = Render.render("yard/pdf_template.html", context)
        import base64
        stream = base64.b64encode(response._container[0])
        return Response({'pdf': json.dumps(stream.decode('utf-8'))})


@method_decorator(csrf_exempt, name='dispatch')
class Pdf_print_lieferscheine(APIView):

    def get(self, request, format=None):
        return Response({'Format': {"id": 0, "username": ""}})

    def post(self, request, format=None):
        data = self.request.data
        context = {}
        try:
            context['dataset'] = Transaction.objects.get(id=data['id'])
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        context['logo'] = Logo.objects.all().last()
        if Logo.objects.all().last() is not None:
            heading = Logo.objects.all().last().heading
        else:
            heading = None
        if heading is not None:
            context['heading'] = Logo.objects.all().last().heading.split(".")
        context['user_name'] = data['username']
        context['sign'] = Signature.objects.filter(
            user__name=data['username']).last()
        context['customer'] = 'Kunde'
        context['article'] = 'Artikel'
        context['show_price'] = 'false'
        context['show_container'] = 'false'
        context['showt'] = ShowTonne.objects.all().last()
        context['io'] = Io.objects.all().last()
        context['tara_date'] = '1'
        context['driver_sign'] = DriverSignature.objects.filter(
            transaction_id=data['id']).last()
        context['images'] = context['dataset'].images_base64_set.first()

        template = "yard/api_lieferscheine_pdf_template.html"
        if ApiDeliveryNoteOption.objects.last() is not None:
            if ApiDeliveryNoteOption.objects.last().option == 2:
                template = "yard/api_pdf_template_rotzler.html"
            if ApiDeliveryNoteOption.objects.last().option == 3:
                template = "yard/api_pdf_template_meba.html"
            if ApiDeliveryNoteOption.objects.last().option == 4:
                template = "yard/api_pdf_template_bruttogewicht.html"
        response = Render.render(template, context)
        import base64
        stream = base64.b64encode(response._container[0])
        return Response({'pdf': json.dumps(stream.decode('utf-8'))})


@method_decorator(csrf_exempt, name='dispatch')
class Pdf_sebald(APIView):

    def get(self, request, format=None):
        return Response({'Format': {"id": 0, "username": ""}})

    def post(self, request, format=None):
        data = self.request.data
        context = {}
        try:
            context['dataset'] = Transaction.objects.get(id=data['id'])
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        context['logo'] = Logo.objects.all()
        context['user_name'] = data['username']
        context['sign'] = Signature.objects.filter(
            user__name=data['username']).last()
        context['customer'] = 'Kunde'
        context['article'] = 'Artikel'
        context['show_price'] = 'false'
        context['show_container'] = 'false'
        context['showt'] = ShowTonne.objects.all().last()
        context['io'] = Io.objects.all().last()
        context['tara_date'] = '1'
        context['driver_sign'] = DriverSignature.objects.filter(
            transaction_id=data['id']).last()

        context['images'] = context['dataset'].images_base64_set.first()

        response = Render.render("yard/pdf_template_sebald.html", context)
        import base64
        stream = base64.b64encode(response._container[0])
        return Response({'pdf': json.dumps(stream.decode('utf-8'))})


@method_decorator(csrf_exempt, name='dispatch')
class dump_db_database(APIView):

    def get(self, request, format=None):
        return Response({'Format': "None"})

    def post(self, request, format=None):
        apps = [
            "yard",
            "stats",
            'scale_app',
        ]
        static_dir = os.path.join(os.getcwd(), "stats", "static")
        try:
            os.mkdir(os.path.join(static_dir, "dump"))
        except:
            print('*******************')
            check_path = static_dir + '\dump'
            ex = os.path.isdir(check_path)
            if ex:
                pass
            else:
                os.mkdir(os.path.join(static_dir, "dump"))
            print('*******************')

        base_dir = os.path.join(static_dir, "dump")
        for app_name in apps:
            output_file = os.path.join(base_dir, f"{app_name}.json")
            file = open(output_file, 'w')
            management.call_command('dumpdata', app_name, format='json', indent=3, stdout=file)
        zip_path = os.path.join(static_dir, "dump.zip")
        zipf = zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED)
        for root, dirs, files in os.walk(base_dir):
            for file in files:
                zipf.write(os.path.join(base_dir, file),
                           os.path.relpath(os.path.join(root, file),
                                           os.path.join(base_dir)))
        zipf.close()
        import base64
        with open(zip_path, "rb") as f:
            bytes = f.read()
            encoded = base64.b64encode(bytes)

        zipf.close()
        os.remove(zip_path)

        return Response({'zip_base64': json.dumps(encoded.decode('utf-8'))})


@method_decorator(csrf_exempt, name='dispatch')
class dump_media_files(APIView):

    def get(self, request, format=None):
        return Response({'Format': "None"})

    def post(self, request, format=None):
        static_dir = os.path.join(os.getcwd(), "media")

        base_dir = os.path.join(static_dir, "drivers_sign")
        base_dir_images = os.path.join(static_dir, "trans_images")
        base_dir_logo = os.path.join(static_dir, "logo")
        base_dir_signatures = os.path.join(static_dir, "signatures")
        zip_path = os.path.join(static_dir, "media_dump.zip")
        zipf = zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED)

        for root, dirs, files in os.walk(base_dir):
            for file in files:
                zipf.write(os.path.join(base_dir, file),
                           os.path.basename(os.path.normpath("drivers_sign")) + "\\" + file)

        for root, dirs, files in os.walk(base_dir_images):
            for file in files:
                zipf.write(os.path.join(base_dir_images, file),
                           os.path.basename(os.path.normpath("trans_images")) + "\\" + file)

        for root, dirs, files in os.walk(base_dir_logo):
            for file in files:
                zipf.write(os.path.join(base_dir_logo, file), os.path.basename(os.path.normpath("Logo")) + "\\" + file)

        for root, dirs, files in os.walk(base_dir_signatures):
            for file in files:
                zipf.write(os.path.join(base_dir_signatures, file),
                           os.path.basename(os.path.normpath("signatures")) + "\\" + file)

        import base64
        with open(zip_path, "rb") as f:
            bytes = f.read()
            encoded = base64.b64encode(bytes)

        zipf.close()
        os.remove(zip_path)

        return Response({'zip_base64': json.dumps(encoded.decode('utf-8'))})


class import_media(APIView):
    def get(self, request, format=None):
        return Response({'Format': {"zip_base64": ""}})

    def post(self, request, format=None):
        if request.method == 'POST':
            import base64

            file = request.FILES["media_dump"]
            # file_name = default_storage.save(file.name, file)
            # decoded = base64.b64decode(data["zip_base64"])
            # file = ContentFile(decoded, "media_dump.zip")
            file_name = default_storage.save(file.name, file)
            # print(file_name)
            base_dir = os.path.join(os.getcwd(), "media")
            target_dir = os.path.join(base_dir)
            zip_path = os.path.join(base_dir, file.name)
            with zipfile.ZipFile(os.path.join(base_dir, file_name), 'r') as zip_ref:
                print(target_dir)
                zip_ref.extractall(target_dir)

            os.remove(zip_path)
            # shutil.rmtree(target_dir)
            return JsonResponse({"status": "Success"}, safe=False)


class import_data(APIView):
    def get(self, request, format=None):
        return Response({'Format': {"zip_base64": ""}})

    def post(self, request, format=None):
        data = self.request.data
        apps = [
            "yard",
            "stats",
            'scale_app',
        ]

        if request.method == 'POST':
            import base64
            decoded = base64.b64decode(data["zip_base64"])
            file = ContentFile(decoded, "dump.zip")
            file_name = default_storage.save(file.name, file)
            # print(file_name)
            base_dir = os.path.join(os.getcwd(), "media")
            target_dir = os.path.join(base_dir, "dump")
            zip_path = os.path.join(base_dir, file.name)
            with zipfile.ZipFile(os.path.join(base_dir, file_name), 'r') as zip_ref:
                zip_ref.extractall(target_dir)
            for root, dirs, files in os.walk(target_dir):
                for file in files:
                    file_path = os.path.join(target_dir, file)
                    management.call_command('loaddata', file_path)

            os.remove(zip_path)
            shutil.rmtree(target_dir)
            return JsonResponse({"status": "Success"}, safe=False)


@method_decorator(csrf_exempt, name='dispatch')
class Pdf_woimg(APIView):

    def get(self, request, format=None):
        return Response({'Format': {"id": 0, "username": ""}})

    def post(self, request, format=None):
        data = self.request.data
        context = {}
        try:
            context['dataset'] = Transaction.objects.get(id=data['id'])
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        context['logo'] = Logo.objects.all()
        context['user_name'] = data['username']
        context['sign'] = Signature.objects.filter(
            user__name=data['username']).last()
        context['customer'] = 'Kunde'
        context['article'] = 'Artikel'
        context['show_price'] = 'false'
        context['show_container'] = 'false'
        context['showt'] = ShowTonne.objects.all().last()
        context['io'] = Io.objects.all().last()
        context['tara_date'] = '1'
        context['driver_sign'] = DriverSignature.objects.filter(
            transaction_id=data['id']).last()

        response = Render.render("yard/pdf_template_without_images.html", context)
        import base64
        stream = base64.b64encode(response._container[0])
        return Response({'pdf': json.dumps(stream.decode('utf-8'))})


@method_decorator(csrf_exempt, name='dispatch')
class Pdf_print_lieferscheine_internal(APIView):

    def get(self, request, format=None):
        return Response({'Format': {"id": 0, "username": ""}})

    def post(self, request, format=None):
        data = self.request.data
        context = {}
        try:
            context['dataset'] = Transaction.objects.get(id=data['id'])
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        context['logo'] = Logo.objects.all()
        context['user_name'] = data['username']
        context['sign'] = Signature.objects.filter(
            user__name=data['username']).last()
        context['customer'] = 'Kunde'
        context['article'] = 'Artikel'
        context['show_price'] = 'false'
        context['show_container'] = 'false'
        context['showt'] = ShowTonne.objects.all().last()
        context['io'] = Io.objects.all().last()
        context['tara_date'] = '1'
        context['driver_sign'] = DriverSignature.objects.filter(
            transaction_id=data['id']).last()
        context['images'] = context['dataset'].images_base64_set.first()

        response = Render.render("yard/pdf_template.html", context)
        import base64
        stream = base64.b64encode(response._container[0])
        return Response({'pdf': json.dumps(stream.decode('utf-8'))})


@method_decorator(csrf_exempt, name='dispatch')
class Pdf_print_external(APIView):

    def get(self, request, format=None):
        return Response({'Format': {"id": 0, "username": ""}})

    def post(self, request, format=None):
        data = self.request.data
        context = {}
        try:
            context['dataset'] = Transaction.objects.get(id=data['id'])
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        context['user_name'] = data['username']
        context['customer'] = 'Kunde'
        context['article'] = 'Artikel'
        context['show_price'] = 'false'
        context['show_container'] = 'false'
        context['tara_date'] = '1'
        context['showt'] = ShowTonne.objects.all().last()
        context['io'] = Io.objects.all().last()

        response = Render.render("yard/pdf_template_external.html", context)
        import base64
        stream = base64.b64encode(response._container[0])
        return Response({'pdf': json.dumps(stream.decode('utf-8'))})


class ContractPageView(ModelViewSet):
    queryset = Contract.objects.all()
    serializer_class = ContractSerializer
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['contract_number', 'customer', 'project_number', 'contract_status', 'reserved_date',
                        'allow_tara', 'vehicles', 'supplier', 'start_date', 'end_date', "name"]

    def list(self, request, format=None):
        contracts = Contract.objects.all().order_by('-reserved_date')

        if request.GET.get('search') is not None:
            contracts = Contract.objects.filter( Q(contract_number__contains=request.GET.get('search') ) |
                                                Q( customer__name1__contains=request.GET.get('search')) |
                                                Q( supplier__supplier_name__contains=request.GET.get('search')))

        page_number = (request.GET.get('page') or 1)
        per_page = 20
        contracts_with_pagination = Paginator(contracts, per_page).page(page_number)

        contracts_count = contracts.count()

        total_pages = math.ceil(contracts_count / per_page)

        next_page = None
        print(total_pages)
        if int(page_number) < total_pages:
            next_page = 'exists'

        previous_page = None
        if int(page_number) > 1:
            previous_page = 'exists'


        ctr_list = []

        for obj in contracts_with_pagination:
            material_list = []

            forwarder_name = ''

            if obj.forwarders is not None:
                forwarder_name = obj.forwarders.name

            if obj.customer is not None:
                customer = {"name": obj.customer.name1, "id": obj.customer.id}
            else:
                customer = None

            for mat in obj.required_materials:
                try:
                    article = Article.objects.get(id=mat["material"])
                except:
                    article = None

                if article is not None:
                    article_obj = {
                        "id": article.id,
                        "name": article.name
                    }
                    materials_obj = {
                        "material": article_obj,
                        "agreed_value": int(mat["agreed_value"]) if mat["agreed_value"] else None,
                        "remaining": int(mat["agreed_value"]) - sum(
                            Transaction.objects.filter(
                                article_id=mat["material"], customer=obj.customer, contract_number=obj.contract_number)
                                .values_list("net_weight", flat=True)) if mat["agreed_value"] else None
                    }
                    material_list.append(materials_obj)
            ctr = {
                "contract_number": obj.contract_number,
                "name": obj.name,
                "customer": customer,
                "start_date": obj.start_date,
                "end_date": obj.end_date,
                "status": obj.status,
                "contract_status": obj.contract_status,
                "forwarders": obj.forwarders_id,
                "forwarders_name": forwarder_name,
                "reserved_date": obj.reserved_date,
                "required_materials": material_list,
                "vehicles": [{"id": i[0], "license_plate": i[1]} for i in
                             obj.vehicles.all().values_list("id", "license_plate")],
                "supplier": [{"id": supp.id, "name": supp.supplier_name} for supp in
                             obj.supplier.all()] if obj.supplier else None
            }
            if "contract_number" in request.GET:
                if request.GET["contract_number"] == ctr["contract_number"]:
                    return Response(ctr)
            ctr_list.append(ctr)



        return Response(OrderedDict([
            ('count', contracts_count),
            ('next', next_page),
            ('previous', previous_page),
            ('results', ctr_list)
        ]))



class ContractView(ModelViewSet):
    queryset = Contract.objects.all()
    serializer_class = ContractSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['contract_number', 'customer', 'project_number', 'contract_status', 'reserved_date',
                        'allow_tara', 'vehicles', 'supplier', 'start_date', 'end_date', "name"]

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def list(self, request, format=None):
        contracts = Contract.objects.all()
        ctr_list = []

        for obj in contracts:
            material_list = []

            forwarder_name = ''

            if obj.forwarders is not None:
                forwarder_name = obj.forwarders.name

            if obj.customer is not None:
                customer = {"name": obj.customer.name1, "id": obj.customer.id}
            else:
                customer = None

            for mat in obj.required_materials:
                try:
                    article = Article.objects.get(id=mat["material"])
                except:
                    article = None

                if article is not None:
                    article_obj = {
                        "id": article.id,
                        "name": article.name
                    }
                    materials_obj = {
                        "material": article_obj,
                        "agreed_value": int(mat["agreed_value"]) if mat["agreed_value"] else None,
                        "remaining": int(mat["agreed_value"]) - sum(
                            Transaction.objects.filter(
                                article_id=mat["material"], customer=obj.customer, contract_number=obj.contract_number)
                                .values_list("net_weight", flat=True)) if mat["agreed_value"] else None
                    }
                    material_list.append(materials_obj)
            ctr = {
                "contract_number": obj.contract_number,
                "name": obj.name,
                "customer": customer,
                "start_date": obj.start_date,
                "end_date": obj.end_date,
                "status": obj.status,
                "contract_status": obj.contract_status,
                "forwarders": obj.forwarders_id,
                "forwarders_name": forwarder_name,
                "reserved_date": obj.reserved_date,
                "required_materials": material_list,
                "vehicles": [{"id": i[0], "license_plate": i[1]} for i in
                             obj.vehicles.all().values_list("id", "license_plate")],
                "supplier": [{"id": supp.id, "name": supp.supplier_name} for supp in
                             obj.supplier.all()] if obj.supplier else None
            }
            if "contract_number" in request.GET:
                if request.GET["contract_number"] == ctr["contract_number"]:
                    return Response(ctr)
            ctr_list.append(ctr)
        return Response(ctr_list)

    def create(self, request):

        serializer = ContractSerializer(data=request.data,
                                        context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object().contract_number
        obj = Contract.objects.get(contract_number=instance)
        material_list = []

        forwarder_name = ''

        if obj.forwarders is not None:
            forwarder_name = obj.forwarders.name

        for mat in obj.required_materials:
            if mat.get('material') is not None:
                if mat['agreed_value'] is None:
                    mat['agreed_value'] = 0
                try:
                    article = Article.objects.get(id=mat["material"])
                except:
                    article = None

                if article is not None:
                    article_obj = {
                        "id": article.id,
                        "name": article.name
                    }
                    materials_obj = {
                        "material": article_obj,
                        "agreed_value": int(mat["agreed_value"]),
                        "remaining": int(mat["agreed_value"]) - sum(
                            Transaction.objects.filter(
                                article_id=mat["material"], customer=obj.customer, contract_number=obj.contract_number)
                                .values_list("net_weight", flat=True))
                    }
                    material_list.append(materials_obj)
        return Response({
            "contract_number": obj.contract_number,
            "customer": {"name": obj.customer.name1, "id": obj.customer.id} if obj.customer is not None else '',
            "start_date": obj.start_date,
            "end_date": obj.end_date,
            "name": obj.name,
            "status": obj.status,
            "contract_status": obj.contract_status,
            "forwarders": obj.forwarders_id,
            "forwarders_name": forwarder_name,
            "reserved_date": obj.reserved_date,
            "required_materials": material_list,
            "project_number": obj.project_number,
            "vehicles": [{"id": i[0], "license_plate": i[1], "self_tara": i[2]} for i in
                         obj.vehicles.all().values_list("id", "license_plate", "self_tara")],
            "supplier": [{"id": i[0], "name": i[1]} for i in
                         obj.supplier.all().values_list("id", "supplier_name")]
        })


class HandTransmitterView(ModelViewSet):
    queryset = HandTransmitter.objects.all()
    serializer_class = HandTransmitterSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'

    # authentication_classes = [BasicAuthentication]
    # permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        result_list = []
        for i in queryset:
            data = {}
            data["id"] = i.id
            data["devise_id"] = i.devise_id
            data["name"] = i.name
            data["combination"] = [i.combination.id, i.combination.ident]
            result_list.append(data)
        # page = self.paginate_queryset(queryset)
        # if page is not None:
        #     serializer = self.get_serializer(page, many=True)
        #     return self.get_paginated_response(serializer.data)

        # serializer = self.get_serializer(queryset, many=True)
        # print(serializer.data[])
        return Response(result_list)

    def create(self, request):
        serializer = HandTransmitterSerializer(
            data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class YardTicket(ViewSet):
    def list(self, request):
        return Response({'Format': {"plate_id": "1"}})

    def create(self, request, *args, **kwargs):
        try:
            plate_id = request.data['plate_id']
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr_data = "PL" + str(plate_id)
            qr.add_data(qr_data)
            qr.make(fit=True)
            img = qr.make_image(fill_color="black", back_color="white")
            img.save("yms_yard_ticket.png")
            COOL_PDF_FILE = ("yms_yard_ticket.pdf")
            pdf = canvas.Canvas(COOL_PDF_FILE)
            pdf.setFont('Helvetica', 30)
            pdf.drawString(225, 800, "Hofticket")
            pdf.setFont('Helvetica', 30)
            pdf.drawString(235, 475, qr_data)
            pdf.setFont('Helvetica', 30)
            pdf.drawString(100, 425, "Bitte verwenden Sie diesen")
            pdf.setFont('Helvetica', 30)
            pdf.drawString(80, 375, "QR-Code für die Zweitwiegung")
            pdf.drawImage("yms_yard_ticket.png", 150, 500)
            pdf.save()

            img_string = None
            with open("yms_yard_ticket.pdf", "rb") as img_file:
                img_string = base64.b64encode(img_file.read()).decode("utf-8")
                img_string = str(img_string)

            os.remove("yms_yard_ticket.pdf")
            os.remove("yms_yard_ticket.png")
            if img_string != None:
                return Response({"status": True, "pdf_string": img_string, "details": None}, status=status.HTTP_200_OK)
            else:
                return Response({"status": False, "pdf_string": None, "details": None},
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            return Response({"status": False, "pdf_string": None, "details": str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PrintPdfDoc(ViewSet):
    def list(self, request):
        return Response({'Format': {"pdf_str": "base64"}})

    def create(self, request, *args, **kwargs):
        try:
            pdf_str = request.data['pdf_str']
            with open("yms.pdf", "wb") as f:
                f.write(codecs.decode(pdf_str.encode(), "base64"))
            file_name = "yms.pdf"
            if settings.WINDOWS:
                import win32api
                import win32print
                win32print.SetDefaultPrinter(settings.PRINTER)
                printer = win32print.GetDefaultPrinter()
                win32api.ShellExecute(0, "print", file_name, None, ".", 0)
            else:
                os.system(f"lp -d {settings.PRINTER} {file_name}")

            time.sleep(2)
            os.remove("yms.pdf")
            return Response({"status": True, "details": "printing done"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"status": False, "details": f"printing error ({str(e)})"},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MasterYardTicketView(ViewSet):
    def list(self, request):
        return Response({'Format': {"id": "12345"}})

    def create(self, request, *args, **kwargs):
        trans_id = request.data["id"]

        context = {}
        try:
            obj = Transaction.objects.get(id=trans_id)
            context['dataset'] = obj
        except Exception as e:
            return Response({'Error': e})
        context["absolute_url"] = "http://" + self.request.get_host()
        context['logo'] = Logo.objects.all().last()

        output = BytesIO()
        qr_img = qrcode.make(str(obj.id) + "H" + str(obj.contract_number.contract_number), box_size=5)
        qr_img = qr_img.resize((100, 100))
        qr_img.save(output, format='PNG')
        output.seek(0)
        output_s = output.read()
        b64 = base64.b64encode(output_s)
        context["qr"] = 'data:image/png;base64,{0}'.format(b64)

        res = Render.render('yard/yard_ticket_pdf.html', context)
        stream = base64.b64encode(res._container[0])
        data = {'pdf': json.dumps(stream.decode('utf-8'))}
        return Response(data, status=status.HTTP_200_OK)


class DriverIDView(ModelViewSet):
    queryset = DriverID.objects.all()
    serializer_class = DriverIDSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['ident_code', 'fahrzeug', 'spediteur', 'fahrer',
                        'always_valid', 'block', 'one_time']
    # permission_classes = [IsAuthenticated]


def gen(camera):
    video = cv2.VideoCapture(camera, cv2.CAP_FFMPEG)

    # video.open(camera, cv2.CAP_FFMPEG)
    print("streaming live feed of ", camera)
    while True:
        success, frame = video.read()
        if not success:
            video.release()
            break
        else:
            # Reduce image quality
            # frame = cv2.resize(frame, None, fx=0.5, fy=0.5, interpolation=cv2.INTER_AREA)
            # output_frame = cv2.resize(frame,(704,576),fx=0,fy=0, interpolation = cv2.INTER_CUBIC)
            ret, buffer = cv2.imencode('.jpg', frame)
            frame = buffer.tobytes()
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
            # ret, buffer = cv2.imencode('.jpg', frame)
            # frame = buffer.tobytes()
            # yield (b'--frame\r\n'
            #        b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')


# import threading
# class VideoCamera(object):
#     def __init__(self, url):
#         self.video = cv2.VideoCapture(url)
#         (self.grabbed, self.frame) = self.video.read()
#         threading.Thread(target=self.update, args=()).start()

#     def __del__(self):
#         self.video.release()

#     def get_frame(self):
#         image = self.frame
#         _, jpeg = cv2.imencode('.jpg', image)
#         return jpeg.tobytes()

#     def update(self):
#         while True:
#             (self.grabbed, self.frame) = self.video.read()

# def gen(camera):
#     while True:
#         frame = camera.get_frame()
#         yield (b'--frame\r\n'
#                b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n\r\n')

# url:"localhost:8000/camera_feed"
def camerafeed(request):
    ip_address = request.GET.get('ip')
    # cam = VideoCamera(f"rtsp://admin:admin100@{ip_address}:554/cam/realmonitor?channel=1&subtype=1")
    return StreamingHttpResponse(gen(f"rtsp://admin:admin100@{ip_address}:554/cam/realmonitor?channel=1&subtype=1"),
                                 content_type="multipart/x-mixed-replace;boundary=frame")


def camerafeed_type2(request):
    ip_address = request.GET.get('ip')
    # cam = VideoCamera(f"rtsp://admin:admin100@{ip_address}:554/substream")
    # return StreamingHttpResponse(gen(cam), content_type="multipart/x-mixed-replace;boundary=frame")
    return StreamingHttpResponse(gen(f"rtsp://admin:admin100@{ip_address}:554/substream"),
                                 content_type="multipart/x-mixed-replace;boundary=frame")


class OfficeNameTimingView(ModelViewSet):
    queryset = OfficeName.objects.all()
    serializer_class = OfficeNameTimingSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['ident_code', 'name']

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        grouping = request.query_params.get("group")
        if grouping is not None:
            data = []
            m = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            for office in serializer.data:
                office_data = {"id": office["id"],
                               "ident_code": office["ident_code"],
                               "name": office["name"],
                               "officetiming": {}}
                start_time = ""
                end_time = ""
                show_data = [day for day in office["officetiming"] if day["show"]]
                time_data = {}
                for day in show_data:
                    if str(day["day_name"]).lower() == "monday":
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("monday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["monday"]

                    elif str(day["day_name"]).lower() == "tuesday":
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("tuesday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["tuesday"]

                    elif str(day["day_name"]).lower() == "wednesday":
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("wednesday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["wednesday"]

                    elif str(day["day_name"]).lower() == "thursday":
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("thursday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["thursday"]

                    elif str(day["day_name"]).lower() == "friday":
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("friday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["friday"]

                    elif str(day["day_name"]).lower() == "saturday":
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("saturday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["saturday"]
                    else:
                        try:
                            time_data[f"{day['start_time'][:-3]} - {day['end_time'][:-3]}"].append("sunday")
                        except:
                            start_time = day["start_time"]
                            end_time = day["end_time"]
                            time_data[f"{start_time[:-3]} - {end_time[:-3]}"] = ["sunday"]

                    for i in time_data.keys():
                        time_data[i] = sorted(time_data[i], key=m.index)
                    office_data["officetiming"] = time_data
                data.append(office_data)
            return Response(data)

        return Response(serializer.data)


def users_without_auth(request):
    users = User.objects.filter(role="tabletuser")
    to_json = []
    for user in users:
        object = {'name': user.name, 'email': user.email}
        to_json.append(object)
    return HttpResponse(simplejson.dumps(to_json))


class RouteImageView(ModelViewSet):
    queryset = RouteImage.objects.all()
    serializer_class = RouteImageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['route_code']


class NotificationView(ModelViewSet):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['key', 'status']


class CustomAuthToken(ObtainAuthToken):

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user_id': user.id,
            'is_superuser': user.is_superuser,
            'is_staff': user.is_staff,
            'role': user.role
        })


class UserCreateAPIView(ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class generateKey:
    @staticmethod
    def returnValue(phone):
        return str(phone) + str(
            datetime.datetime.date(datetime.datetime.now())) + "ldsdsnfIDSOINFWLENFJLWN@#ER@#D@#D@#@#DQ#dionadn"


class getPhoneNumberRegistered(ViewSet):
    # Get to Create a call for OTP
    def list(self, request):
        query_data = request.query_params
        if "phone" in query_data:
            phone = query_data["phone"]
            try:
                Mobile = Phone.objects.get(Mobile=phone)
            except ObjectDoesNotExist:
                Phone.objects.create(
                    Mobile=phone,
                )
                Mobile = Phone.objects.get(Mobile=phone)
            Mobile.counter += 1
            Mobile.save()
            keygen = generateKey()
            key = base64.b32encode(keygen.returnValue(phone).encode())
            OTP = pyotp.HOTP(key, digits=4)
            print(OTP.at(Mobile.counter))
            # LANGUAGE = json.load(open(0os.path.join(settings.BASE_DIR / "lang.json"), encoding="utf-8"))
            try:
                requests.get(
                    f"{settings.SMS_URL}/cgi-bin/sms_send?username={settings.SMS_USERNAME}&password={settings.SMS_PASSWORD}&number={phone}&text={OTP.at(Mobile.counter)}",
                    timeout=5, verify=False)
            except:
                pass
            return Response({"OTP sent successfully"}, status=200)
        else:
            return Response("Please add 'phone' in params", status=400)

    # This Method verifies the OTP
    '''{
        "otp": 9813,
        "phone": 1234567
        }
    '''

    def create(self, request):
        req_data = request.data
        if "otp" in req_data and "phone" in req_data:
            phone = req_data["phone"]
            otp = req_data["otp"]
            try:
                Mobile = Phone.objects.get(Mobile=phone)
            except ObjectDoesNotExist:
                return Response("User does not exist", status=404)  # False Call

            keygen = generateKey()
            key = base64.b32encode(keygen.returnValue(phone).encode())  # Generating Key
            OTP = pyotp.HOTP(key, digits=4)  # HOTP Model
            if OTP.verify(otp, Mobile.counter):  # Verifying the OTP
                Mobile.isVerified = True
                Mobile.save()
                return Response("You are authorised", status=200)
        return Response("OTP is wrong", status=400)


class SendMessage(ViewSet):
    # Get to Create a call for OTP
    def list(self, request):

        return Response({
            "message": "demo msg",
            "mobile": "123456789",
            "lang": "de"
        }, status=200)

    # This Method verifies the OTP
    '''{
        "message": "demo msg",
        "mobile": "123456789",
        "lang": "de"
        }
    '''

    def create(self, request):
        req_data = request.data
        if "mobile" in req_data and "message" in req_data:
            mobile = req_data["mobile"]
            msg = req_data["message"]
            lang = req_data.get("lang")
            LANGUAGE = json.load(open(os.path.join(settings.BASE_DIR / "lang.json"), encoding="utf-8"))
            if lang is not None:
                try:
                    msg = LANGUAGE.get(lang).get('msg')
                except:
                    msg = LANGUAGE.get("DE").get('msg')
            else:
                msg = LANGUAGE.get("DE").get('msg')

            print(msg)
            try:
                requests.get(
                    f"{settings.SMS_URL}/cgi-bin/sms_send?username={settings.SMS_USERNAME}&password={settings.SMS_PASSWORD}&number={mobile}&text={msg}",
                    timeout=5, verify=False)
            except Exception as e:
                return Response({"status": False, "detail": str(e)}, status=500)
            return Response({"status": True}, status=200)
        return Response({"status": False}, status=400)


class DisplayTexttView(ViewSet):
    def list(self, request):
        object, _ = DisplayText.objects.get_or_create(id=1)
        data = {
            "text": object.text,
            "updated_at": object.updated_at
        }
        return Response(data, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        try:
            text = request.data.get("text")
            if text is not None:
                object, _ = DisplayText.objects.get_or_create(id=1)
                object.text = text
                object.save()
            data = {
                "text": object.text,
                "updated_at": object.updated_at
            }
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                "error": str(e)
            }
            return Response(data, status=500)


class LoadingBoxView(ModelViewSet):
    queryset = LoadingBox.objects.all()
    serializer_class = LoadingBoxSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["loading_box_number", "status"]


class MaxVehicleView(ModelViewSet):
    queryset = MaxVehicle.objects.all()
    serializer_class = MaxVehicleSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = '__all__'


class GetSampleYardTicketView(ViewSet):

    def list(self, request):
        return Response({"yard_ticket": "str"})

    def create(self, request):
        '''
        {
            yard_ticket : str,
        }
        '''
        data = request.data
        try:
            yard_ticket = data["yard_ticket"]

        except Exception as e:

            return Response({"details": str(e) + " not found"}, status=status.HTTP_406_NOT_ACCEPTABLE)

        res = Transaction.objects.get(id=yard_ticket)

        num = "PL" + str(res.vehicle.id)
        context = {}
        output = BytesIO()
        im_logo = Image.open(str(settings.BASE_DIR) + "/yard/static/yard/images/logo.png")  # Your image here!
        im_logo = im_logo.resize((300, 100))
        im_logo.save(output, format='PNG')
        output.seek(0)
        output_s = output.read()
        b64 = base64.b64encode(output_s)
        context["logo"] = 'data:image/png;base64,{0}'.format(b64)

        output = BytesIO()
        qr_img = qrcode.make(num, box_size=5)
        # qr_img = Image.open(img)
        qr_img = qr_img.resize((90, 90))
        qr_img.save(output, format='PNG')
        output.seek(0)
        output_s = output.read()
        b64 = base64.b64encode(output_s)
        context["qr"] = 'data:image/png;base64,{0}'.format(b64)
        context["absolute_url"] = "http://" + self.request.get_host()

        context["dataset"] = res
        date = str(context["dataset"].created_date_time.date()).split("-")
        date = date[-1] + "." + date[1] + "." + date[0]
        time = str(context["dataset"].created_date_time.time())[:5]

        context["created_datetime"] = date + " " + time

        images = RouteImage.objects.get(route_code=1)
        context["image"] = images.image

        res = Render.render('yard/sample_yard_ticket_pdf.html', context)
        stream = base64.b64encode(res._container[0])
        data = {'pdf': json.dumps(stream.decode('utf-8'))}
        return Response(data, status=status.HTTP_200_OK)


from PyPDF2 import PdfFileWriter, PdfFileReader
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.utils import ImageReader
from reportlab.lib.units import mm
from PIL import Image
import cv2


class GetCMR(ViewSet):

    def list(self, request):
        return Response({"transaction_id": "str"})

    def create(self, request):
        '''
        {
            transaction_id : str,
        }
        '''
        data = request.data
        try:
            transaction_id = data["transaction_id"]

        except Exception as e:

            return Response({"details": str(e) + " not found"}, status=status.HTTP_406_NOT_ACCEPTABLE)

        driver_signature = DriverSignature.objects.filter(transaction_id=transaction_id).last()
        print(driver_signature.image)

        packet = io.BytesIO()
        can = canvas.Canvas(packet, pagesize=letter)
        signature_path = str(settings.BASE_DIR) + "/media/" + str(driver_signature.image)
        img = cv2.imread(signature_path, cv2.IMREAD_GRAYSCALE)
        thresh = 110
        img = cv2.threshold(img, thresh, 255, cv2.THRESH_BINARY)[1]
        img = Image.fromarray(img)
        img = img.convert("RGBA")
        pixdata = img.load()
        width, height = img.size
        for y in range(height):
            for x in range(width):
                if pixdata[x, y] == (255, 255, 255, 255):
                    pixdata[x, y] = (255, 255, 255, 0)
        img.save(signature_path, "PNG")
        logo = ImageReader(signature_path)
        can.drawImage(logo, 300, 190, 30 * mm, 15 * mm, mask="auto")

        can.save()

        # move to the beginning of the StringIO buffer
        packet.seek(0)

        # create a new PDF with Reportlab
        new_pdf = PdfFileReader(packet)
        # read your existing PDF
        input_pdf_path = str(settings.BASE_DIR) + "/CMR.pdf"
        f = open(input_pdf_path, "rb")
        existing_pdf = PdfFileReader(f)
        output = PdfFileWriter()
        # add the "watermark" (which is the new pdf) on the existing page

        for num in range(existing_pdf.numPages):
            page = existing_pdf.getPage(num)
            page.mergePage(new_pdf.getPage(0))
            output.addPage(page)
        # finally, write "output" to a real file
        # outputStream = open(output_pdf_path, "wb")
        # output.write(outputStream)
        # outputStream.close()
        # f.close()

        output_buffer = io.BytesIO()
        output.write(output_buffer)
        stream = base64.b64encode(output_buffer.getvalue())
        data = {'pdf': json.dumps(stream.decode('utf-8'))}
        return Response(data, status=status.HTTP_200_OK)

{% load i18n %}
{%block head%}
  {%load static%}

{%endblock%}
{% block content %}

<div class="row" >  
<div class="col-12">
      <form class="form-group" method="POST" action="{% url 'weight_page_set' %}" enctype="multipart/form-data">
          <div class="row" >
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-6">    
                    <div class=" text-right">
                      
                    {% csrf_token %}
                      <div class="md-form mt-2 mb-4">
                        <div class="mb-4">{% translate 'Forwarder'%}</div>
                      </div>
                      <hr>
                      <div class="md-form mt-3 mb-4">
                        <div class="mb-4">{% translate 'Customer' %}</div>
                      </div>
                      <hr>
                      <div class="md-form mt-4 mb-4">
                        <div class="mb-4">{% translate 'Material' %}</div>
                      </div>
                      <hr>
                      <div class="md-form mt-4 mb-4">
                        <div class="mb-4">{% translate 'Supplier' %}</div>
                      </div>
                          <hr>
                      <div class="md-form mt-4 mb-4">
                        <div class="mb-4">Info</div>
                      </div>
                      
                    </div><hr>
          </div>
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-6">
                  <div class=" text-left">
                    <div class="md-form mb-2">
                      <div class="mb-2">
                        <input type="checkbox" name="f_1" id="forwarder" {% if wps.forwarder == 1 or wps.forwarder == 2 %} checked {% endif%}><button type='button' class="btn btn-success ml-2" disabled>{% translate "Visible" %}</button>
                        <input type="checkbox" name="f_2" id="forwarder" class="ml-3" {% if wps.forwarder == 0 %} checked {% endif%}><button type='button' class="btn btn-danger ml-2" disabled>{% translate "Hide"%}</button>
                        <input type="checkbox" name="f_3" id="forwarder_1" class="ml-3" {% if wps.forwarder == 2  %} checked {% endif%}><button type='button' class="btn btn-info ml-2" disabled>{% translate "Mandatory" %}</button>
                      </div>
                    </div>
                    <hr>
                    <div class="md-form mb-2">
                      <input type="checkbox" name="k_1" id="customer" {% if wps.customer == 1 or wps.customer == 2%} checked {% endif%}><button type='button' class="btn btn-success ml-2" disabled>{% translate "Visible" %}</button>
                      <input type="checkbox" name="k_2" id="customer" class="ml-3" {% if wps.customer == 0 %} checked {% endif%} ><button type='button' class="btn btn-danger ml-2" disabled>{% translate "Hide" %}</button>
                      <input type="checkbox" name="k_3" id="customer_1" class="ml-3" {% if wps.customer == 2 %} checked {% endif%} ><button type='button' class="btn btn-info ml-2" disabled>{% translate "Mandatory"%}</button>
                    </div>
                    <hr>
                    <div class="md-form mb-2">
                      <input type="checkbox" name="m_1" id="material" {% if wps.material == 1 or wps.material == 2 %} checked {% endif%}><button type='button' class="btn btn-success ml-2" disabled>{% translate "Visible"%}</button>
                      <input type="checkbox" name="m_2" id="material" class="ml-3" {% if wps.material == 0 %} checked {% endif%} ><button type='button' class="btn btn-danger ml-2" disabled>{% translate "Hide"%}</button>
                      <input type="checkbox" name="m_3" id="material_1" class="ml-3" {% if wps.material == 2 %} checked {% endif%} ><button type='button' class="btn btn-info ml-2" disabled>{% translate "Mandatory"%}</button>
                    </div>
                    <hr>
                    <div class="md-form mb-2">
                      <input type="checkbox" name="s_1" id="supplier" {% if wps.supplier == 1 or wps.supplier == 2 %} checked {% endif%}><button type='button' class="btn btn-success ml-2" disabled>{% translate "Visible"%}</button>
                      <input type="checkbox" name="s_2" id="supplier" class="ml-3" {% if wps.supplier == 0 %} checked {% endif%} ><button type='button' class="btn btn-danger ml-2" disabled>{% translate "Hide"%}</button>
                      <input type="checkbox" name="s_3" id="supplier_1" class="ml-3" {% if wps.supplier == 2 %} checked {% endif%} ><button type='button' class="btn btn-info ml-2" disabled>{% translate "Mandatory"%}</button>
                    </div>
                  <hr>
                  <div class="md-form mb-2">
                      <input type="checkbox" name="c_1" id="comment" {% if wps.comment == 1 or wps.supplier == 2 %} checked {% endif%}><button type='button' class="btn btn-success ml-2" disabled>{% translate "Visible"%}</button>
                      <input type="checkbox" name="c_2" id="comment" class="ml-3" {% if wps.comment == 0 %} checked {% endif%} ><button type='button' class="btn btn-danger ml-2" disabled>{% translate "Hide"%}</button>
                      <input type="checkbox" name="c_3" id="comment_1" class="ml-3" {% if wps.comment == 2 %} checked {% endif%} ><button type='button' class="btn btn-info ml-2" disabled>{% translate "Mandatory"%}</button>
                    </div>
                  </div><hr>
        </div>
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 text-center">
        <button type="submit" class="btn btn-primary text-right">{% translate "Save"%}</button>
        
        </div>
        </div>
        </form>
</div>        
</div>
<script type="text/javascript">

$('input#forwarder').on('change', function() {
  $('input#forwarder').not(this).prop('checked', false);
  forward();
  });
  
function forward(){
    if($('input[name=f_1]').prop('checked') == true){
        $('input#forwarder_1').prop('checked', true);
        $('input#forwarder_1').attr('disabled', false);
    } else {
        $('input#forwarder_1').prop('checked', false);
        $('input#forwarder_1').attr('disabled', true);
    }
};
//////////////////////////////////////////////////////////////

$('input#customer').on('change', function() {
  $('input#customer').not(this).prop('checked', false);
  customer();
  });
  
function customer(){
    if($('input[name=k_1]').prop('checked') == true){
        $('input#customer_1').prop('checked', true);
        $('input#customer_1').attr('disabled', false);
    } else {
        $('input#customer_1').prop('checked', false);
        $('input#customer_1').attr('disabled', true);
    }
};
//////////////////////////////////////////////////////////////

$('input#material').on('change', function() {
  $('input#material').not(this).prop('checked', false);
  material();
  });
  
function material(){
    if($('input[name=m_1]').prop('checked') == true){
        $('input#material_1').prop('checked', true);
        $('input#material_1').attr('disabled', false);
    } else {
        $('input#material_1').prop('checked', false);
        $('input#material_1').attr('disabled', true);
    }
};
//////////////////////////////////////////////////////////////

$('input#supplier').on('change', function() {
  $('input#supplier').not(this).prop('checked', false);
  supplier();
  });
  
function supplier(){
    if($('input[name=s_1]').prop('checked') == true){
        $('input#supplier_1').prop('checked', true);
        $('input#supplier_1').attr('disabled', false);
    } else {
        $('input#supplier_1').prop('checked', false);
        $('input#supplier_1').attr('disabled', true);
    }
};
//////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////

$('input#comment').on('change', function() {
  $('input#comment').not(this).prop('checked', false);
  comment();
  });

function comment(){
    if($('input[name=c_1]').prop('checked') == true){
        $('input#comment_1').prop('checked', true);
        $('input#comment_1').attr('disabled', false);
    } else {
        $('input#comment_1').prop('checked', false);
        $('input#comment_1').attr('disabled', true);
    }
};
//////////////////////////////////////////////////////////////

{% endblock%}
{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block head %}
{%load static%}
{% endblock %}
{% block content %}
    <!-- /#sidebar-wrapper -->
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              <p>{% translate 'Site List' %}</p>
            </div>
          </div>
        </div>
        
<!--         <div class="container">
          <form method="POST" enctype="multipart/form-data"  id="form_date_selection" class="row">
            {% csrf_token %}
          <div class="col-xl-4 col-lg-4 col-md-5 col-sm-12 col-12">
             <div class="form-group">
                <div class='input-group date' id='datetimepicker6'>
                  <label class="mt-2 mr-3">Date</label>
                   <input type='date' class="form-control" id='del_from_date' name="fromdate" />
                   <span class="input-group-addon">
                   <span class="glyphicon glyphicon-calendar"></span>
                   </span>
                </div>
             </div>
          </div>
          <div class="col-xl-4 col-lg-4 col-md-5 col-sm-12 col-12">
             <div class="form-group">
                <div class='input-group date' id='datetimepicker7'>
                  <label class="mt-2 mr-3">To</label>
                   <input type='date' class="form-control" id="del_to_date" name="todate"/>
                   <span class="input-group-addon">
                   <span class="glyphicon glyphicon-calendar"></span>
                   </span>
                </div>
             </div>
          </div>
          <div class="col-xl-4 col-lg-4 col-md-5 col-sm-12 col-12">
            <button type="submit" class="btn btn-primary" id="btn_date_selection" name="date_selection">Filter</button>
          </div>
          </form>
       </div> -->

        <!--table strat-->
        <div class="row">
           <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12" >
              <label>{% translate "Show entries" %}:</label>
             <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
             </select>
           </div>
           <div class="col-xl-2 col-lg-2 col-md-12 col-sm-12 col-12">
               <label>{% translate "Search" %}:</label>
               <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
           </div>

            <div class="col-xl-2 col-lg-2 col-md-12 col-sm-12 col-12">
                <form action="" id="material-direction-form" method="post">
              {% csrf_token%}
               <label>Externes Wiegen:</label>
               <input  type="checkbox" name="external_weighing" class="form-control mr-sm-2">
                </form>
           </div>
           <div class="col-xl-2 col-lg-2 col-md-12 col-sm-12 col-12" >
            <label>{% translate "Richtung" %}:</label>
            <form action="" id="material-direction-form" method="post">
              {% csrf_token%}
             <select class="form-control w-30" id="showentries" name="material-direction" id="material-direction" >
                <option value="">{% translate "All" %}</option>
                <option value="0">{% translate "Eingang" %}</option>
                <option value="1">{% translate "Ausgang" %}</option>
                 <option value="2">{{ "Fremdwiegung" }}</option>
             </select>
            </form>
           </div>
           <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12" style="margin-top: 28px;">
            <button type="submit" class="btn btn-primary mt-2" onclick="filterdata()">{% translate 'Filter' %}</button>
           </div>
        </div>
        <div class="row">
        <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="deliveryNoteTable">
          <thead>
            <tr> {% if request.user.is_superuser%}
              <th>{% translate 'Action' %}</th> {% endif %}
              <th>{% translate 'Lfs.-Nr.' %}</th>
              {% if master_container.status == False %}
              <th>{{ "Kennzeichen" }}</th>
              <th>{% translate 'Material' %}</th>
              <th>{% translate 'Customer' %}</th>
              <th>{{ "Baustelle" }}</th>
              {% else %}
                <th>Containernummer</th>
                <th>{% translate 'Material' %}</th>
                <th>Firma</th>
                <th>Gebäude</th>
              {% endif %}
              
            <th>{{ "Lieferant" }}</th>
             <th>{{ "Spedition" }}</th>
              <th>{{ "Erstgewicht" }} (kg)</th>
{#              <th>{% translate 'Second Weight' %} (kg)</th>#}
{#              <th>{% translate 'Net Weight' %} (kg)</th>#}
              <th>{% translate 'Updated on' %}</th>
            </tr>
          </thead>
          <tbody class="mt-4">
            {% for data in dataset %}

            <tr>
              <td>
                 <a href="javascript:loadHoflisteDeliveryNoteDetails('{{ data.id }}')" ><i class="fas fa-pencil-alt text-primary  ml-4"></i></a>
                   {% if request.user.is_superuser  %}
                <a class="confirmdelete" href="{% url 'site_list_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a>
              </td> {% endif %}
              {% if data.lfd_nr is not None and data.lfd_nr != "" %}
                  <td>{{ data.lfd_nr }}</td>
              {% else %}
                  <td>{{ data.id }}</td>
              {% endif %}
              <td>{{ data.vehicle|default_if_none:"-" }}</td>
              <td>{{ data.article|default_if_none:"-" }}</td>
              <td>{{ data.customer|default_if_none:"-" }}</td>
              <td>{{ data.supplier|default_if_none:"-" }}</td>
             <td>{{ data.place_of_delivery|default_if_none:"-" }}</td>
             <td>{{ data.forwarders|default_if_none:"-" }}</td>
              <td>{{ data.first_weight|default_if_none:"-" }}</td>
{#              <td>{{ data.second_weight|default_if_none:"-" }}</td>#}
{#              <td>{{ data.net_weight|default_if_none:"-" }}</td>#}
              <td>{{ data.updated_date_time|default_if_none:"-" }}</td>
            </tr>
            {% endfor %}
<!--            <tfoot hidden>-->
<!--               <tr>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text"class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text"class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--                 <th rowspan="1" colspan="1">-->
<!--                    <input type="text" class="form-control">-->
<!--                 </th>-->
<!--               </tr>-->
<!--            </tfoot>-->
          </tbody>
        </table>
      </div>

        <div class="container" >
      <div class="row mb-5">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12" >
          <!-- Material form login -->
          <div class="card" id="div_delivery_form">
            <h5 class="card-header info-color white-text py-3 mt-4">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18" class="" aria-expanded="true">
                    <div class="row">
                      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 text-left">
                        <p class="mb-0 pt-2 text-color text_color">{% translate 'Site List' %}</p>
                      </div>
                    </div>
                  </a>
                </h4>
              </div>
            </h5>

            <div id="collapse_18" class="collapse show" style="">
              <div class="panel-body">
                 <div class="contanier">
                <form class="form-group" method="POST" enctype="multipart/form-data" id="form_delivery_note" target="_blank">
                  {% csrf_token %}
                      <input type="hidden" name="id" id="id">
                      <input type="hidden" name="firstw_alibi_nr" id="id_firstw_alibi_nr">
                      <input type="hidden" name="firstw_date_time" id="id_firstw_date_time">
                      <input type="hidden" name="secondw_date_time" id="id_secondw_date_time">
                      <input type="hidden" name="vehicle_weight_flag" id="id_vehicle_weight_flag">
                      <input type="hidden" name="trans_flag" id="id_trans_flag">
                    <input type="hidden" name="second_weight" value="0" >
                      <input type="hidden" name="net_weight" value="0" >
                      <input type="hidden" name="price_per_item" id="id_article_price" >
                <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  <div class="md-form mb-3">
                    {% if master_container.status == False %}
                    <label>{{ "Kennzeichen" }}</label>
                    {% else %}
                    <label>Containernummer</label>
                    
                    {% endif %}
                    {{ form.vehicle}}
                  </div>
                  <div class="md-form mb-3">
                    {% if master_container.status == False %}
                    <label>{% translate 'Customer' %}</label>
                    {% else %}
                    <label>Firma </label>
                    
                    {% endif %}
                    
                    {{ form.customer}}
                  </div>

                  <div class="md-form mb-3">
                    <label>{{ "Erstgewicht" }} [kg]</label>
                    {{ form.first_weight}}
                  </div>

                      <div class="md-form mb-3">
                    <label>{{ "Lieferant" }}</label>
                    {{ form.place_of_delivery}}
                  </div>
{#                  <div class="md-form mb-3">#}
{#                    <label>{% translate 'Second Weight' %}</label>#}
{#                    {{ form.second_weight}}#}
{#                  </div>#}
                   <!-- <div class="md-form mb-3"> -->
                    <!-- </div> -->
                </div>
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  <div class="md-form mb-3">
                    <label>{% translate 'Material' %}</label>
                    {{ form.article}}
                  </div>
                  <div class="md-form mb-3">
                    {% if master_container.status == False %}
                    <label>{% translate 'Supplier' %}</label>
                    {% else %}
                    <label>Gebäude </label>
                    
                    {% endif %}
                    
                    {{ form.supplier}}
                  </div>
                      <div class="md-form mb-3">
                    <label>{{ "Spedition" }}</label>
                    {{ form.forwarders}}
                  </div>
                       <div class="md-form mb-3">
                    <label>{{ "Richtung" }}</label>
                       <select id='id_richtung' name='status' class='form-control'
                                                            style="font-size:15px;">
                                                        <option value='0'> Eingang</option>
                                                        <option value='1'> Ausgang</option>
                                                        <option value='2'>Fremdwiegung</option>
                                                    </select>
                  </div>
{#                  <div class="md-form mb-3">#}
{#                    <label>{% translate 'Net Weight' %}</label>#}
{#                    {{ form.net_weight}}#}
{#                  </div>#}
{#                    <div class="md-form mb-3">#}
{#                    <label>{% translate 'Alibi Nr.' %}</label>#}
{#                    <input type="text" name="secondw_alibi_nr" id="id_secondw_alibi_nr" class="form-control" readonly>#}
{#                  </div>#}

                  </div>
                    <a id="load_images_btn" class="btn btn-primary ml-2" style="color: white;" > {% translate 'View Images' %}</a>
                    <button id="save_delivery_note" name="save_button"  class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate 'Save2' %}</button>
                    <button id="print_delivery_note" name="print_button" class="btn btn-primary ml-2"><i class="fas fa-print ml-2"></i> {% translate 'Print' %}</button>
                    <button id="cancel_delivery_note" class="btn btn-primary ml-2"> {% translate 'Cancel' %}</button>
                </div>

                </form>

              </div></div>
            </div>

          </div>

        </div>
      </div>
    </div>
    </div>
    <div id="MyModal" style="overflow-y: auto;" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content  p-4">
                <div class="modal-body">
                    <img class="img-responsive" src="" style="width:100%"/>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div id="MyPopupImg" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
          <div class="modal-dialog modal-lg">
              <!-- Modal content-->
              <div class="modal-content p-4">
                  <div class="modal-header modal-header-custom">                  
                      <h4 class="modal-title">{% translate 'Captured Images' %}
                      </h4>
                      <button type="button" class="close" data-dismiss="modal">
                        &times;</button>
                  </div>
                  <div class="modal-body">
                    <center> </center>
                  </div>
                  <div class="modal-footer">
                      <input type="button" id="btnClosePopup" value="Close" class="btn btn-secondary" />
                  </div>
              </div>
          </div>
      </div>
{% endblock %}
{% block scripts %}
<script src="{% static 'stats/js/stats_custom.js'%}"></script>
<script>
  
  filterdata = () => {
    $("#material-direction-form").submit();
  }
</script>
<script>
    // DataTable
    $(document).ready(function() {
      
      var table = $('#deliveryNoteTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
                   console.log(that)
// <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
// <!--                       if ( that.search() !== this.value ) {
// <!--                           that.search( this.value ).draw();
// <!--                       }
// <!--                   } );
               } );
           }
          });

      $("#deliveryNoteTable_filter").hide()
      $("#deliveryNoteTable_previous").html("Zurück");
      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      } );

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      } );

 });
 
</script>
{% endblock %}


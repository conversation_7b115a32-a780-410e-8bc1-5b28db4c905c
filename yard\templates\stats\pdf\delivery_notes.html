{% load i18n %}
<!DOCTYPE html>
<html lang="en">
  <head>
    {%block head%}
    {%load static%}
    {%load custom_template_filters%}
    {%endblock%}
    {%load custom_template_filters%}
    <meta charset="utf-8">
    <title>Statitics</title>
    <style>
      table { -pdf-keep-with-next: false; }
      p { margin: 0; -pdf-keep-with-next: true; }
      p.separator { -pdf-keep-with-next: false; font-size: 6pt; }
      table { page-break-inside:auto }
      .group_heading {
          font-size: xxx-large;
          font-weight: bolder
      }
      .sub_group_heading {
          font-size: x-large;
          font-weight: bold;
          padding-left: 10px
      }
      
      thead tr{
          border-bottom: 0.5px solid gray;
      }
      *,td{
          font-size: 8px;
      }
    </style>
  </head>
  <body>
    <div class="a4_sheet">
      <div></div>
      <div>&nbsp;</div>
      <div style="width:100%;">
        <div style="width:50%;padding-right: 10px;padding-left: 10px;">
          <label style="text-align: right;padding-right:10px;">
          {% translate 'Date' %}:&nbsp;&nbsp;{{date|date:"d.m.Y"}}</label>
          <div><br></div>
        </div>
        
          <div><br></div>
      
        <div style="width:50%;padding-right: 10px;padding-left: 10px;"> 
          {% translate 'From' %}:&nbsp;&nbsp;{{from}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{% translate 'To' %}:&nbsp;&nbsp;{{to}}
        </div>
        <div><br></div>
      </div>
      
        <div>
            <table  width="100%" cellpadding="2" style="border-collapse: collapse;text-align: center;">
                <thead>
                  <tr> 
                    <th style="width: 5%;">{% translate 'Lfs.Nr.' %}</th>
                    <th>{% translate 'Code' %} .1</th>
                    <th>Material</th>
                    <th>Kunde</th>
                    <th>Baustelle</th>
                    <th>{% translate 'Erstgewicht' %} (kg)</th>
                    <th>{% translate 'Zweitgewicht ' %} (kg)</th>
                    <th>{% translate 'Net Weight' %} (kg)</th>
                    <th>{% translate 'Status' %}</th>
                    <th>{% translate 'Updated on' %}</th>
                  </tr>
                </thead>
                <tbody class="mt-4">
                  {% for data in dataset %}
                  <tr >
                    {% if data.lfd_nr is not None and data.lfd_nr != "" %}
                    <td>{{ data.lfd_nr }}</td>
                    {% else %}
                    <td>{{ data.id }}</td>
                    {% endif %}
                    <td class="vehicleName{{data.id}}">{{ data.vehicle|default_if_none:" " }}</td>
                    <td>{{ data.article|default_if_none:" " }}</td>
                    <td>{{ data.customer|default_if_none:" " }}</td>
                    <td>{{ data.supplier|default_if_none:" " }}</td>
                    <td>{{ data.first_weight|default_if_none:"0" }}</td>
                    <td>{{ data.second_weight|default_if_none:"0" }}</td>
                    <td>{{ data.net_weight|default_if_none:"0" }}</td>
                    <td>
                    {% if data.status is None %}
                        -
                    {% elif data.status == "0" %}
                        Eingang
                    {% elif data.status == "1" %}
                        Ausgang
                    {% else %}
                        {{data.status}}
                    {% endif %}

                    </td>
                    <td>{{ data.updated_date_time|default_if_none:" " }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
        </div>
    </div>
  </body>
</html>
# Generated by Django 3.1.1 on 2021-07-13 13:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0047_contract'),
    ]

    operations = [
        migrations.CreateModel(
            name='AutoCapture',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=False)),
            ],
        ),
        migrations.AlterField(
            model_name='contract',
            name='construction_site',
            field=models.ManyToManyField(blank=True, to='yard.BuildingSite'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='signature',
            field=models.FileField(blank=True, upload_to='contract_signatures/'),
        ),
    ]

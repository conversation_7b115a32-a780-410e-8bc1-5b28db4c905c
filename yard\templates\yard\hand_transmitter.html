{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{% load i18n %}
{% block head %}
    {% load static %}
{% endblock %}
{% block content %}
    <!-- /#sidebar-wrapper -->
    <div class="container">
    <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
        <i class="fas fa-align-justify"></i>
    </button>
    <!-- breadcrumb -->
    <style>
        ol li a:hover {
            color: #dc3545 !important;
        }
    </style>
    <div>
        <nav style="--bs-breadcrumb-divider: '>';" aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/" class=" text-muted">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">{% translate 'Hand Sender' %}</li>
            </ol>
        </nav>
    </div>
    <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
                <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heading">
                <p>{% translate 'Handsender' %}</p>
            </div>
        </div>
    </div>

    <!--table strat-->
    <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <label>{% translate "Show entries" %}:</label>
            <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
            </select>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <label>{% translate "Search" %}:</label>
            <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search"
                   id="mysearch">
        </div>
    </div>
    <div class="row">
        <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%"
               id="deliveryNoteTable">
            <thead>
            <tr>
                <th>{% translate 'Action' %}</th>
                <th>{% translate 'Name' %}</th>
                <th>{% translate 'ID' %}</th>
                <th>{% translate 'Geräte-ID' %}</th>
            </tr>
            </thead>
            <tbody class="mt-4">
            {% for data in data %}
                <tr class="loadCSD" ondblclick="javascript:loadHandTransmitterDetails('{{ data.id }}')">
                    <td><a class="loadCS" href="javascript:loadHandTransmitterDetails('{{ data.id }}')"><i
                            class="fas fa-pencil-alt text-primary  ml-4"></i></a>
                        <a class="confirmdelete" href="{% url 'hand_transmitter_delete' identifier=data.id %}"><i
                                class="fas fa-trash-alt ml-2 text-danger"></i></a> &nbsp;
                    </td>
                    <td>{{ data.name|default_if_none:"-" }}</td>
                    <td>{{ data.combination|default_if_none:"-" }}</td>
                    <td>{{ data.devise_id|default_if_none:"-" }}</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="container">
        <div class="row mb-5">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

                <!-- Material form login -->
                <div class="card mt-4" id="entry">
                    <div class="row">
                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                            <h5 class="card-header info-color white-text py-3">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse2031"> {% endcomment %}
                                        <div class="row">
                                            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                                                <p class="mb-0 pt-2 mr-4 text-color text_color float-left">
                                                    {% translate "Handsender" %}
                                                </p>
                                                <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill"
                                                        style="float:right;">Neue Eingabe
                                                </button>

                                            </div>
                                        </div>
                                        {% comment %} </a> {% endcomment %}
                                    </h4>
                                </div>
                            </h5>
                        </div>
                    </div>


                    <!--first forms satrat-->
                    <div id="collapse2031" class="collapse">
                        <div class="panel-body">
                            <div class="contanier">
                                <form class="form-group" method="POST" enctype="multipart/form-data">
                                    {% csrf_token %}
                                    <input type="hidden" name="id" id="id" value="">
                                    <div class="row">
                                        <div class="col-xl-4 col-lg-4 text-left mt-4">
                                            <div>
                                                <label>{{ form.name.label }}</label>
                                                {{ form.name }} {{ form.name.errors }}
                                            </div>

                                        </div>
                                        <div class="col-xl-4 col-lg-4 text-left mt-4">
                                            <div>
                                                <label>{{ form.combination.label }}</label>
                                                {{ form.combination }} {{ form.combination.errors }}
                                            </div>


                                        </div>
                                        <div class="col-xl-4 col-lg-4 text-left mt-4">
                                            <div>
                                                <label>{{ form.devise_id.label }}</label>
                                                {{ form.devise_id }} {{ form.devise_id.errors }}
                                            </div>

                                        </div>
                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                            <button id="submit" type="submit" class="btn btn-primary mr-3 mt-3"><i
                                                    class="fas fa-save ml-2"></i>{% translate "Save" %}</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block scripts %}
    <script src="{% static 'stats/js/stats_custom.js' %}"></script>

    <script>
        // DataTable
        $(document).ready(function () {
            var table = $('#deliveryNoteTable').DataTable(
                {
                    "bLengthChange": false,
                    initComplete: function () {
                        // Apply the search
                        this.api().columns().every(function () {
                            var that = this;
                            console.log(that)
                            <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
                            <!--                       if ( that.search() !== this.value ) {-->
                            <!--                           that.search( this.value ).draw();-->
                            <!--                       }-->
                            <!--                   } );-->
                        });
                    }
                });

            $("#deliveryNoteTable_filter").hide()

            // custom search filter
            $('#mysearch').on('keyup', function () {
                table.search(this.value).draw();
            });

            //  custom show entries
            $('#showentries').change(function () {
                table.page.len(this.value).draw();
            });

        });

        $('#id_tara_with_mobile').click(function () {
            if ($('#id_tara_with_mobile').is(':checked')) {
                $('#id_tara_with_mobile').val(true)
            } else {
                $('#id_tara_with_mobile').val("false")

            }
        });

        $(".loadCS").click(function () {
            $("#collapse2031").addClass('show');
            window.location = "#entry";
        });

        $(".loadCSD").dblclick(function () {
            $("#collapse2031").addClass('show');
            window.location = "#entry";
        });

        $("#new_entry").click(function (e) {
            $("#collapse2031").addClass('show');
            $('input#id_update_quantity').val('0');
            $('input#id_minimum_amount').val('0');

            $("#id_article").prop('selectedIndex', 0);
            $("#id_driver").prop('selectedIndex', 0);
            $("#id_status").prop('selectedIndex', 0);
            $("#id_delivery_customer").prop('selectedIndex', 0);
            $("#id_forwarders").prop('selectedIndex', 0);
            $("#id_forwarders").prop('selectedIndex', 0);
            $("#id_supplier").prop('selectedIndex', 0);
            $("#id_vehicle").prop('selectedIndex', 0);
            return false;
        });

        $("#id_ident").keyup(function () {
            val = this.value;
            $.ajax({
                url: "{% url 'hand_transmitter_match' %}",
                type: 'GET',
                data: {'val': val},
                success: function (resp) {
                    if (resp.data == 1) {
                        $("#id_ident").css('background-color', '#fd0000')
                        $("#submit").prop('disabled', true)
                    } else {
                        $("#id_ident").css('background-color', '#ffff')
                        $("#submit").prop('disabled', false)
                    }
                }
            })
        })
        $("#text_btn").click(function () {
            var title = "Text für id";
            // var body = "Material list";
            $("#MyPopup .modal-title").html(title);

            $("#btnClosePopup").click(function () {
                $("#MyPopup").modal("hide");
            });
            $("#MyPopup .modal-body").load("/id_text");
            $("#MyPopup").modal();
        });
    </script>
{% endblock %}
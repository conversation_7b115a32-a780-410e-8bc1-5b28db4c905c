# Generated by Django 3.1.1 on 2021-07-01 08:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('scale_app', '0001_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='devices',
            name='certi_num',
            field=models.Char<PERSON>ield(default='None', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='devices',
            name='ip_addr',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='devices',
            name='mac_addr',
            field=models.Char<PERSON>ield(max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='devices',
            name='serial_num',
            field=models.CharField(default='None', max_length=100, null=True),
        ),
    ]

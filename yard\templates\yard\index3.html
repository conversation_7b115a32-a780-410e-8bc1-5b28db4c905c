{% extends 'base2.html' %}
{% load i18n %}
{%block head%}
  {%load static%}
{%endblock%}
{% block content %}

<div class="container">
  <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
    <i class="fas fa-align-justify"></i>
     </button>
       <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
      <div class="content_text">
        <p class="mb-0">{% trans 'OVERVIEW' %}</p>
      </div>
      <div class="heding ">
        <p>{% trans 'Weighing' %}</p>
      </div>
    </div>
  </div>
</div>
<div class="container">
  <div class="row mb-5">
    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
      <div class="card">
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapse18">

                    <div class="row">
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                        <p class="mb-0 pt-2 text-color text_color">Kunde</p>
                      </div>
                    </div>
                  </a>
                </h4>
              </div>
            </h5>
          </div>
        </div>
        
        <div id="collapse18" class="collapse" >
          <div class="panel-body">  
             <div class="card-body text-left">
            <form class="form-group" action="#!">
              <div class="md-form mb-3">
                <div class="row">
                  <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-right">
                  </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-right">
                  <button type="button" class="btn btn-primary" data-toggle="modal" data-target=".bd-example-modal-lg">Advanced Search</button>
                </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 mt-3">
                <label>Name</label>
                <select class="form-control">
                  <option>Select Customer</option>
                  <option>Ebin</option>
                  <option>Tendulkar</option>
                  <option>joseph</option>
               </select>
               </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-3">
                <label>First name</label>
                <input type="First name" class="form-control" name="First name" placeholder="First name">
              </div>
              <div class=" col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mb-3 mt-3">
                <label>Description</label>
                <input type="Description" class="form-control" name="Description" placeholder="Description">
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mb-3 mt-3">
                <label>Telephone</label>
                <input type="Telephone" class="form-control" name="Telephone" placeholder="Telephone">
               </div>
              </div>
             </div>
            </form>
          </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
      <!-- Material form login -->
      <div class="card">
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapse12">
                    <div class="row">
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                        <p class="mb-0 pt-2 text_color">Vehicle</p>
                      </div>
                    </div>
                  </a>
                </h4>
              </div>
            </h5>
          </div>
        </div>

        <div id="collapse12" class="collapse" >
          <div class="panel-body">  
             <div class="card-body text-left">
              <form class="form-group" action="#!">
                <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                  </div>
                  <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-right">
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#Vehicle">Advanced Search</button>
                  </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-3">
                <label>License Plate</label>
                <select class="form-control">
                  <option>Select vehicle</option>
                  <option>B 22-33</option>
                  <option>HE-705</option>
                  <option>KL-05-2277</option>
                </select>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-3">
                <label>License Plate</label>
                <input type="License Plate" class="form-control" name="License Plate" placeholder="License Plate">
              </div>
              <div class="md-form col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 mt-3">
                <label>Forwarder</label>
                <input type="Forwarder" class="form-control" name="Forwarder" placeholder="Forwarder">
              </div>
              <div class="md-form col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 mt-3">
                <label>Vehicle Weight</label>
                <input type="Vehicle Weight" class="form-control" name="Vehicle Weight" placeholder="Vehicle Weight">
              </div>
            </div>
            </form>
          </div>
         </div>
        </div>
      </div>
    </div>
  </div>
  <!--FORM-->
  <div class="row mb-5">
    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
      <!-- Material form login -->
      <div class="card">
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapse6">
                    <div class="row">
                      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                        <p class="mb-0 pt-2 text_color">Material</p>
                      </div>                          
                    </div>
                  </a>
                </h4>
              </div>
            </h5>
          </div>
        </div>
        <div id="collapse6" class="collapse" >
          <div class="panel-body">  
             <div class="card-body text-left">
            <form class="form-group" action="#!">
              <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">

                </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                  <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-right">
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#Material">Advanced Search</button>
                  </div>
                </div>

              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-3">
                <label>Description</label>
                <select class="form-control">
                  <option>Description</option>
                  <option>Select Produkt</option>
                  <option>Sand</option>
                  <option>Metal</option>
                  <option>Steel 10</option>
                </select>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-3">
                <label>Short Name</label>
                <input type="text" class="form-control" name="fname" placeholder="Short Name">
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-2">
                <label>Street</label>
                <input type="text" class="form-control" name="description" placeholder="Street">
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mt-2">
                <label>Vat</label>
                <input type="Vat" class="form-control" name="Vat" placeholder="Vat">
              </div>
            </div>
            <div class="md-form mb-3 mt-3">
              <label>Remaining Quantity</label>
              <input type="Remaining Quantity" class="form-control" name="Remaining Quantity" placeholder="Remaining Quantity">
            </div>
            </form>
          </div>
        </div>
        </div>
      </div>
    </div>

    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
      <!-- Material form login -->
      <div class="card py-0">
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3">
   
         <div class="panel-heading">
           <h4 class="panel-title">
             <a data-toggle="collapse" data-parent="#accordion" href="#collapse-32">

              <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                  <p class="mb-0 pt-2 text_color">Baustelle</p>
                </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-right">
                   </h5>
                </div>
              </div>
            </a>
          </h4>
        </div>
        <div id="collapse-32" class="collapse" >
          <div class="panel-body">  
             <div class="card-body text-left">
            <form class="form-group" action="#!">
              <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">

                </div>
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#Baustelle">
                  Advanced Search
                </button>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mb-3 mt-3">
                <label>Baustelle Name</label>
                <select class="form-control">
                  <option>Baustelle Name</option>
                  <option>Select Baustelle</option>
                  <option>BMW</option>
                  <option>Benz</option>
                  <option>AMG Gmbh</option>
                  <option>New World Gmbhh</option>
                </select>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mb-3 mt-3">
                <label>Short Name</label>
                <input type="text" class="form-control" name="fname" placeholder="Short Name">
              </div>  
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mb-3 mt-2">
                <label>Street</label>
                <input type="text" class="form-control" name="description" placeholder="Street">
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 md-form mb-3 mt-2">
                <label>Pin</label>
                <input type="text" class="form-control" name="telephone" placeholder="Pin">
              </div>
            </div>
            <div class="md-form mb-3 mt-2">
              <label>Place</label>
              <input type="text" class="form-control" name="telephone" placeholder="Place">
            </div>
            </form>
        </div>
        </div>
       </div>
      </div>
     </div>

     <div class="row mb-5">
      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
        <!-- Material form login -->
        <div class="card">
          <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
              <h5 class="card-header info-color white-text py-3">
                <div class="panel-heading">
                  <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion" href="#collapse-24">
                       <div class="row">
                       <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                       <p class="mb-0 pt-2 text_color">
                       Weight</p>
                    </a>
                  </h4>
                </div>
              </h5>
            </div>
          </div>
               
        <div id="collapse-24" class="collapse" >
        <div class="panel-body">  
          <div class="card-body text-left">
            <form class="form-group" action="#!">
              <div class="md-form mb-3">
                <div id="card_back">
                  <ul class="nav nav-tabs mt-4" id="myTab" role="tablist">
                    <li class="nav-item">
                      <a class="nav-link text-dark active show" id="scale1-tab" data-toggle="tab" href="#scale1"
                        role="tab" aria-controls="scale1" aria-selected="false">Scale1</a>
                        </li>
                          <li class="nav-item ">
                          <a class="nav-link text-dark" id="scale2-tab" data-toggle="tab" href="#scale2" role="tab"
                       aria-controls="scale2" aria-selected="false">Scale2</a>
                    </li>
                    </ul>
          
          <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="scale1" role="tabpanel" aria-labelledby="scale1-tab">
              <div class="card-body py-4">
                <form class="form-group" action="#!">
                  <div class="md-form mb-3">
                    <div class="kg text-center mt-3">
                      <h1 class="bg-text">0000<small class="align-middle text-secondary">KG</small></h1>
                    </div>
                    <div class="col-auto">
                      <label class="sr-only" for="inlineFormInputGroup">0000</label>
                      <div class="input-group mb-2">
                        <div class="input-group-prepend">
                          <div class="input-group-text">First Weighing</div>
                        </div>
                        <input type="text" class="form-control" id="inlineFormInputGroup" placeholder="0000"><label
                          class=" ml-2">KG</label>
                      </div>
                    </div>
                  </div>
                  <div id="text_card">
                    <div class="md-form mb-3">
                      <div class="md-form mb-3">
                        <div class="col-auto">
                          <label class="sr-only" for="inlineFormInputGroup">0000</label>
                          <div class="input-group mb-2">
                            <div class="input-group-prepend">
                              <div class="input-group-text">Second Weighing</div>
                            </div>
                            <input type="text" class="form-control" id="inlineFormInputGroup" placeholder="0000"><label
                              class=" ml-2">KG</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="md-form mb-3">
                    <div class="md-form mb-3">
                      <div class="md-form mb-3">
                        <div class="col-auto">
                          <label class="sr-only" for="inlineFormInputGroup">0</label>
                          <div class="input-group mb-2">
                            <div class="input-group-prepend">
                              <div class="input-group-text">Net Weighing</div>
                            </div>
                            <input type="text" class="form-control" id="inlineFormInputGroup" placeholder="0"><label
                              class=" ml-2">KG</label>
                          </div>
                        </div>
                      </div>

                    </div>

                  </div>
                </form>
              </div>
            </div>
            <div class="tab-pane fade" id="scale2" role="tabpanel" aria-labelledby="scale2-tab">
              <div class="card-body py-4">
                <form class="form-group" action="#!">
                  <div class="md-form mb-3">
                    <div class="kg text-center mt-3">
                      <h1 class="bg-text">0020<small class="align-middle text-secondary">KG</small></h1>
                    </div>
                    <div class="col-auto">
                      <label class="sr-only" for="inlineFormInputGroup">0000</label>
                      <div class="input-group mb-2">
                        <div class="input-group-prepend">
                          <div class="input-group-text">First Weighing</div>
                        </div>
                        <input type="text" class="form-control" id="inlineFormInputGroup" placeholder="0000"><label
                          class=" ml-2">KG</label>
                      </div>
                    </div>
                  </div>
                  <div id="text_card">
                    <div class="md-form mb-3">
                      <div class="md-form mb-3">
                        <div class="col-auto">
                          <label class="sr-only" for="inlineFormInputGroup">0000</label>
                          <div class="input-group mb-2">
                            <div class="input-group-prepend">
                              <div class="input-group-text">Second Weighing</div>
                            </div>
                            <input type="text" class="form-control" id="inlineFormInputGroup" placeholder="0000"><label
                              class=" ml-2">KG</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="md-form mb-3">
                    <div class="md-form mb-3">
                      <div class="md-form mb-3">
                        <div class="col-auto">
                          <label class="sr-only" for="inlineFormInputGroup">0</label>
                          <div class="input-group mb-2">
                            <div class="input-group-prepend">
                              <div class="input-group-text">Net Weighing</div>
                            </div>
                            <input type="text" class="form-control" id="inlineFormInputGroup" placeholder="0"><label
                              class=" ml-2">KG</label>
                          </div>
                        </div>
                      </div>

                    </div>

                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
      <div class="row mb-5">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
          <button type="button" class="btn btn-primary ml-5"><i class="fas fa-save ml-2"></i> Save</button>
          <button type="button" class="btn btn-primary ml-2"><i class="fas fa-print"></i> Print</button>
          <button type="button" class="btn btn-primary ml-2"><i class="fas fa-camera"></i> Capture
            Image</button>
        </div>
      </div>
  </div>
</div>
</div>
</div>
    </div>

<div id="MyPopup" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
      <div class="modal-dialog modal-lg">
          <!-- Modal content-->
          <div class="modal-content p-4">
              <div class="modal-header modal-header-custom">                  
                  <h4 class="modal-title">
                  </h4>
                  <button type="button" class="close" data-dismiss="modal">
                    &times;</button>
              </div>
              <div class="modal-body">
              </div>
              <div class="modal-footer">
                  <input type="button" id="btnClosePopup" value="Close" class="btn btn-secondary" />
              </div>
          </div>
      </div>
  </div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script src="{% static 'yard/js/index.js'%}"></script>
{%endblock%}
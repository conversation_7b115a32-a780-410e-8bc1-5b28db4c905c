# Generated by Django 3.1.1 on 2021-04-12 10:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='container',
            name='container_number',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='hazard_warnings',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='maximum_gross_weight',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='container',
            name='next_exam',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='payload_container_volume',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='tare_weight',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name='container',
            name='waste_type',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='warehouse',
            name='storage_location',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='warehouse',
            name='warehouse_street',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='price_group',
            field=models.CharField(blank=True, choices=[('price1', 'Price 1'), ('price2', 'Price 2'), ('price3', 'Price 3'), ('price4', 'Price 4'), ('price5', 'Price 5')], max_length=10),
        ),
        migrations.AlterField(
            model_name='customer',
            name='street',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
    ]

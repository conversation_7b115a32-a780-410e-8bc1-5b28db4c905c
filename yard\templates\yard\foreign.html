{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block head %}
{%load static%}
{% endblock %}
{% block content %}
    <!-- /#sidebar-wrapper -->
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              <p>{% translate 'Foreign Weighing List' %}</p>
            </div>
          </div>
        </div>
        <!--table strat-->
        <div class="row">
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
              <label>{% translate "Show entries" %}:</label>
             <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
             </select>
           </div>
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
               <label>{% translate "Search" %}:</label>
               <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
           </div>
        </div>
        <div class="row">
        <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="deliveryNoteTable">
          <thead>
            <tr> {% if request.user.is_superuser%}
              <th>{% translate 'Action' %}</th> {% endif %}
              <th>{% translate 'Lfd Nr' %}</th>
              <th>{% translate 'Transaction ID' %}</th>
              <th>{% translate 'Vehicle' %}</th>
              <th>{% translate 'Material' %}</th>
              <th>{% translate 'Customer' %}</th>
              <th>{% translate 'Supplier' %}</th>
              <th>{% translate 'First Weight' %}</th>
              <th>{% translate 'Second Weight' %}</th>
              <th>{% translate 'Net Weight' %}</th>
              <th>{% translate 'Total Price' %}</th>
              <th>Status</th>
              <th>{% translate 'Alibi Nr' %}</th>
              <th>{% translate 'Updated on' %}</th>
            </tr>
          </thead>
          <tbody class="mt-4">
            {% for data in dataset %}
            <tr> {% if request.user.is_superuser%}
              <td>
                <a class="confirmdelete" href="{% url 'foreign_list_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a>
              </td> {% endif %}
              <td>{{ data.id }}</td>
              <td>{{ data.transaction_id }}</td>
              <td>{{ data.vehicle|default_if_none:" " }}</td>
              <td>{{ data.article|default_if_none:" " }}</td>
              <td>{{ data.customer|default_if_none:" " }}</td>
              <td>{{ data.supplier|default_if_none:" " }}</td>
              <td>{{ data.first_weight|default_if_none:"0" }}</td>
              <td>{{ data.second_weight|default_if_none:"0" }}</td>
              <td>{{ data.net_weight|default_if_none:"0" }}</td>
              <td>{{ data.total_price|default_if_none:"0" }}</td>
              <td> {% if data.status == '0' %} Eingang {% elif data.status == '1' %} Ausgang {% else %} N/A {%endif%}</td>
              <td>{{ data.secondw_alibi_nr }}</td>
              <td>{{ data.updated_date_time }}</td>
            </tr>
            {% endfor %}

{% endblock %}
{% block scripts %}
<script src="{% static 'stats/js/stats_custom.js'%}"></script>

<script>
    // DataTable
    $(document).ready(function() {
      var table = $('#deliveryNoteTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
                   console.log(that)
<!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
<!--                       if ( that.search() !== this.value ) {-->
<!--                           that.search( this.value ).draw();-->
<!--                       }-->
<!--                   } );-->
               } );
           }
          });

      $("#deliveryNoteTable_filter").hide()

      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      } );

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      } );

 });

</script>
{% endblock %}


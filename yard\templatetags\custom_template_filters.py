import math

from django import template
from django.contrib.humanize.templatetags.humanize import intcomma
from django.template.defaultfilters import stringfilter
from django.utils.safestring import mark_safe

from yard.utils import get_image_data

register = template.Library()


@register.filter
@stringfilter
def space(value):
    return mark_safe("&nbsp;".join(value.split(' ')))


@register.filter
def addstr(arg1, arg2):
    """concatenate arg1 & arg2"""
    return str(arg1) + str(arg2)


@register.filter
def convert_to_kg(value):
    try:
        if value != 0 or value is not None:
            weight = round(value / 1000)
            return weight
    except:
        return value

@register.filter
def currency(dollars):
    dollars = round(float(dollars), 2)
    return ",".join(("%s%s" % (intcomma(int(dollars)), ("%0.2f" % dollars)[-3:])).split("."))

@register.filter
def format_number(number, precision=2):
    # build format string
    format_str = '{{:,.{}f}}'.format(precision)
    # make number string
    number_str = format_str.format(number)

    # replace chars
    return number_str.replace(',', 'X').replace('.', ',').replace('X', '.')

@register.filter
def format_weight(number, precision=2):
    # build format string
    format_str = '{{:,.{}f}}'.format(precision)

    # make number string
    number_str = format_str.format(number)

    # replace chars
    return number_str.replace(',', 'X').replace('.', ',').replace('X', '.').split(",")[0]

@register.filter
def convert_to_tonne(value):
    try:
        if value != 0 or value is not None:
            weight = value / 1000
            return weight
    except:
        return value

@register.filter
def format_float(value:float):
    if value:
        if value == 0.0:
            return 0
        return 0
    else:
        return "-"

@register.filter
def to_base64(url):
    img_str = "data:image/png;base64, " + get_image_data(url)
    return img_str
    # return "data:image/png;base64, " + str(requests.get(url).content)

@register.filter(name='times') 
def times(number):
    return range(number)
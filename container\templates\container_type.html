{% extends 'base2.html' %}
{% load static%}
{% load i18n%}
{% load l10n %}
{% block content%}

<style>
  ol li a:hover{
  color: #dc3545 !important;
}
</style>

<button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
  <i class="fas fa-align-justify"></i>
</button>
  <div class="container-fluid">
    <div>
      <nav style="--bs-breadcrumb-divider: '>';" aria-label="breadcrumb">
          <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="/" class=" text-muted">Home</a></li>
              <li class="breadcrumb-item active" aria-current="page">{% translate "Container Type" %}</li>
          </ol>
      </nav>
  </div>
    <div class="row">
      <div class="col-md-12 ">
        <h5 class="font-weight-bold">{% translate "Container Type" %}</h5>
        </div>
    </div>
    <div class="row">
            <div class="col-md-11 mx-auto bg-white mt-3 p-3 rounded shadow border">       
                      <div class="row">
                    <div class="col-md-2 col-sm-6 col-12 pr-sm-1 pl-md-7">   
                        <input type="hidden" name="customer_type" id="customer_type">
                        {% comment %} <input type="hidden" name="trans_id" id="trans_id" value=""> {% endcomment %}
                        {% comment %} <input type="hidden" name="id_transaction_id" id="id_transaction_id" > {% endcomment %}
                        <label for="" id="" class="label">{% trans " Container Type" %}</label>
                        
                        <select name="id" id='container_type' class="form-control select2" onchange="populateContainerDetails($(this).val())">
                           <option value='0'>{% trans "Select Type of container" %}</option>
                             {%for i in container_type_list%}
                              <option value="{{i.pk}}">{{i.container_type}}</option>
                              {%endfor%}
                        </select>
                     </div>
                     <div class="col-md-4 col-sm-6 col-12 pr-sm-1 pl-md-7">
                                 <label for="" class="label">{% trans " Designation" %}</label>
                        <input type="text" onkeydown="return event.key != 'Enter';" class="form-control AddHeightinInputbox" placeholder="{% trans 'Designation' %}" id="container_designation" name="container_designation">
                         {% comment %} <input type='text' name="ident" id="designation"> {% endcomment %}
                     </div>
                     <div class="col-md-3 col-sm-6 col-12 pr-sm-1 pl-md-7">
                                 <label for=""  class="label">{% trans "  Size" %}</label>

                         <input type='text' name="ident" id="container_size">
                     </div>
                     <div class="col-md-3 col-sm-6 col-12 pr-sm-1 pl-md-7">
                      <label for="" id="" class="label">{% trans " " %}</label>
                        <button type="submit" target="_self" name="get_container_details" id="get_container_details" title="Get Container Details" class="btn btn-primary btnFull mt-2 mt-md-0" onclick="populateContainerDetails1($('#container_type').val())">{% trans 'Get Container Detail' %}</button>
                     </div>
                     
                 </div>
                 </div>
          </div>

         

    <div class="row">
            <div class="col-md-11 mx-auto bg-white mt-3 p-3 rounded shadow border">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="">Type</label>
                            <input type="text"  id="type" name="type">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="">Designation</label>
                            <input type="text" class="form-control" id="designation"  name="designation">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>

                     <div class="col-md-3">
                        <div class="form-group">
                            <label for="">Size</label>
                            <input type="text" class="form-control" id="size" name="size">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Account Unit</label>
                            <input type="text" class="form-control" id="account_unit" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                        </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Maintenance Cycle</label>
                            <input type="text" class="form-control" id="maintenance_cycle" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Empty Weight</label>
                            <input type="text" class="form-control" id="empty_weight" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Maximum waight allowed</label>
                            <input type="text" class="form-control" id="maxweightallowed" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Cost Per Day</label>
                            <input type="text" class="form-control" id="costperday" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Cost Per Hour</label>
                            <input type="text" class="form-control" id="costperhour" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                     <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Working Days Per Month</label>
                            <input type="text" class="form-control" id="workingdaypermonth" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Working Hours Per Day</label>
                            <input type="text" class="form-control" id="workinghourperday" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="title">Cost Center</label>
                            <input type="text" class="form-control" id="costcenter" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title font-weight-bold"><i class="far fa-file-alt"></i> Set Price Per Piece</h6>
                                 <div class="col-md-3">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <label for="type">Set Price 1 :</label>
                            {% comment %} </div> {% endcomment %}
                            </div>
                             <div class="col-md-4">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <input type="text" class="form-control" id="sp1" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                            {% comment %} </div> {% endcomment %}
                            </div>

                            <div class="col-md-3">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <label for="type">Set Price 2 :</label>
                            {% comment %} </div> {% endcomment %}
                            </div>
                             <div class="col-md-4">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <input type="text" class="form-control" id="sp2" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                            {% comment %} </div> {% endcomment %}
                            </div>

                            <div class="col-md-3">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <label for="type">Set Price 3 :</label>
                            {% comment %} </div> {% endcomment %}
                            </div>

                             <div class="col-md-4">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <input type="text" class="form-control" id="sp3" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                            {% comment %} </div> {% endcomment %}
                            </div>


                            </div>
                            </div>
                        </div>
                    </div>

                      <div class="col-md-6">
                        <div class="form-group">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title font-weight-bold"><i class="far fa-file-alt"></i> Stand Price Per Day</h6>
                                 <div class="col-md-3">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <label for="type">Set Price 1 :</label>
                            {% comment %} </div> {% endcomment %}
                            </div>
                             <div class="col-md-4">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <input type="text" class="form-control" id="ssp1" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                            {% comment %} </div> {% endcomment %}
                            </div>

                            <div class="col-md-3">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <label for="type">Set Price 2 :</label>
                            {% comment %} </div> {% endcomment %}
                            </div>
                             <div class="col-md-4">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <input type="text" class="form-control" id="ssp2" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                            {% comment %} </div> {% endcomment %}
                            </div>

                            <div class="col-md-3">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <label for="type">Set Price 2 :</label>
                            {% comment %} </div> {% endcomment %}
                            </div>
                            
                             <div class="col-md-4">
                                 {% comment %} <div class="form-group"> {% endcomment %}
                            <input type="text" class="form-control" id="ssp3" required name="title">
                            <!-- <small id="title_msg" class="color-danger">This field is reqired</small> -->
                            {% comment %} </div> {% endcomment %}
                            </div>

                            <input type="hidden" id=id >
                            </div>
                            </div>
                        </div>
                    </div>
                        <div class="col-md-3 col-sm-6 col-12 pr-sm-l pr-md-7">
                    <button type="submit" target="_self" name="save_comb" id="save_container" title="ID speichern" class="btn btn-primary btnFull mt-2 mt-md-0" onclick="savecontaierdetails($(#id.val()))">{% trans 'Save Detail' %}</button>
                        </div>
                </div>
            </div>
        </div>
        
{% endblock content%}
{% block scripts %}
<script src="{% static 'container/js/customs.js'%}"></script>
<script>


{% endblock %}
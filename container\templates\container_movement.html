{% extends 'base2.html' %}
{% load static%}
{% load i18n%}
{% load l10n %}
{% block content%}

<style>
  ol li a:hover{
  color: #dc3545 !important;
}
</style>

<button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
  <i class="fas fa-align-justify"></i>
</button>
  <div class="container-fluid">
    <div>
      <nav style="--bs-breadcrumb-divider: '>';" aria-label="breadcrumb">
          <ol class="breadcrumb">
              <li class="breadcrumb-item"><a href="/" class=" text-muted">Home</a></li>
              <li class="breadcrumb-item active" aria-current="page">{% translate "Container Movement" %}</li>
          </ol>
      </nav>
  </div>
    <div class="row">
      <div class="col-md-12 ">
        <h5 class="font-weight-bold">{% translate "Container Movement" %}</h5>
        </div>
    </div>
    <div class="card-deck mt-5">

          <div class="card">
            <div class="card-body">
              <h6 class="card-title font-weight-bold"><i class="far fa-file-alt"></i> Crops & Lands</h6>
              <ul class="list-group">
                <a href="/crop" style="color: gray;" class="c-link"><li class="list-group-item">Crop</li></a>
                <a href="/crop_cycle" style="color: gray;" class="c-link"><li class="list-group-item ">Crop Cycle</li></a>
                <a href="/crop_location" style="color: gray;" class="c-link"><li class="list-group-item ">Location</li></a>
              </ul>
            </div>
          </div>


{% endblock content%}
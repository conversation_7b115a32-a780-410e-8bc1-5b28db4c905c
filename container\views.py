from django.shortcuts import render
from django.http import JsonResponse
from container.models import Container
from django.forms.models import model_to_dict

# Create your views here.


def container_movement(request):
    return render(request,"container_movement.html")

def container_type(request):
    if request.GET.get('ajax') is not None:
        try:
            obj = Container.objects.get(id=request.GET['id'])
        except:
            obj = None
        if obj is not None:
            data = model_to_dict(obj)

            return JsonResponse(data)
        else:
            return JsonResponse({'status':0})
    context = {}
    try:
        container_type = Container.objects.all()
    except Exception as e:
        print(e)
        container_type =None
    context['container_type_list'] = container_type 
    return render(request, "container_type.html", context)

def savecontaierdetails(request, identifier):

    c_type = request.GET.get('type')
    designation = request.GET.get('designation')
    size = request.GET.get('size')
    account_unit = request.GET.get('account_unit')
    maintenance_cycle = request.GET.get('maintenance_cycle')
    empty_weight = request.GET.get('empty_weight')
    maximum_weight_allowed = request.GET.get('maxweightallowed')
    cost_per_day = request.GET.get('costperday')
    cost_per_hour = request.GET.get('costperhour')
    workingdays_permonth = request.GET.get('workingdayspermonth')
    workinghoursperday = request.GET.get('workinghoursperday')
    cost_center = request.GET.get('coastcenter')

    
    container = Container.objects.create(id=id)
    container_type = c_type
    designation = designation
    maximum_weight_allowed=maximum_weight_allowed
    size=size
    account_unit=account_unit
    maintenance_cycle=maintenance_cycle
    empty_weight=empty_weight
    cost_per_day=cost_per_day
    cost_per_hour=cost_per_hour
    workingdays_permonth=workingdays_permonth
    workinghoursperday=workinghoursperday
    cost_center=cost_center



    container.save()
    
    return JsonResponse({'status': 1})
  



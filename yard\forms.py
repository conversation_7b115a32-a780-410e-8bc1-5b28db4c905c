from django import forms
from django.contrib.postgres.forms import SimpleArray<PERSON>ield

from .models import *
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.validators import UnicodeUsernameValidator
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import ast

username_validator = UnicodeUsernameValidator()

# class SignUpForm(UserCreationForm):
#     first_name = forms.CharField(max_length=12, min_length=4, required=True, help_text='Required: First Name',
#                                 widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'First Name'}))
#     last_name = forms.CharField(max_length=12, min_length=4, required=True, help_text='Required: Last Name',
#                                widget=(forms.TextInput(attrs={'class': 'form-control'})))
#     email = forms.EmailField(max_length=50, help_text='Required. Inform a valid email address.',
#                              widget=(forms.TextInput(attrs={'class': 'form-control'})))
#     password1 = forms.CharField(label=_('Password'),
#                                 widget=(forms.PasswordInput(attrs={'class': 'form-control'})),
#                                 help_text=password_validation.password_validators_help_text_html())
#     password2 = forms.CharField(label=_('Password Confirmation'), widget=forms.PasswordInput(attrs={'class': 'form-control'}),
#                                 help_text=_('Just Enter the same password, for confirmation'))
#     username = forms.CharField(
#         label=_('Username'),
#         max_length=150,
#         help_text=_('Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.'),
#         validators=[username_validator],
#         error_messages={'unique': _("A user with that username already exists.")},
#         widget=forms.TextInput(attrs={'class': 'form-control'})
#     )
#     yard_id = forms.CharField(label=_('Yard ID'), max_length=30,widget=forms.TextInput(attrs={'class': 'form-control'}))

#     class Meta:
#         User = get_user_model()
#         model = User
#         fields = ('username', 'yard_id', 'first_name', 'last_name', 'email', 'password1', 'password2',)

yard_id_choices = (('1', 'Yard1'), ('2', 'Yard2'))
role_choices = (('technician', _('Technician')), ('superuser', _('Superuser')), ('operator', _('Operator')),
                ('selfservice', _('Self Service')), ('tabletuser', _('Tablet User')), ('Rundholz', _('Rundholz')), ('Noritec', _('Noritec')),
                ('NoritecVerladung', _('Noritec Verladung')), ('SchnittholzVerladung', _('Schnittholz Verladung')), ('HobelwerkVerladung', _('Hobelwerk Verladung')),
                ('Dispo', _('Dispo')), ('Empfang', _('Empfang')), ('Admin', _('Admin')))


class SignUpForm(UserCreationForm):
    name = forms.CharField(label=_('Name'), max_length=200, min_length=4, required=True, help_text='Required: Name',
                           widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('Name')}))
    email = forms.EmailField(label=_('E-mail'), max_length=50, help_text='Required: Inform a valid email address.',
                             widget=(forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('E-mail')})))
    password1 = forms.CharField(label=_('Password'),
                                widget=(
                                    forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': _('Password')})),
                                help_text='Required: valid password')

    # yard_id = forms.ChoiceField(label=_('Yard ID'),required=True,choices=yard_id_choices,widget=forms.Select(attrs={'class': 'form-control','placeholder': _('Yard ID')}))
    yard = forms.ModelChoiceField(label=_('Yard'), queryset=Yard_list.objects.all(), required=True,
                                  widget=forms.Select(attrs={'class': 'form-control'}))
    role = forms.ChoiceField(label=_('Role'), required=True, choices=role_choices,
                             widget=forms.Select(attrs={'class': 'form-control', 'placeholder': _('Role')}))

    class Meta:
        User = get_user_model()
        model = User
        fields = ('name', 'email', 'password1', 'yard', 'role')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # del self.fields['password1']
        del self.fields['password2']


class CustomLoginForm(forms.Form):
    email = forms.EmailField(widget=forms.TextInput(
        attrs={'class': 'form-control', 'placeholder': _('Email')}))
    password = forms.CharField(widget=forms.PasswordInput(
        attrs={
            'class': 'form-control',
            'placeholder': _('Password'),
        }
    ))


class UserUpdateForm(forms.ModelForm):
    name = forms.CharField(label=_('Name'), max_length=200, min_length=4, required=True, help_text='Required: Name',
                           widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('Name')}))
    email = forms.EmailField(label=_('E-mail'), max_length=50, help_text='Required: Inform a valid email address.',
                             widget=(forms.TextInput(attrs={'class': 'form-control', 'placeholder': _('E-mail')})))
    # yard_id = forms.ChoiceField(label=_('Yard ID'),required=True,choices=yard_id_choices,widget=forms.Select(attrs={'class': 'form-control','placeholder': _('Yard ID')}))
    yard = forms.ModelChoiceField(queryset=Yard_list.objects.all(), required=False,
                                  widget=forms.Select(attrs={'class': 'form-control'}))
    role = forms.ChoiceField(label=_('Role'), required=False, choices=role_choices,
                             widget=forms.Select(attrs={'class': 'form-control', 'placeholder': _('Role')}))

    class Meta:
        User = get_user_model()
        model = User
        fields = ('name', 'email', 'yard', 'role', 'address', 'telephone')
        labels = {
            'address': _('Address'),
            'telephone': _('Telephone')
        }


class CustomerForm(forms.ModelForm):
    delivery_terms = forms.ChoiceField(label=_('Delivery Terms'), required=False, widget=forms.Select(attrs={'class': 'form-control'}),
                                       choices=Customer.DELIVERY_CHOICES)
    # price_group = forms.ChoiceField(label=_('Price Group'), widget=forms.Select(attrs={'class': 'form-control'}),
    #                                 choices=Customer.PRICE_CHOICES)
    # warehouse = forms.ModelChoiceField(label=_('Warehouse'), queryset=Warehouse.objects.all(), required=False,
    #                                    widget=forms.Select(attrs={'class': 'form-control'}))
    salutation = forms.ChoiceField(label=_('Salutation'), required=True, choices=SALUTATION_CHOICES,
                                   widget=forms.Select(attrs={'class': 'form-control', 'placeholder': _('Salutation')}))

    class Meta:
        model = Customer

        fields = ["perm_street", "perm_pin", "perm_place", "perm_country", "diff_invoice_recipient", "company", "price_group",
                  "cost_centre", "salutation", "addition1", "addition2", "addition3", "post_office_box", "name1",
                  "name2",
                  "description", "street", "pin", "fax", "place", "country",
                  "website", "contact_person1_email", "contact_person2_email", "contact_person3_email",
                  "contact_person1_phone", "contact_person2_phone", "contact_person3_phone", "customer_type",
                  "private_person", "document_lock", "payment_block", "delivery_terms", "special_discount",
                  "debitor_number", "dunning",
                  "sector", "company_size", "area", "phone_number"]
        labels = {
            'name2': _("Name 2"),
            'name1': _("Name 1"),
            'street': _("Street"),
            'pin': _("PLZ"),
            'description': _("Description"),
            'fax': _("Fax"),
            'place': _("Place"),
            'country': _("Country"),
            'company': _("Company"),
            'addition1': _("addition1"),
            'addition2': _("addition2"),
            'addition3': _("addition3"),
            # 'email': _("Email"),
            'website': _("Website"),
            'contact_person1_email': _("Email1"),
            'contact_person2_email': _("Email2"),
            'contact_person3_email': _("Email3"),
            'contact_person1_phone': _("Phone1"),
            'contact_person2_phone': _("Phone2"),
            'contact_person3_phone': _("Phone3"),
            'diff_invoice_recipient': _("Invoice Recipient"),
            'customer_type': _("Customer Type"),
            # 'classification': _("Classification"),
            'sector': _("Sector"),
            'company_size': _("Company Size"),
            'area': _("Area"),
            'private_person': _("Private Person"),
            'document_lock': _("Document Lock"),
            'payment_block': _("Payment Block"),
            'special_discount': _("Special Discount"),
            'debitor_number': _("Debitor Number"),
            'dunning': _("Dunning"),
            'cost_centre': _("Cost Centre"),
            'perm_street': _("Permenant Street"),
            'perm_pin': _("Permanent Pin"),
            'perm_place': _("Permanent Place"),
            'perm_country': _("Permanent Country"),
            'post_office_box': _("Post Box"),
            'phone_number': _("Phone Number"),

        }


class PlaceOfDeliveryForm(forms.ModelForm):
    delivery_terms = forms.ChoiceField(label=_('Delivery Terms'), widget=forms.Select(attrs={'class': 'form-control'}),
                                       choices=Customer.DELIVERY_CHOICES)
    # price_group = forms.ChoiceField(label=_('Price Group'), widget=forms.Select(attrs={'class': 'form-control'}),
    #                                 choices=Customer.PRICE_CHOICES)
    # warehouse = forms.ModelChoiceField(label=_('Warehouse'), queryset=Warehouse.objects.all(), required=False,
    #                                    widget=forms.Select(attrs={'class': 'form-control'}))
    salutation = forms.ChoiceField(label=_('Salutation'), required=True, choices=SALUTATION_CHOICES,
                                   widget=forms.Select(attrs={'class': 'form-control', 'placeholder': _('Salutation')}))

    class Meta:
        model = PlaceOfDelivery

        fields = ["perm_street", "perm_pin", "perm_place", "perm_country", "diff_invoice_recipient", "company",
                  "cost_centre", "salutation", "addition1", "addition2", "addition3", "post_office_box", "name1",
                  "name2",
                  "description", "street", "pin", "fax", "place", "country", "term_of_payment",
                  "website", "contact_person1_email", "contact_person2_email", "contact_person3_email",
                  "contact_person1_phone", "contact_person2_phone", "contact_person3_phone", "customer_type",
                  "private_person", "document_lock", "payment_block", "delivery_terms", "special_discount",
                  "kreditoren_number", "dunning",
                  "sector", "company_size", "area", "phone_number"]
        labels = {
            'name2': _("Name"),
            'name1': _("Firma"),
            'street': _("Street"),
            'pin': _("PLZ"),
            'description': _("Description"),
            'fax': _("Fax"),
            'place': _("Place"),
            'country': _("Country"),
            'company': _("Company"),
            'addition1': _("addition1"),
            'addition2': _("addition2"),
            'addition3': _("addition3"),
            # 'email': _("Email"),
            'website': _("Website"),
            'contact_person1_email': _("Email1"),
            'contact_person2_email': _("Email2"),
            'contact_person3_email': _("Email3"),
            'contact_person1_phone': _("Phone1"),
            'contact_person2_phone': _("Phone2"),
            'contact_person3_phone': _("Phone3"),
            'diff_invoice_recipient': _("Invoice Recipient"),
            'customer_type': _("Customer Type"),
            # 'classification': _("Classification"),
            'sector': _("Sector"),
            'company_size': _("Company Size"),
            'area': _("Area"),
            'private_person': _("Private Person"),
            'document_lock': _("Document Lock"),
            'payment_block': _("Payment Block"),
            'special_discount': _("Special Discount"),
            'debitor_number': _("Debitor Number"),
            'dunning': _("Dunning"),
            'cost_centre': _("Cost Centre"),
            'perm_street': _("Permenant Street"),
            'perm_pin': _("Permanent Pin"),
            'perm_place': _("Permanent Place"),
            'perm_country': _("Permanent Country"),
            'post_office_box': _("Post Box"),
            'phone_number': _("Phone Number"),

        }


class SupplierForm(forms.ModelForm):
    # first_name = forms.CharField(label=_("First Name"), required=False,
    #                              widget=forms.TextInput(attrs={'class': 'form-control'}))
    supplier_name = forms.CharField(
        label=_("Company"), widget=forms.TextInput(attrs={'class': 'form-control'}))
    name = forms.CharField(label=_("Baustellenleiter"), required=False,
                           widget=forms.TextInput(attrs={'class': 'form-control'}))
    street = forms.CharField(label=_("Street"), required=False,
                             widget=forms.TextInput(attrs={'class': 'form-control'}))
    pin = forms.IntegerField(label=_("PLZ"), required=False, widget=forms.NumberInput(
        attrs={'class': 'form-control'}))
    # fax = forms.CharField(label=_("Fax"), required=False,
    #                       widget=forms.TextInput(attrs={'class': 'form-control'}))
    place = forms.CharField(label=_("Place"), required=False,
                            widget=forms.TextInput(attrs={'class': 'form-control'}))
    # salutation = forms.ChoiceField(label=_('Salutation'), required=True, choices=SALUTATION_CHOICES,
    #                                widget=forms.Select(attrs={'class': 'form-control', 'placeholder': _('Salutation')}))
    addition1 = forms.CharField(label=_("addition1"), required=False,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    addition2 = forms.CharField(label=_("addition2"), required=False,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    addition3 = forms.CharField(label=_("addition3"), required=False,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    # post_office_box = forms.CharField(label=_("Post Box"), required=False,
    #                                   widget=forms.TextInput(attrs={'class': 'form-control'}))
    country = forms.CharField(label=_("Country"), required=False,
                              widget=forms.TextInput(attrs={'class': 'form-control'}))
    contact_person1_email = forms.CharField(label=_("Email1"), required=False,
                                            widget=forms.TextInput(attrs={'class': 'form-control'}))
    # contact_person2_email = forms.CharField(label=_("Email2"), required=False,
    #                                         widget=forms.TextInput(attrs={'class': 'form-control'}))
    # contact_person3_email = forms.CharField(label=_("Email3"), required=False,
    #                                         widget=forms.TextInput(attrs={'class': 'form-control'}))
    contact_person1_phone = forms.CharField(label=_("Phone1"), required=False,
                                            widget=forms.TextInput(attrs={'class': 'form-control'}))

    # contact_person2_phone = forms.CharField(label=_("Phone2"), required=False,
    #                                         widget=forms.TextInput(attrs={'class': 'form-control'}))
    # contact_person3_phone = forms.CharField(label=_("Phone3"), required=False,
    #                                         widget=forms.TextInput(attrs={'class': 'form-control'}))
    # website = forms.CharField(label=_("Website"), required=False,
    #                           widget=forms.TextInput(attrs={'class': 'form-control'}))
    # cost_centre = forms.IntegerField(label=_("Cost Centre"), required=False,
    #                                  widget=forms.NumberInput(attrs={'class': 'form-control'}))
    # warehouse = forms.ModelChoiceField(label=_("Warehouse"), queryset=Warehouse.objects.all(), required=False,
    #                                    widget=forms.Select(attrs={'class': 'form-control'}))
    # creditor_number = forms.IntegerField(label=_("Creditor number"), required=False,
    #                                      widget=forms.NumberInput(attrs={'class': 'form-control'}))

    class Meta:
        model = Supplier

        fields = ["supplier_name", "name", "street", "pin", "place", "addition1",
                  "addition2", "addition3", "country", "contact_person1_email",
                  "contact_person1_phone", "project_number"]
        # labels = {
        #     'short_name': _("Short name"),
        #     'supplier_name': _("Supplier name"),
        #     'name': _("Name"),
        #     'street': _("Street"),
        #     'pin': _("Pin"),
        #     'telephone': _("Telephone"),
        #     'place': _("Place"),
        #     'infotext': _("Infotext"),
        # }


class ForwardersForm(forms.ModelForm):
    second_name = forms.CharField(label=_("Surname"), required=False,
                                  widget=forms.TextInput(attrs={'class': 'form-control'}))
    name = forms.CharField(label=_("Company"), widget=forms.TextInput(
        attrs={'class': 'form-control'}))
    firstname = forms.CharField(label=_("First name"), required=False,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    street = forms.CharField(label=_("Street"), required=False,
                             widget=forms.TextInput(attrs={'class': 'form-control'}))
    pin = forms.CharField(label=_("PLZ"), required=False,
                          widget=forms.TextInput(attrs={'class': 'form-control'}))
    telephone = forms.CharField(label=_("Telephone"), required=False,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    place = forms.CharField(label=_("Place"), required=False,
                            widget=forms.TextInput(attrs={'class': 'form-control'}))
    # contact_person = forms.CharField(label=_("Contact person"), widget=forms.TextInput(attrs={'class': 'form-control'}))
    country = forms.CharField(label=_("Country"), required=False,
                              widget=forms.TextInput(attrs={'class': 'form-control'}))

    class Meta:
        model = Forwarders

        fields = ["name", "firstname", "second_name",
                  "street", "pin", "telephone", "place", "country"]
        # labels = {
        #     'short_name': "Short name",
        #     'name': "Name",
        #     'firstname': "First name",
        #     'street': "Street",
        #     'pin': "Pin",
        #     'telephone': "Telephone",
        #     'place': "Place",
        #     'contact_person': "Contact person",
        # }


class TransactionForm(forms.ModelForm):
    vehicle = forms.ModelChoiceField(label=_("License Plate"), queryset=Vehicle.objects.all(), required=False,
                                     widget=forms.Select(attrs={'class': 'form-control'}))
    article = forms.ModelChoiceField(label=_("Material"), queryset=Article.objects.all(), required=False,
                                     widget=forms.Select(attrs={'class': 'form-control'}))
    customer = forms.ModelChoiceField(label=_("Customer"), queryset=Customer.objects.all(), required=False,
                                      widget=forms.Select(
                                          attrs={'class': 'form-control', 'id': 'id_delivery_customer'}))
    supplier = forms.ModelChoiceField(label=_("Supplier"), queryset=Supplier.objects.all(), required=False,
                                      widget=forms.Select(attrs={'class': 'form-control'}))
    container = forms.ModelChoiceField(label=_("Container"), queryset=Container.objects.all(), required=False,
                                       widget=forms.Select(attrs={'class': 'form-control'}))
    place_of_delivery = forms.ModelChoiceField(label=_("Lieferant"), queryset=PlaceOfDelivery.objects.all(), required=False,
                                       widget=forms.Select(attrs={'class': 'form-control'}))
    total_price = forms.DecimalField(label=_('Total_price'), required=False)

    deduction = forms.ModelChoiceField(label=_("Deduction"), queryset=Article.objects.all(), required=False,
                                       widget=forms.Select(attrs={'class': 'form-control'}))
    deduction_item = forms.JSONField(required=False)

    forwarders = forms.ModelChoiceField(label=_("Forwarders"), queryset=Forwarders.objects.all(), required=False,
                                        widget=forms.Select(attrs={'class': 'form-control'}))

    # CHOICES= (
    #     ('1', 'Ausgang'),
    #     ('0', 'Eingang')

    #     )
    # status = forms.CharField(widget=forms.Select(choices=CHOICES))
    class Meta:
        model = Transaction

        fields = ["vehicle", "article", "customer", "supplier", "container", "first_weight", "second_weight", "place_of_delivery",
                  "net_weight", "firstw_alibi_nr", "firstw_date_time", "secondw_alibi_nr", "secondw_date_time", "item_price", "comment", "trailer",
                  "vehicle_weight_flag", "vehicle_second_weight_flag", "trans_flag", "price_per_item", "deduction", "deduction_weight",
                  'total_price', "contract_number", "material_weight", "status", "deduction_item", "forwarders", "driver_name", "temp", "project_number"
                  ]
        labels = {
            'first_weight': _("First Weight"),
            'net_weight': _("Net Weight"),
            'second_weight': _("Second Weight"),
            'secondw_alibi_nr': _("Alibi Nr"),
            'material_weight': _("Material Weight"),
        }

    # def save(self, commit=True):
    #     instance = self.instance
    #     if self.cleaned_data['deduction_item']:
    #         if len(self.cleaned_data['deduction_item']) > 0:
    #             data = instance.deduction.all()
    #             for i in data:
    #                 instance.deduction.remove(i)
    #                 instance.save()
    #             for i in self.cleaned_data['deduction_item']:
    #                 if i != '':
    #                     instance.deduction.add(int(i))
    #                     instance.save()
    #
    #     # if self.cleaned_data['deduction'] == '' or self.cleaned_data['deduction'] is None:
    #     #     data = instance.deduction.all()
    #     #     for i in data:
    #     #         instance.deduction.remove(i)
    #     #         instance.save()
    #     # if self.cleaned_data['deduction']:
    #     #     instance.deduction.add(self.cleaned_data['deduction'])
    #     #     instance.save()
    #
    #     return instance

    def clean(self):
        data = self.cleaned_data

        if data['deduction'] is None:
            data['deduction'] = []
        return data


class yardListForm(forms.ModelForm):
    class Meta:
        model = Yard_list

        fields = ["name", "place", "country"]
        labels = {
            'name': _("Name"),
            'place': _("Place"),
            'country': _("Country"),
        }


class ArticleForm(forms.ModelForm):
    # group = forms.ChoiceField(widget=forms.Select(attrs={'class': 'form-control'}),
    #                           choices=Article.GROUP_CHOICES, label=_("Type"))

    # revenue_group = forms.ChoiceField(widget=forms.Select(attrs={'class': 'form-control'}),
    #                                   choices=Article.REVENUE_GROUP_CHOICES, label=_("Revenue Group"), required=False)
    description = forms.CharField(label=_("Description"), widget=forms.Textarea(attrs={'class': 'form-control'}),
                                  required=False)
    ware_house = forms.ModelChoiceField(label=_("Warehouse"), required=False, queryset=Warehouse.objects.all(),
                                        widget=forms.Select(attrs={'class': 'form-control'}))
    supplier = forms.ModelChoiceField(label=_("Supplier"), queryset=Supplier.objects.all(), required=False,
                                      widget=forms.Select(attrs={'class': 'form-control'}))

    class Meta:
        model = Article

        fields = ["supplier", "name", "description", "short_name", "vat", "minimum_amount", "price1", "price2",
                  "price3", "price4", "price5", "second_price1", "second_price2", "second_price3", "second_price4", "second_price5",
                  "avv_num", "account", "min_quantity", "density",
                  "list_price_net", "list_price_gross", "ean", "ware_house", "article_number", "unit"]
        labels = {
            'name': _("Name"),
            'short_name': _("Short Name"),
            'vat': _("Vat"),
            'minimum_amount': _("Minimum Amount"),
            'price1': _("Nettopreis 1 [€]"),
            'price2': _("Nettopreis 2 [€]"),
            'price3': _("Nettopreis 3 [€]"),
            'price4': _("Nettopreis 4 [€]"),
            'price5': _("Nettopreis 5 [€]"),
            'second_price1': _("Bruttopreis  1 [€]"),
            'second_price2': _("Bruttopreis  2 [€]"),
            'second_price3': _("Bruttopreis  3 [€]"),
            'second_price4': _("Bruttopreis  4 [€]"),
            'second_price5': _("Bruttopreis 5 [€]"),
            'density': _("Materialdichte"),
            # 'discount': _("Discount"),
            'account': _("Account"),
            # 'cost_center': _("Cost Centre"),
            # 'unit': _("Unit"),
            'min_quantity': _("Min Quantity"),
            # 'revenue_account': _("Revenue Account"),
            'list_price_net': _("List Price Net"),
            'list_price_gross': _("List price gross "),
            'ean': _("EAN"),
            'avv_num': _("AVV-Number"),
            'unit': _("Einheit"),
        }


class BuildingSiteForm(forms.ModelForm):
    class Meta:
        model = BuildingSite

        fields = ["name", "short_name", "place", "street", "pin", "infotext"]
        labels = {
            'name': _("Name"),
            'short_name': _("Short Name"),
            'place': _("Place"),
            'street': _("Street"),
            'pin': _("Pin"),
            'infotext': _("Infotext"),
        }


class Delivery_noteForm(forms.ModelForm):
    class Meta:
        model = Delivery_note

        fields = ["lfd_nr", "file_name"]
        labels = {
            'lfd_nr': _("Lfd Nr"),
            'file_name': _("File Name"),
        }


class VehicleForm(forms.ModelForm):
    license_plate = forms.CharField(label=_(
        "License Plate"), widget=forms.TextInput(attrs={'class': 'form-control'}))
    license_plate2 = forms.CharField(label=_("License Plate 2"), required=False,
                                     widget=forms.TextInput(attrs={'class': 'form-control'}))
    # forwarder = forms.CharField(widget=forms.TextInput(attrs={'class': 'form-control'}))
    vehicle_weight_date = forms.DateField(label=_("Weight Date"), required=False,
                                          widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}))
    vehicle_weight_time = forms.TimeField(label=_("Weight Time"), required=False,
                                          widget=forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}))
    vehicle_weight_date2 = forms.DateField(label=_("Weight Date"), required=False,
                                           widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}))
    vehicle_weight_time2 = forms.TimeField(label=_("Weight Time"), required=False,
                                           widget=forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}))

    forwarder = forms.ModelChoiceField(label=_("Forwarder"), queryset=Forwarders.objects.all(), required=False,
                                       widget=forms.Select(attrs={'class': 'form-control'}))

    country = forms.CharField(label=_("Country"), required=False,
                              widget=forms.TextInput(attrs={'class': 'form-control'}))
    email = forms.CharField(label=_("Email"), required=False,
                            widget=forms.TextInput(attrs={'class': 'form-control'}))
    vehicle_weight = forms.IntegerField(label=_("Tara Zugmaschine"), required=False,
                                        widget=forms.NumberInput(attrs={'class': 'form-control'}))
    vehicle_weight2 = forms.IntegerField(label=_("Tara Trailer"), required=False,
                                         widget=forms.NumberInput(attrs={'class': 'form-control'}))
    vehicle_weight_id = forms.CharField(label=_("Vehicle Weight Id"), required=False,
                                        widget=forms.TextInput(attrs={'class': 'form-control'}))
    vehicle_weight_id2 = forms.CharField(label=_("Vehicle Weight Id"), required=False,
                                         widget=forms.TextInput(attrs={'class': 'form-control'}))
    vehicle_type = forms.ChoiceField(widget=forms.Select(attrs={'class': 'form-control'}),
                                     choices=Vehicle.VEHICLE_CHOICES, label=_("Type"), required=False)
    street = forms.CharField(label=_("Street"), required=False,
                             widget=forms.TextInput(attrs={'class': 'form-control'}))
    pin = forms.CharField(label=_("PLZ"), required=False,
                          widget=forms.TextInput(attrs={'class': 'form-control'}))
    place = forms.CharField(label=_("Place"), required=False,
                            widget=forms.TextInput(attrs={'class': 'form-control'}))
    # fahrzeugtypen = forms.ChoiceField(label=_("Fahrzeugtypen"), required=False,
    #                         widget=forms.Select(attrs={'class': 'form-control'}),
    #                         choices=Vehicle.VEHICLE_TYPE_CHOICES)

    # self_tara = forms.CharField(label=_('Self Tara'), widget=forms.RadioSelect(attrs={'class': 'form-radio'}), required=True)

    class Meta:
        model = Vehicle

        fields = ["license_plate", "forwarder", "country", "email", "vehicle_weight", "vehicle_weight2",
                  "vehicle_weight_id", "vehicle_weight_date", "vehicle_weight_time",
                  "vehicle_weight_id2", "vehicle_weight_date2", "vehicle_weight_time2", "taken", "license_plate2",
                  "vehicle_type", "owner", "driver_name", "self_tara", "street", "pin", "place"]
        labels = {
            'taken': _("Taken"),
            'owner': _("Vehicle Owner"),
            'driver_name': _("Driver"),
            'self_tara': _("Speichertarawiegung erlaubt"),
        }


class CombinationForm(forms.ModelForm):
    vehicle = forms.ModelChoiceField(label=_("License Plate"), queryset=Vehicle.objects.all(), required=False,
                                     widget=forms.Select(attrs={'class': 'form-control'}))
    article = forms.ModelChoiceField(label=_("Material"), queryset=Article.objects.all(), required=False,
                                     widget=forms.Select(attrs={'class': 'form-control'}))
    customer = forms.ModelChoiceField(label=_("Customer"), queryset=Customer.objects.all(), required=False,
                                      widget=forms.Select(
                                          attrs={'class': 'form-control', 'id': 'id_delivery_customer'}))
    supplier = forms.ModelChoiceField(label=_("Supplier"), queryset=Supplier.objects.all(), required=False,
                                      widget=forms.Select(attrs={'class': 'form-control'}))
    place_of_delivery = forms.ModelChoiceField(label=_("Lieferant"), queryset=PlaceOfDelivery.objects.all(), required=False,
                                      widget=forms.Select(attrs={'class': 'form-control'}))
    container = forms.ModelChoiceField(label=_("Container"), queryset=Container.objects.all(), required=False,
                                       widget=forms.Select(attrs={'class': 'form-control'}))
    # contracts = forms.ModelMultipleChoiceField(label=_("Auftrag"), queryset=Contract.objects.all(), required=False,
    #                                            widget=forms.Select(attrs={'class': 'form-control', 'multiple': 'multiple'}))
    building_site = forms.ModelChoiceField(label=_("Baustelle"), queryset=BuildingSite.objects.all(), required=False,
                                           widget=forms.Select(attrs={'class': 'form-control'}))
    forwarders = forms.ModelChoiceField(label=_("Forwarders"), queryset=Forwarders.objects.all(), required=False,
                                        widget=forms.Select(attrs={'class': 'form-control'}))
    yard = forms.ModelChoiceField(label=_("Yard"), queryset=Yard_list.objects.all(), required=False,
                                  widget=forms.Select(attrs={'class': 'form-control'}))
    status = forms.ChoiceField(widget=forms.Select(attrs={'class': 'form-control', 'title': 'Biite Richtung auswählen',
                                                          'oninvalid': 'setCustomValidity("Biite Richtung auswählen")',
                                                          'oninput': 'this.setCustomValidity("")'}),
                               choices=Combination.STATUS_CHOICES, label=_("Richtung"), required=True,
                               error_messages={'required': "Biite Richtung auswählen"})
    tara_with_mobile = forms.BooleanField(widget=forms.CheckboxInput(),
                                          label=_("Wiegung mit Tara"), required=False)

    class Meta:
        model = Combination

        fields = ["ident", "customer", "vehicle", "building_site", "short_name", "supplier", "forwarders", "article",
                  "container", "yard", "transaction_id", "status", "tara_with_mobile", "place_of_delivery", "contracts"]

        labels = {
            'short_name': _("Name"),
            'contracts': _("Auftrag")
        }

class SettingsForm(forms.ModelForm):
    customer = forms.ChoiceField(label=_("Customer"), widget=forms.RadioSelect(attrs={'class': 'form-radio'}),
                                 required=False, choices=Settings.CUSTOMER_CHOICES)
    supplier = forms.ChoiceField(label=_("Supplier"), widget=forms.RadioSelect(attrs={'class': 'form-radio'}),
                                 required=False, choices=Settings.SUPPLIER_CHOICES)
    article = forms.ChoiceField(label=_("Article"), widget=forms.RadioSelect(attrs={'class': 'form-radio'}),
                                required=False, choices=Settings.ARTICLE_CHOICES)

    class Meta:
        model = Settings
        # fields = ["name","customer","article","supplier","show_article","show_supplier","show_yard","show_forwarders","show_storage","show_building_site","read_number_from_camera","language"]
        fields = ["name", "customer", "article", "supplier",
                  "language", "smtp_support", "smtp_creds", "company_email"]
        # labels = {"read_number_from_camera":"Read Number plate from camera"}

        labels = {
            'name': _("Name"),
            'language': _("Language"),
            'article': _("Article"),
            'company_email': _("Company Email")
        }


class SmtpForm(forms.ModelForm):
    password = forms.CharField(widget=forms.PasswordInput)

    class Meta:
        model = SMTPCred
        fields = ["host", "port", "username", "password", "sender_address"]


class ContainerForm(forms.ModelForm):
    # name = forms.CharField(label=_("Name"), widget=forms.TextInput(
    #     attrs={'class': 'form-control'}))
    # group = forms.IntegerField(
    #     label=_("Group"), widget=forms.NumberInput(attrs={'class': 'form-control'}))
    next_exam = forms.DateField(label=_("Next Exam"), required=False,
                                          widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}))
    # last_site = forms.ModelChoiceField(queryset=BuildingSite.objects.all(), widget=forms.Select(attrs={'class': 'form-control'}))
    class Meta:
        model = Container

        fields = [ "container_type", "container_weight", "volume", "container_number", "check_digit", "curb_weight",
                  "total_weight", "last_site", "payload", "maximum_gross_weight", "tare_weight", "payload_container_volume",
                  "next_exam", "waste_type", "hazard_warnings", "unit"]
        labels = {
            'container_type': _("Container Type"),
            'container_weight': _("Container Weight"),
            'volume': _("Volume"),
            'container_number': _("Container number"),
            'maximum_gross_weight': _("Maximum gross weight"),
            'tare_weight': _("Tare weight"),
            'payload_container_volume': _("Payload container volume"),
            'next_exam': _("Next exam"),
            'waste_type': _("Waste type"),
            'hazard_warnings': _("Hazard warnings")
        }


class SSUpdateForm(forms.Form):
    field_type = forms.CharField()
    user = forms.IntegerField()
    values = SimpleArrayField(forms.IntegerField(), required=False)


class ContractForm(forms.ModelForm):
    customer = forms.CharField(required=False)
    supplier = forms.CharField(required=False)
    vehicles = forms.CharField(required=False)

    class Meta:
        model = Contract
        fields = ["contract_number", "customer", "project_number", "contract_status", "status", "forwarders", "price_group",
                  "required_materials",
                  "start_date", "end_date", "reserved_date", "allow_tara", "supplier",
                  "vehicles", "name", "target_value", "unit"]

    def clean(self):
        data = self.cleaned_data

        if data['customer'] == '0':
            data['customer'] = None
        else:
            data['customer'] = Customer.objects.get(id=int(data['customer']))

        if data['supplier'] == '0':
            data['supplier'] = []
        else:
            sup = ast.literal_eval(data['supplier'])
            data['supplier'] = []
            for i in range(len(sup)):
                if int(sup[i]) == 0:
                    data['supplier'] = []
                else:
                    data['supplier'].append(
                        Supplier.objects.get(id=int(sup[i])))

        if data['vehicles'] == '0':
            data['vehicles'] = []
        else:
            veh = ast.literal_eval(data['vehicles'])
            data['vehicles'] = []
            for i in range(len(veh)):
                if int(veh[i]) == 0:
                    data['vehicles'] = []
                else:
                    data['vehicles'].append(
                        Vehicle.objects.get(id=int(veh[i])))

        for i in range(len(data['required_materials'])):
            if data['required_materials'][i]['material'] == '0':
                data['required_materials'][i]['material'] = None
        return data


class WarehouseForm(forms.ModelForm):
    class Meta:
        model = Warehouse

        fields = ["name", "stock_designation", "stock_number", "stock_item", "locked_warehouse", "parking_space",
                  "reserved", "available", "total_stock", "store", "outsource", "storage_location",
                  "warehouse_street", ]

        labels = {
            'name': _("Name"),
            'stock_designation': _("Stock Designation"),
            'stock_number': _("Stock Number"),
            'stock_item': _("Stock Item"),
            'locked_warehouse': _("Locked Warehouse"),
            # 'ordered': _("Ordered"),
            # 'production': _("Production"),
            'reserved': _("Reserved"),
            'available': _("Available"),
            'total_stock': _("Total Stock"),
            'store': _("Store"),
            'outsource': _("Outsource"),
            'storage_location': _("Storage location"),
            'warehouse_street': _("Warehouse street"),
            # 'minimum_quantity': _("Minimum quantity"),
        }


class HandTransmitterForm(forms.ModelForm):
    name = forms.CharField(label=_("Name"), required=False,
                           widget=forms.TextInput(attrs={'class': 'form-control'}))
    devise_id = forms.CharField(label=_("Geräte-ID"), required=False,
                                widget=forms.TextInput(attrs={'class': 'form-control'}))
    combination = forms.ModelChoiceField(label=_("ID"), queryset=Combination.objects.all(), required=False,
                                         widget=forms.Select(
                                             attrs={'class': 'form-control', 'id': 'id_combination'}))

    class Meta:
        model = HandTransmitter

        fields = ["name", "devise_id", "combination"]

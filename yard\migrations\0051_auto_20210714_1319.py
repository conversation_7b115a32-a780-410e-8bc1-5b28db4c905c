# Generated by Django 3.1.1 on 2021-07-14 13:19

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0050_transaction_combination_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='article',
            name='name',
            field=models.CharField(max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='first_weight',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='net_weight',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='second_weight',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='vehicle',
            name='license_plate',
            field=models.Char<PERSON>ield(max_length=100, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='vehicle',
            name='ss_role_access',
            field=models.ManyToManyField(blank=True, null=True, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='vehicle',
            name='trailor_weight',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
    ]

# Generated by Django 3.1.1 on 2022-05-26 11:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0117_article_list_price_gross'),
    ]

    operations = [
        migrations.AlterField(
            model_name='combination',
            name='contracts',
            field=models.ManyToManyField(blank=True, related_name='contracts', to='yard.Contract'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='supplier',
            field=models.ManyToManyField(blank=True, to='yard.Supplier'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='vehicles',
            field=models.ManyToManyField(blank=True, to='yard.Vehicle'),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='invoice',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.transaction'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='transaction',
            name='deduction',
            field=models.ManyToManyField(blank=True, to='yard.Article'),
        ),
    ]

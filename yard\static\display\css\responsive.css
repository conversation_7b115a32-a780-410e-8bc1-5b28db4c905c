@media (min-width: 575px){
	.pr-sm-7{padding-right: 7px !important;}
	.pl-sm-7{padding-left: 7px !important;}
	.plr-sm-7{padding-left: 7px !important; padding-right: 7px !important;}
}
@media (min-width: 768px){
	.pr-md-7{padding-right: 7px !important;}
	.pl-md-7{padding-left: 7px !important;}
	.plr-md-7{padding-left: 7px !important; padding-right: 7px !important;}
}


@media (min-width: 1200px) {
	.container {max-width: 1170px;}
}
@media (min-width: 1400px) {
	.container {max-width: 1370px;}
	.NewCardBoxClrDesign {padding: 15px;}
	#wrapper.toggled{padding-left: 340px;}
	#wrapper{padding: 20px; padding-right: 5px;}
	#sidebar-wrapper {
    min-width: 310px;
    max-width: 310px;
    margin-left: 0;
    left: 30px;}
    #sidebar-wrapper ul li a {font-size: 16px; padding: 15px 20px;}
    #sidebar-wrapper ul li ul {padding-left: 20px;}
    .logoutFix {max-width: 310px;}
    .mb_20{margin-bottom: 20px !important;}
    .wf_btnRow>li>button{min-width: 60px;}
	.mainHomePage #sidebar-wrapper {left: 20px;}
	.responsive-iframe {height: 230px;}
	.cardTitleCol {
		margin-top: -36px;
	  }
	#sidebar>.list-unstyled {height: calc(100vh - 202px);}
}
@media (min-width: 1500px) {}
@media (max-width: 1366px) {}
@media (max-width: 1280px) {}
@media (max-width: 1199px) {}
@media (max-width: 1024px) {}
@media (max-width: 991px) {}
@media (max-width: 860px) {}
@media (max-width: 768px) {
	#sidebarCollapse{nav-right: auto;}
}
@media (max-width: 767px) {}
@media (max-width: 575px) {}
@media (max-width: 480px) {}
@media (max-width: 414px) {}
@media (max-width: 375px) {}

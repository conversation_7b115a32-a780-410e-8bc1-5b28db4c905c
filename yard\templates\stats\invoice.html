{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block head %}
{%load static%}
{% endblock %}
{% block content %}
    <!-- /#sidebar-wrapper -->
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              <p>{% translate 'Invoices' %}</p>
            </div>
          </div>
        </div>
        <form method="POST" id="invoice_form" enctype="multipart/form-data" action="{% url 'get_invoices' %}">
        <div class="container">
         {% if messages %}
						{% for message in messages %}
						<div id="msg" class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}" role="alert">{{ message }}</div>
						{% endfor %}
                  {% endif %}
                  {% csrf_token %}
                  <div class="row">
                  <div class="col-xl-2 col-lg-2 col-md-5 col-sm-12 col-12">
                    <div class="form-group">
                       <div class='input-group date' id='datetimepicker6'>
                         <label >{% translate 'Date' %}</label>
                          <input type='date' class="form-control" id='del_from_date' name="fromdate" />
                          <span class="input-group-addon">
                          <span class="glyphicon glyphicon-calendar"></span>
                          </span>
                       </div>
                    </div>
                 </div>
                 <div class="col-xl-2 col-lg-2 col-md-5 col-sm-12 col-12" id="to-date-select">
                    <div class="form-group">
                       <div class='input-group date' id='datetimepicker7'>
                         <label >{% translate 'To' %}</label>
                          <input type='date' class="form-control" id="del_to_date" name="todate"/>
                          <span class="input-group-addon">
                          <span class="glyphicon glyphicon-calendar"></span>
                          </span>
                       </div>
                    </div>
                 </div>
                 <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12">
                  <label>{% translate "Customer" %}:</label>
                  <select class="form-control select2" name="customer">
                      <option value="0" >{% translate 'Select Customer' %}</option>
                      {% for i in customer %}
                      <option value={{i.id}} >{{i.name1}}</option>
                      {% endfor %}
                  </select>
              </div>
              <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12">
               <label>{% translate "Material" %}:</label>
               <select class="form-control select2" name="article">
                   <option value="0" >{% translate 'Select Material' %}</option>
                   {% for i in article %}
                   <option value={{i.id}} >{{i.name}}</option>
                   {% endfor %}
               </select>
              </div>
              
                </div>
              

       </div>
       
        <!--table strat-->
        <div class="row">
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
              <label>{% translate "Show entries" %}:</label>
             <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
             </select>
           </div>
           <div class="col-xl-2 col-lg-2 col-md-12 col-sm-12 col-12">
            <div class="form-group" style="margin-top:35px">
            <button type="submit" class="btn btn-primary">Filter Invoice</button>
            {% comment %} {% if data|length > 1 %} {% endcomment %}
            <input type="hidden" id="invoices" name="multiple_ids" />
            <button type="submit" class="btn btn-primary" name="multiple" id="print_all">Print All</button>
            {% comment %} {% endif %} {% endcomment %}
          </div>
          </div>
        </div>
      </form>
        <div class="container">
          <div class="row">
            <div class="table-responsive pr-2">
              <table class="table table-striped table-hover table-bordered mt-3 mr-3" width="100%" id="deliveryNoteTable">
                <thead>
                  <tr> 
                    <th>{% translate 'Action' %}</th>
                    <th> lfd.Nr</th>
                    <th>{% translate 'Customer' %} </th>
                    <th>{% translate 'Article' %} </th>
                    <th>{% translate 'Supplier' %} </th>
                    <th width="10%">{% translate 'Total Price' %} </th>
                    <th>{% translate 'Created on' %}</th>
                  </tr>
                </thead>
                <tbody class="mt-4" id="tbl">
                  {% for i in data %}
                  <tr>
                    <input type="hidden" value={{i.id}} class="id_input" />
                    <td><a class="hover-pointer-cursor" target="_blank" href="/stats/invoice/?id={{ i.id }}"><i
                      class="fas fa-file-pdf ml-2" title="Print Invoice"></i></a></td>
                    <td>{{i.lfd_nr}}</td>
                    <td>{{i.customer.name1|default_if_none:" "}}</td>
                    <td>{{i.article.name|default_if_none:" "}}</td>
                    <td>{{i.supplier.name|default_if_none:" "}}</td>
                    <td>{{i.total_price|default_if_none:"0"}}</td>
                    <td>{{i.created_date_time}}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>        
          </div>
        </div>

        
    </div>
    <div id="MyModal" style="overflow-y: auto;" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
   <div class="modal-dialog modal-lg">
      <div class="modal-content  p-4">
         <div class="modal-body">
            <img class="img-responsive" src="" style="width:100%"/>
         </div>
         <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
         </div>
      </div>
   </div>
</div>
    <div id="MyPopupImg" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
          <div class="modal-dialog modal-lg">
              <!-- Modal content-->
              <div class="modal-content p-4">
                  <div class="modal-header modal-header-custom">                  
                      <h4 class="modal-title">{% translate 'Captured Images' %}</h4>
                      <button type="button" class="close" data-dismiss="modal">
                        &times;</button>
                  </div>
                  <div class="modal-body">
                    <center> </center>
                  </div>
                  <div class="modal-footer">
                      <input type="button" id="btnClosePopup" value="Close" class="btn btn-secondary" />
                  </div>
              </div>
          </div>
      </div>
{% endblock %}
{% block scripts %}
<script src="{% static 'stats/js/stats_custom.js'%}"></script>
<script type="text/javascript" src="{% static 'stats/js/jszip.js'%}"></script>
<script>
    // DataTable
    $(document).ready(function() {
      var table = $('#deliveryNoteTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
<!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
<!--                       if ( that.search() !== this.value ) {-->
<!--                           that.search( this.value ).draw();-->
<!--                       }-->
<!--                   } );-->
               } );
           }
          });

      $("#deliveryNoteTable_filter").hide()

      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      } );

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      } );

 });

$("#id_first_weight").keyup(function(){
  first = $(this).val();
  second = $("#id_second_weight").val();
  net = Math.abs(second-first);
  $("#id_net_weight").val(net);
})

$("#id_second_weight").keyup(function(){
  first = $("#id_first_weight").val();
  second = $(this).val();
  net = Math.abs(second-first);
  $("#id_net_weight").val(net);
});

$(".loadW").click(function(e){
  $('.collapse_18').addClass('show');
  window.location="#div_delivery_form";
});
$(".loadWD").dblclick(function(e){
  $('.collapse_18').addClass('show');
  window.location="#div_delivery_form";
});

$("#print_all").click(function(e){
  e.preventDefault()
  arr = new Array()
  $(".id_input").each(function(i,v){
    console.log(v.value)
    arr.push($(v).val())
  })
  $("#invoices").val(arr)

  var form = $("#invoice_form");
    form.attr("target", "_blank");
    form.attr("action", "/stats/invoice/")
    form.submit();
})

</script>
{% endblock %} 

<!DOCTYPE html>
<html lang="en">
<head>
    {%block head%}
    {%load static%}
    {%endblock%}
    {% load i18n %}
    {% load l10n %}
    {%load custom_template_filters%}
    <meta charset="utf-8">
    <title>ID</title>
    <style>
    .Row {
        display: table;
        width: 100%; /*Optional*/
        table-layout: fixed; /*Optional*/
        border-spacing: 40px; /*Optional*/
    }
    .Column {
        display: table-cell;
        border-left: 10px;
    }
    table { -pdf-keep-with-next: true; }
    p { margin: 0; -pdf-keep-with-next: true; }
    p.separator { -pdf-keep-with-next: false; font-size: 6pt; }

    .header_logo{
        text-align: right;
        padding-bottom: 20px;
        padding-right: 20px;
        width:100%;
        height:auto;
    }
    .header_logo img{
        max-width: 100%;
        max-height: auto;
    }
    .right-column {
        width: 200px;
        text-align: right;
        padding-top: -150px;
    }
    .title{
        font-size: 16px;
    }
    .main_date{
        padding-top: 30px;
    }
    .year{
        text-align: left;
    }
    .month{
        text-align: center;
        padding-top: -34px;
    }
    .day{
        text-align: center;
        padding-top: -34px;
        padding-left: 600px;
    }
    .table-title{
        padding-top: 20px;
    }
    .table-title b{
        font-size: 20px;
    }
    .main_table{
        margin-top: 30px;
    }
    .table{
        width:100%;
    }
    .main_table .table .tr .th{
        border-top: 1px solid #000;
        border-bottom: 1px solid #000;
        padding: 4px;
        text-align: left;
    }
    .main_table .table .tr .td{
        text-align: left;
    }
    .footer{
        padding-top: 60px;
    }
    .footer .footer_first span{
        font-size: 10px;
    }

    </style>
</head>
   <body>
      <div class="a4_sheet">
         <div class="row main_row" style="padding-top:20%;">
            <div class="header_logo">
                   {% if logo.logo.url is not None %}
                   <img src="{{ logo.logo.url }}" style="object-fit: contain;height: 70px; width: 170px;"/>
                   {% endif %}
            </div>
            <div class="left-column">
                <font color="maroon">{{logo.heading}}</font> 
                <br>
                <br>
                <br>
                <br>
                <strong class="title"><b>ID:</b> {% if data.ident is not None%} {{ data.ident }} {% endif %} </strong><br><br><br>
                <span>
                <b>Kunden:</b> {% if data.customer.name1 is not None%} {{ data.customer.name1 }} {% endif %} <br>
                {% if data.customer.street is not None%}
                    {{ data.customer.street }} {% endif %}<br>
                    
                {% if data.customer.place is not None%}
                    {{ data.customer.place }} {% endif %}<br>
                {% if data.customer.post_office_box is not None%}
                    {{ data.customer.post_office_box }}
                    {% endif %}
                </span><br>
                <b>Baustelle:</b> <br>
                <span>
                {% if data.supplier.name is not None%}
                {{ data.supplier.name }}
                {% endif %}</span><br>
                </span><br>
                <b>Artikel:</b> <br>
                <span>{% if data.article.name is not None%} {{ data.article.name }} {% endif %}</span>
            </div>

            <div class="right-column">
            <strong style="font-size:14px;"><b>{{text.text}}</b></strong>
                <img src="data:image/png;base64, {{ qr_code }}"/>

            </div>
<br>

<hr>

            <div class="main_date">
                <div class="year">
                    <b>ID erstellt am:</b> <br>
                    <span>{{ data.created_date_time }}</span>
                </div> 
                <div class="month">
                    <b>ID Aktualisiert am:</b> <br>
                    <span>{{ data.updated_date_time }}</span>
                </div> 

                <div class="day">
                    <b>Datum:</b> <br>
                    <span>{{ date}}</span>
                </div> 
            </div>

<br>
<br><br>
<br><br>
<br><br>
<br><br>
<br><br>
<br><br>
<br><br>
<br><br>
<br>
           

            <div class="footer">
                <div class="footer_first">
                    <span>Sie haben noch Fragen? Sie erreichen uns täglich von 08:00 bis 17:00 Uhr unter (Company Telefon) oder per E-Mail {{email.company_email}} </span>
                        <br><br>
                    <span>Wir freuen uns auf die Zusammenarbeit </span>
                        <br><br>
                    <span>Mit freundlichen Grüßen</span>
                </div>
            </div>
        </div>
      </div>
   </body>
</html>

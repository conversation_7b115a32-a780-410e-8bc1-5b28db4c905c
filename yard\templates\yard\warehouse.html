
{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}


    <!-- /#sidebar-wrapper -->
    <!-- <div id="content"> -->
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              <p>{% translate 'Warehouse' %}</p>
            </div>
          </div>
        </div>

        <div class="row">
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
              <label>{% translate "Show entries" %}:</label>
             <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
             </select>
           </div>
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
               <label>{% translate "Search" %}:</label>
               <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
           </div>
        </div>

        <!--table strat-->
        <div class="table-responsive">
          <table class="table table-striped table-hover table-bordered mt-3 " width="100%" id="warehouseTable">
            <thead>
              <tr> {% if request.user.is_superuser %}
                <th>{% translate "Action" %}</th> {% endif %}
{#                <th>{{form.name.label}}</th>#}
                <th>{{ "Lagernummer" }}</th>
                <th>{{ "Lagerbezeichnung" }}</th>
                <th>{{ "Straße" }}</th>
              <th>{{ "Stellplatz" }}</th>
                {% comment %} <th>{{form.warehouse_street.label}}</th> {% endcomment %}
                <!-- <th>{{form.stock_item.label}}</th> -->
                {% comment %} <th>{{form.locked_warehouse.label}}</th> {% endcomment %}
                {% comment %}<th>{{form.ordered.label}}</th>{% endcomment %}
                {% comment %} <th>{{form.production.label}}</th> {% endcomment %}
{#                <th>{{form.reserved.label}}</th>#}
{#                <th>{{form.available.label}}</th>#}
{#                <th>{{form.total_stock.label}}</th>#}
{#                <th>{{form.store.label}}</th>#}
{#                <th>{{form.outsource.label}}</th>#}
              </tr>
            </thead>

            <tbody class="mt-4">
                {% for data in dataset %}
                <tr class="loadWD" ondblclick="javascript:loadWarehouseDetails('{{ data.id }}')"> {% if request.user.is_superuser %}
                    <td><a class="loadW" href="javascript:loadWarehouseDetails('{{ data.id }}')"><i class="fas fa-pencil-alt text-primary"></i></a><a class="confirmdelete" href="{% url 'warehouse_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a></td>
                 {% endif %}
{#                  <td>{{ data.name|default_if_none:"-" }}</td>#}
                  <td>{{ data.stock_number|default_if_none:"-" }}</td>
                  <td>{{ data.stock_designation|default_if_none:"-" }}</td>
                  <td>{{ data.storage_location|default_if_none:"-" }}</td>
                  <td>{{ data.parking_space|default_if_none:"-" }}</td>
                  {% comment %} <td>{{ data.warehouse_street }}</td> {% endcomment %}
                  <!-- <td>{{ data.stock_item|default_if_none:"-" }}</td> -->
                  {% comment %} <td>{{ data.locked_warehouse }}</td> {% endcomment %}
                  {% comment %}<td>{{ data.ordered }}</td>{% endcomment %}
                  {% comment %} <td>{{ data.production }}</td> {% endcomment %}
{#                  <td>{{ data.reserved|default_if_none:"-" }}</td>#}
{#                  <td>{{ data.available|default_if_none:"-" }}</td>#}
{#                  <td>{{ data.total_stock|default_if_none:"-" }}</td>#}
{#                  <td>{{ data.store|default_if_none:"-" }}</td>#}
{#                  <td>{{ data.outsource|default_if_none:"-" }}</td>#}
                </tr>
            {% endfor %}
            </tbody>
<!--              <tfoot hidden>-->
<!--                  <tr>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text"class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text"class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->


<!--                  </tr>-->
<!--               </tfoot>-->

          </table>
        </div>
        <!--table end-->
      </div>
      <div class="container">
        <div class="row mb-5">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
              <!-- Material form login -->
              <div class="card" id="warehouse-form-section">
                   {% if request.user.is_superuser %}
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                      <h5 class="card-header info-color white-text py-3">
                        <div class="panel-heading">
                              <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12"><h3 class="panel-title">
                                  <p class="mb-0 pt-2 text-color text_color float-left">{% translate "Warehouse" %}</p></h3>
                                  <button type="button" class="btn btn-blue btn-blue-fill loadW" style="float:right;">Neue Eingabe</button>
                                </div>
                                </div>
                        </div>
                      </h5>
                    </div>
                  </div>
                  {% endif %}
            
                   <!--first forms satrat-->
                  <div id="collapse2031" class="collapse" >
                      <div class="panel-body"> 
                        <div class="contanier">
                            <form class="form-group" method="POST" enctype="multipart/form-data">
                              {% csrf_token %}
                              <input type="hidden" name="id" id="id" value="">
                              <div class="row">               
                                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
{#                                    <div>#}
{#                                    <label>{{form.name.label}}</label>#}
{#                                    {{ form.name}} {{ form.name.errors}}#}
{#                                  </div>#}

                                    <div>
                                    <label>{{ "Lagerbezeichnung" }}</label>
                                    {{ form.stock_designation}} {{ form.stock_designation.errors}}
                                  </div>

                                    <div>
                                    <label>{{ "Lagernummer" }}</label>
                                    {{ form.stock_number}} {{ form.stock_number.errors}}
                                  </div>

                                     <div>
                                    <label>{{ "Stellplatz" }}</label>
                                    {{ form.parking_space}} {{ form.parking_space.errors}}
                                  </div>


                                    <!-- <div>
                                    <label>{{form.stock_item.label}}</label>
                                    {{ form.stock_item}} {{ form.stock_item.errors}}
                                  </div> -->

                                    <!-- <div>
                                    <label>{{form.locked_warehouse.label}}</label>
                                    {{ form.locked_warehouse}} {{ form.locked_warehouse.errors}}
                                  </div> -->
                                  {% comment %}
                                    <div>
                                    <label>{{form.ordered.label}}</label>
                                    {{ form.ordered}} {{ form.ordered.errors}}
                                  </div>
                                  {% endcomment %}
                                </div>
                                 <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">

                                      <div>
                                    <label>{{ "Straße" }}</label>
                                    {{ form.storage_location}} {{ form.storage_location.errors}}
                                  </div>

                                        <div>
                                    <label>{{ "Ort" }}</label>
                                    {{ form.warehouse_street}} {{ form.warehouse_street.errors}}
                                  </div>

                                  {% comment %}
                                    <div>
                                        <label>{{form.production.label}}</label>
                                        {{ form.production}} {{ form.production.errors}}
                                    </div>
                                    {% endcomment %}
{#                                    <div>#}
{#                                        <label>{{form.reserved.label}}</label>#}
{#                                        {{ form.reserved}} {{ form.reserved.errors}}#}
{#                                    </div>#}
{#                                    <div>#}
{#                                        <label>{{form.available.label}}</label>#}
{#                                        {{ form.available}} {{ form.available.errors}}#}
{#                                    </div>#}

{#                                    <div>#}
{#                                        <label>{{form.total_stock.label}}</label>#}
{#                                        {{ form.total_stock}} {{ form.total_stock.errors}}#}
{#                                    </div>#}
{#                                    <div>#}
{#                                        <label>{{form.store.label}}</label>#}
{#                                        {{ form.store}} {{ form.store.errors}}#}
{#                                    </div>#}
{#                                    <div>#}
{#                                        <label>{{form.outsource.label}}</label>#}
{#                                        {{ form.outsource}} {{ form.outsource.errors}}#}
{#                                    </div>#}
                                    {% comment %}
                                    <div>
                                        <label>{{form.minimum_quantity.label}}</label>
                                        {{ form.minimum_quantity}} {{ form.minimum_quantity.errors}}
                                    </div>
                                    {% endcomment %}

                                 </div>
                                 <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                    <button id="submit" type="submit" class="btn btn-primary ml-3 mt-2"><i class="fas fa-save ml-2"></i>{% translate "Save2" %}</button>
                                 </div>
                            </div>
                          </form>
                        </div>
                     </div>
                  </div>
              </div>
          </div>
        </div>
      </div>
    <!-- </div> -->
{% endblock %}

{% block scripts %}

<script>
    // DataTable
    $(document).ready(function() {
      var table = $('#warehouseTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
                   console.log(that)
<!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
<!--                       console.log("that.search()",that.search())-->
<!--                       console.log("this.value",this.value)-->
<!--                       if ( that.search() !== this.value ) {-->
<!--                           that.search( this.value ).draw();-->
<!--                       }-->
<!--                   } );-->
               } );
           }
          });

      $("#warehouseTable_filter").hide()

      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      });

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      });

 });

    {% if request.user.is_superuser %}
        $(".loadW").click(function () {
            window.location = "#warehouse-form-section";
            $("#collapse2031").addClass('show');

        });

        $(".loadWD").dblclick(function () {
            window.location = "#warehouse-form-section";
            $("#collapse2031").addClass('show');

        });
    {% endif %}
</script>
{% endblock %}


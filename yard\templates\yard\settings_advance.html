{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}
     <input type="hidden" id="cache_smtp_creds" value="">
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate "OVERVIEW" %}</p>
            </div>
            <div class="heding">
              <p>{% translate "Advance Settings" %}</p>
            </div>
          </div>
        </div>
    <!---------------------------------------------------------------->
    <!-- {{form.errors}} -->
    <div class="card">
      <div class="row">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
          <h5 class="card-header info-color white-text py-3">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18">
                  <div class="row">
                    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 ">
                      <p class="mb-0 pt-2 d-flex text-color text_color">{% translate "Advance Settings" %}</p>
                    </div>
                  </div>
                </a>
              </h4>
            </div>
          </h5>
        </div>
      </div>

      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12"></div>
      <div id="collapse_18" class="collapse show" >
        <div class="panel-body">  
           <div class="card-body text-left">
          <form class="form-group" method="POST" enctype="multipart/form-data">
            {% csrf_token %}
              
            <div class="row">
            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
            <div class="md-form mb-3">
              <label>{% trans 'Use Camera' %} ?</label>
            </div>
            <div class="md-form mb-3">
              <label>Waage verwenden  ?</label>
            </div>
            <div class="md-form mb-3">
              <label>{% trans 'Use Barrier' %} ?</label>
            </div>
            <div class="md-form mb-3">
              <label>{% trans 'Handsender' %} ?</label>
          </div>
            <div class="md-form mb-3">
              <label>{% trans 'Traffic Light' %} ?</label>
            </div>
            <div class="md-form mb-3 mt-4">
              <label>{% trans 'Show Invoices Menu' %} ?</label>
            </div>
            <div class="md-form mb-3 mt-4">
              <label>{% trans 'Master-Container' %} ?</label>
            </div>
            <div class="md-form mb-3">
              <label>{% trans 'Delivery Note Template' %} ?</label>
            </div>
                 <div class="md-form mb-3 mt-5" style="padding-top: 75px;">
              <label>{% trans 'ID Template' %} ?</label>
            </div>

                   <div class="md-form mb-3" >
              <label>{% trans 'Bon Template' %} ?</label>
            </div>

            <input type="hidden" value="20/7204" name="yardman">
                {% comment %} <div class="md-form mb-3">
                    <label>{{ form.company_email.label }}</label>
                </div>
                <div class="md-form mb-3">
                    <label>{{ form.smtp_support.label }}</label>
                </div> {% endcomment %}
            </div>
             
            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
    
            <div class="row mt-2">
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="check" type="checkbox" name="check_yes" {% if camera.yes == 1%} checked {% endif %}> {% trans 'Yes' %}</label>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="check" type="checkbox" name="check_No" {% if camera.yes == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                </div>
                <div class="row mt-2 col-sm-2">
                  <input type="number" name="total_camera" id="total_camera" style="display:none;" min="1" max="3" placeholder="Total Cameras" value="{{camera.number}}"  class="form-control">
                </div>
            </div>
            
            <div class="row mt-2">
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="id_check_waage" type="checkbox" name="check_waage_yes" {% if waage.yes == 1%} checked {% endif %}> {% trans 'Yes' %}</label>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="id_check_waage" type="checkbox" name="check_waage_no" {% if waage.yes == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                </div>
                <div class="row mt-2 col-sm-2">
                  <input type="number" name="total_waage" id="id_total_waage" style="display:none;" min="1" max="5" placeholder="Total Waage" value="{{waage.number}}"  class="form-control">
                </div>
            </div>
            <div class="row mt-3">
                   <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="schrank" type="checkbox" name="schrank_yes" {% if barr.barrier == 1 %} checked {% endif %} > {% trans 'Yes' %}</label>
                  </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="schrank" type="checkbox" name="schrank_no" {% if barr.barrier == 0 %} checked {% endif %} > {% trans 'No' %}</label>
                  </div>
                  {% comment %} <div class="row mt-2 col-sm-2">
                <input type="number" name="total_shrank" id="total_shrank"  style="display:none;" min="1" max="4" placeholder="Total Shranke" value={{barr.count}} class="form-control">
                      </div> {% endcomment %}
            </div>

            <div class="row mt-2">
              <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="handsender" type="checkbox" name="handsender_yes"
                          {% if handsender.status == 1 %} checked {% endif %} > {% trans 'Yes' %}
                  </label>
              </div>
              <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="handsender" type="checkbox" name="handsender_no"
                          {% if handsender.status == 0 %} checked {% endif %} > {% trans 'No' %}
                  </label>
              </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="t_light" type="checkbox" name="tl_yes" {% if ampel.status == 1 %} checked {% endif %} > {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="t_light" type="checkbox" name="tl_no" {% if ampel.status == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="si" type="checkbox" name="si_yes" {% if si.status == 1 %} checked {% endif %} > {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="si" type="checkbox" name="si_no" {% if si.status == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="id_master_container" type="checkbox" name="master_container_yes" {% if master_container.status == 1 %} checked {% endif %} > {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="id_master_container" type="checkbox" name="master_container_no" {% if master_container.status == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_1" {% if dt.option == 1%} checked {% endif %} > {% trans 'First' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_2" {% if dt.option == 2 %} checked {% endif %}> {% trans 'Second' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_3" {% if dt.option == 3 %} checked {% endif %}> {% trans 'Third' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_4" {% if dt.option == 4 %} checked {% endif %}> {% trans 'Fourth' %}</label>
                  </div>
                   <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_5" {% if dt.option == 5 %} checked {% endif %}> {% trans 'Fifth' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_6" {% if dt.option == 6 %} checked {% endif %}> {% trans 'Sixth' %}</label>
                  </div>
                 <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_7" {% if dt.option == 7 %} checked {% endif %}> {% trans 'Without border' %}</label>
                  </div>
                 <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_8" {% if dt.option == 8 %} checked {% endif %}> {% trans 'Fees' %}</label>
                  </div>
              <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_9" {% if dt.option == 9 %} checked {% endif %}> {% trans 'Volumen' %}</label>
                  </div>
             <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_10" {% if dt.option == 10 %} checked {% endif %}> {% trans 'Sebald' %}</label>
                  </div>
              <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_11" {% if dt.option == 11 %} checked {% endif %}> {% trans 'RBS' %}</label>
                  </div>
             <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_12" {% if dt.option == 12 %} checked {% endif %}> {% trans 'Weigl' %}</label>
                  </div>
             <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_13" {% if dt.option == 13 %} checked {% endif %}> {% trans 'Container' %}</label>
                  </div>
              <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="del_note" type="checkbox" name="del_note_14" {% if dt.option == 14 %} checked {% endif %}> {% trans 'Sek' %}</label>
                  </div>
            </div>

             <div class="row mt-4">
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="id_del_note" type="checkbox" name="id_del_note_1" {% if id_dt.option == 1%} checked {% endif %} > {% trans 'First' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="id_del_note" type="checkbox" name="id_del_note_2" {% if id_dt.option == 2 %} checked {% endif %}> {% trans 'Second' %}</label>
                  </div>
                  
            </div>

                <div class="row mt-4">
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="id_external_del_note" type="checkbox" name="id_external_del_note_1" {% if external_id_dt.option == 1%} checked {% endif %} > {% trans 'Default' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                  <label><input id="id_external_del_note" type="checkbox" name="id_external_del_note_2" {% if external_id_dt.option == 2 %} checked {% endif %}> {% trans 'Rotzler' %}</label>
                  </div>
                  <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                    <label><input id="id_external_del_note" type="checkbox" name="id_external_del_note_3" {% if external_id_dt.option == 3 %} checked {% endif %}> {% trans 'Meba' %}</label>
                    </div>
                <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12">
                    <label><input id="id_external_del_note" type="checkbox" name="id_external_del_note_4" {% if external_id_dt.option == 4 %} checked {% endif %}> {% trans 'Bruttogewicht' %}</label>
                    </div>
            </div>

            
          </div>
            <button id="submit" type="submit" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Save2" %}</button>
            <button id="submit" type="submit" name="clear" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Clear" %}</button>
            <a class="btn btn-primary ml-4" href="/devices">{% translate "Manage Devices" %}</a>
             <a href="/stats/dump_media/" class="btn btn-primary ml-4">{{ "Media exportieren" }}</a>
             <a href="#" id="import_media" class="btn btn-primary ml-4">{% translate "Import Media" %}</a>
            </div>
            <hr style="height:4px;">
          </form>
           {% comment %} <div class="custom-file md-form mb-3 ml-3">
            <form method='post' action="{% url 'logo' %}" enctype="multipart/form-data"> {% csrf_token%}
            <div class="row">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-1">
                      <input  type="file" accept='image/*' class="custom-file-input col-sm-12 col-form-label " id="customFile" name="logo">
                      <label class="custom-file-label col-sm-12 col-form-label"  id="labelCustomFile" for="customFile">Bild für Logo auswählen</label>
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-3">
                      <button type="submit" name="save_logo" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Upload" %} </button>
                </div>
              </div>
            </div>
            <div class="row" >
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-4">
                      <input type='text' class="col-sm-12 col-form-label" id='heading' name='heading' placeholder="current" value="{% for h in heading %} {{h.heading}} {% endfor %}">
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
              <div class="row mt-4">
                  <button type="submit" id="save_heading" name="save_heading" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Save" %} </button>
                  </div>
              </div>
            </div>
            </form>
          </div> {% endcomment %}
          {% if messages %}
						{% for message in messages %}
            <div class="row mt-4">
						<div id="msg" class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}" role="alert">{{ message }}</div></div>
						{% endfor %}
                  {% endif %}
                  
           </div></div></div></div>
    <br>
    <br>
    <div id="smtp_div" class="card">
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
              <h5 class="card-header info-color white-text py-3">
                <div class="panel-heading">
                  <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#accordion" href="#collapse_19">

                      <div class="row">
                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                          <p class="mb-0 pt-2 text-color text_color">{% translate "SMTP Form" %}</p>
                        </div>
                        </div>

                    </a>
                  </h4>
                </div>
              </h5>
            </div>
          </div>
    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12"></div>
      <div id="collapse_19" class="collapse show" >
        <div class="panel-body">
           <div class="card-body text-left">
               <form class="form-group" id="smtp_form" method="POST">
               {% csrf_token %}
               <input type="hidden" name="id" id="id">
               <div class="row">
                   <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                       <div class="md-form mb-3">
                            <h4>SMTP Credential</h4>
                       </div>
                       <div class="md-form mb-3">
                            <label>{{smtp_form.host.label}}</label>
                       </div>
                       <div class="md-form mb-3">
                            <label>{{smtp_form.port.label}}</label>
                       </div>
                       <div class="md-form mb-3">
                            <label>{{smtp_form.username.label}}</label>
                       </div>
                       <div class="md-form mb-4">
                            <label>{{smtp_form.sender_address.label}}</label>
                       </div>
                       <div class="md-form mb-4">
                            <label>{{smtp_form.password.label}}</label>
                       </div>

                   </div>
               <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                    <div class="row mt-3">
                    </div>
                <div class="row mt-4">
                    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
{#                        <input required type='text' value="{{ smtp_form.host }}" class="col-sm-12 col-form-label" id='smtp_host' name='host' placeholder="Host">#}
                        {{ smtp_form.host }}
                    </div>
                </div>
                   <div class="row mt-4">
                       <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
{#                        <input required type='number' class="col-sm-12 col-form-label" id='smtp_port' name='port' placeholder="Port">#}
                           {{ smtp_form.port }}
                    </div>
                   </div>
                <div class="row mt-4">
                   <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
{#                        <input required type='text' class="col-sm-12 col-form-label" id='smtp_user' name='username' placeholder="Username">#}
                       {{ smtp_form.username }}
                    </div>
                </div>
                   <div class="row mt-4">
                       <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
                           {{ smtp_form.sender_address }}
                    </div>
                   </div>
                   <div class="row mt-4">
                       <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
                           {{ smtp_form.password }}
                    </div>
                   </div>
            </div>
                   <br>
                   <button id="test_smtp" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Test SMTP" %}</button>
                   <button id="save_smtp" type="submit" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Save SMTP" %}</button>
               </div>
           </form>
           </div>
        </div>
      </div>
    </div>
{% endblock %} 

{% block scripts %}

<script type="text/javascript">

    $(document).ready(function () {

             let importMediaDataRequest = async (formData) =>{
        let response = await $.ajax("/stats/import_media/",{
            data: formData,
            type: 'POST',
            contentType: false,
            processData: false
        });
        return response
    }

       $("#import_media").click(function (e){
        e.preventDefault();
        var fileSelector = $('<input type="file" accept=".zip,.rar,.7zip">');
        fileSelector.on("change", async (file_event) =>{
            let formData = new FormData();
            formData.append("media_dump",file_event.currentTarget.files[0])
            let response = await importMediaDataRequest(formData);
            console.log(response);
            alert("Successfully Added")
            window.location = '/sign_out'
        });
        fileSelector.click();

    })



      if ($("input[name=check_yes]").prop('checked') == true){
        $("#total_camera").css('display','block');
        $("#total_camera").prop('required',true);
      }

      if ($("input[name=check_waage_yes]").prop('checked') == true){
        $("#id_total_waage").css('display','block');
        $("#id_total_waage").prop('required',true);
      }

      $('select').addClass('form-control mt-3');

      $('input:radio').removeClass('form-control').removeClass('form-radio').addClass('form-check-input');

      var radios = $('input:radio');

      var input_name = []

      for (i = 0; i < radios.length; i++) {

          radios[i].type = "checkbox";

          if(jQuery.inArray(radios[i].name, input_name)==-1){

              input_name.push(radios[i].name)

             // $("input:checkbox[name='" + radios[i].name + "']").attr({"oninvalid":"this.setCustomValidity('Please select one of these options')"});

              var x = $("[name='"+radios[i].name+"']")

              if(x.is(":checked")== true){

                x.removeAttr('required');
              }
            }
      }
      if($("#check_smtp").prop("checked")){
          $("#smtp_div").css("display","block");
      }
      else {
          $("#smtp_div").css("display","none");
      }
    });
    
    $("input:checkbox, input:radio").on('click', function() {

      var $box = $(this);

      if ($box.is(":checked")) {

        var group = "input:checkbox[name='" + $box.attr("name") + "']";

        $(group).prop("checked", false);

        $(group).removeAttr('required');

        $box.prop("checked", true);

      } else {

        $box.prop("checked", false);

        $("input:checkbox[name='" + $box.attr("name") + "']").attr("required",false)

      }
    });

    $('input#check').on('change', function() {
    $('input#check').not(this).prop('checked', false);
    });
    
    $('input#id_check_waage').on('change', function() {
    $('input#id_check_waage').not(this).prop('checked', false);
    });

    $('input#schrank').on('change', function() {
    $('input#schrank').not(this).prop('checked', false);
    });

    $('input#t_light').on('change', function() {
    $('input#t_light').not(this).prop('checked', false);
    });

    $('input#si').on('change', function() {
      $('input#si').not(this).prop('checked', false);
      });
    
      $('input#id_master_container').on('change', function() {
      $('input#id_master_container').not(this).prop('checked', false);
      });

    $('input#p_grp').on('change', function() {
    $('input#p_grp').not(this).prop('checked', false);
    });

    $('input#contr').on('change', function() {
    $('input#contr').not(this).prop('checked', false);
    });

    $('input#del_note').on('change', function() {
    $('input#del_note').not(this).prop('checked', false);
    });

    $('input#id_del_note').on('change', function() {
    $('input#id_del_note').not(this).prop('checked', false);
    });

     $('input#id_external_del_note').on('change', function() {
    $('input#id_external_del_note').not(this).prop('checked', false);
    });


    save_cache_smtp_cred = () =>{
       let cached_input  =  $("#cache_smtp_creds");
       let smtp_form_input = $("#smtp_creds");
       if(smtp_form_input.attr("value") != ""){
           cached_input.attr("value",smtp_form_input.attr("value"))
       }
    }

    get_cache_smtp_cred = () =>{
        let cached_input  =  $("#cache_smtp_creds");
        let smtp_form_input = $("#smtp_creds");
        if(cached_input.attr("value") != ""){
           smtp_form_input.attr("value",cached_input.attr("value"))
       }
    }

    $('input#check_smtp').on('change', function() {
        $('input#check_smtp').not(this).prop('checked', false);
        if($('input[name="check_smtp_yes"]').is(":checked")){
            $("#smtp_div").css("display","block");
            $("#smtp_supp_hidden").prop("checked","true");
            get_cache_smtp_cred()
        }
        else {
            $("#smtp_div").css("display","none");
            $("#smtp_supp_hidden").prop("checked",false);
            save_cache_smtp_cred();
            $("#smtp_creds").attr("value","")
        }
    });

    const postTestSMTP = async (form_data) =>{
        let url = "/test_smtp/";
        form_data = {'csrfmiddlewaretoken': '{{ csrf_token }}',...form_data}
        delete form_data["name"];
        delete form_data["value"]
        let response = await $.ajax({
                type: "POST",
                url:url,
                data: form_data
            }
        );
        return response
    }

    const postSaveSMTP = async (form_data) =>{
        let url = "/save_smtp/";
        form_data = {'csrfmiddlewaretoken': '{{ csrf_token }}',...form_data}
        delete form_data["name"];
        delete form_data["value"]
        let response = await $.ajax({
                type: "POST",
                url:url,
                data: form_data
            }
        );
        return response
    }

    $("#test_smtp").on('click', (e) =>{
        $("#smtp_form").validate().destroy();
        $("#smtp_form").validate({
            rules:{
                host: "required",
                port: "required",
                user: "required",
                pass: "required",
                sender_address:"required"
            },
            messages:{
                host:"Host Required"
            },
            submitHandler: function (form,event) {
                {#var data =Object.fromEntries(new FormData(form))#}
                event.preventDefault();
                let data = $(form).serializeArray().reduce((o,p) => ({...o, [p.name]: p.value}))
                postTestSMTP(data).then(response =>{
                    alert("Email Successfully sent to User")
                }).catch(err=>{
                    alert(err.responseText)
                });
                return false;
            }
        });
    });

    $("#save_smtp").on('click', (e) =>{
        $("#smtp_form").validate().destroy();
        $("#smtp_form").validate({
            rules:{
                host: "required",
                port: "required",
                user: "required",
                pass: "required",
                sender_address:"required"
            },
            messages:{
                host:"Host Required"
            },
            submitHandler: async function (form,event) {
                {#var data =Object.fromEntries(new FormData(form))#}
                event.preventDefault();
                let data = $(form).serializeArray().reduce((o,p) => ({...o, [p.name]: p.value}))
                await postSaveSMTP(data).then(response => {
                    alert("SMTP Credentials Successfully created.");
                    $("#smtp_creds").attr("value",response.id);
                });
                return false;
            }
        });
    });

    $("#save_heading").click(function(){
      head = $("#heading").val();
      if (head === ''){
        if (!confirm("Do you want to clear Heading!!")){
          return false;
        }
      }
    })

    $('#customFile').change(function () {
    $("#labelCustomFile").text(this.value);
  });

  $('input').change(function(){
    if ($("[name=check_yes]").prop('checked') == true){
      $("#total_camera").css('display','block');
      $("#total_camera").prop('required',true);
    } else {
      $("#total_camera").css('display','none');
      $("#total_camera").prop('required',false);
    }
  
    
    

    // if ($("input[name=schrank_yes").prop('checked') == true){
    //   $("#total_shrank").css('display','block');
    //   $("#total_shrank").prop('required','true');
    // } else {
    //   $("#total_shrank").css('display','none');
    //   $("#total_shrank").prop('required','false');
    // }


  })

  $('input').change(function(){

    if ($("[name=check_waage_yes]").prop('checked') == true){
        $("#id_total_waage").css('display','block');
        $("#id_total_waage").prop('required',true);
      } else {
        $("#id_total_waage").css('display','none');
        $("#id_total_waage").prop('required',false);
      }
  })

  
  $('input#handsender').on('change', function () {
            $('input#handsender').not(this).prop('checked', false);
        });
</script>

{% endblock %}


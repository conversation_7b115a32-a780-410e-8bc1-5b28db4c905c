# Generated by Django 3.1.1 on 2022-06-13 11:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0131_auto_20220610_1519'),
    ]

    operations = [
        migrations.AddField(
            model_name='container',
            name='check_digit',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='curb_weight',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='payload',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='container',
            name='total_weight',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='container',
            name='container_type',
            field=models.Char<PERSON>ield(blank=True, choices=[('20′', '20′'), ('40′', '40′'), ('45′HC', '45′HC'), ('45′PW', '45′PW'), ('48′HC', '48′HC'), ('53′HC', '53′HC')], max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='container',
            name='last_site',
            field=models.DecimalField(decimal_places=0, default=0, max_digits=10, null=True),
        ),
    ]

var acc = document.getElementsByClassName("accordion");
var i;

for (i = 0; i < acc.length; i++) {
  acc[i].addEventListener("click", function () {
    this.classList.toggle("active");
    var panel = this.nextElementSibling;
    if (panel.style.maxHeight) {
      panel.style.maxHeight = null;
    } else {
      panel.style.maxHeight = panel.scrollHeight + "px";
    }
  });
}

/* This function is the alternate javascript function of loadDetails. and not using now */
function editFunction(lc_plate_1, lc_plate_2, lc_plate_3) {
  document.getElementById("id_license_plate1").value = lc_plate_1;
  document.getElementById("id_license_plate2").value = lc_plate_2;
  document.getElementById("id_license_plate3").value = lc_plate_3;
}

function loadUsersDetails(id) {
   $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/users_details/" + id,
    success: function (result) {
      // document.getElementById("id_password1").disabled = true;
      document.getElementById("id").value = id;
      document.getElementById("id_name").value = result.name;
      document.getElementById("id_email").value = result.email;
      document.getElementById("id_password1").value = result.password;
      document.getElementById("id_yard").value = result.yard;
      document.getElementById("id_role").value = result.role;
      if (result.address != null) {
        document.getElementById("id_address").value = result.address;
      }
      if (result.telephone != null) {
        document.getElementById("id_telephone").value = result.telephone;
      }

      if ($("#user_id").val() == id) {
        $("#id_role").prop("readonly", true);
        $("#id_role").prop("tabindex", "-1");
        // $("#label_role").prop('hidden',true)
        $("#id_role").css("pointer-events", "none");
        $("#id_role").css("background-color", "#dadada");
      } else {
        $("#id_role").prop("readonly", false);
        $("#id_role").prop("tabindex", "0");
        // $("#label_role").prop('hidden',false)
        $("#id_role").css("pointer-events", "all");
        $("#id_role").css("background-color", "#fff");
      }
    },
  });
}

function loadUserEditDetails(id) {
   $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/users_details/" + id,
    success: function (result) {
      // document.getElementById("id_password1").disabled = true;
      document.getElementById("id").value = id;
      document.getElementById("id_name").value = result.name;
      document.getElementById("id_email").value = result.email;
      //document.getElementById("id_yard").value = result.yard;
      // document.getElementById("id_role").value = result.role;
      document.getElementById("id_address").value = result.address;
      document.getElementById("id_telephone").value = result.telephone;
    },
  });
}

function loadCustomerDetails(id) {
  window.location="#customer-form-section";
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/customer_details/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_name1").value = result.name1;
      document.getElementById("id_name2").value = result.name2;
      // document.getElementById("id_description").value = result.description;
      document.getElementById("id_street").value = result.street;
      document.getElementById("id_pin").value = result.pin;
     // document.getElementById("id_fax").value = result.fax;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_country").value = result.country;
      // document.getElementById("id_website").value = result.website;
      document.getElementById("id_contact_person1_email").value =
        result.contact_person1_email;
      document.getElementById("id_contact_person2_email").value =
        result.contact_person2_email;
      // document.getElementById("id_contact_person3_email").value =
      //   result.contact_person3_email;
      // document.getElementById("id_contact_person1_phone").value =
      //   result.contact_person1_phone;
      // document.getElementById("id_contact_person2_phone").value =
      //   result.contact_person2_phone;
      // document.getElementById("id_contact_person3_phone").value =
      //   result.contact_person3_phone;
      // document.getElementById("id_customer_type").value = result.customer_type;
      // document.getElementById("id_classification").value =
      //   result.classification;
      // document.getElementById("id_sector").value = result.sector;
      // document.getElementById("id_company_size").value = result.company_size;
      // document.getElementById("id_area").value = result.area;
      // document.getElementById("id_warehouse").value = result.warehouse;
      document.getElementById("id_phone_number").value =
        result.phone_number;
      document.getElementById("id_salutation").value = result.salutation;
      document.getElementById("id_addition1").value = result.addition1;
      document.getElementById("id_addition2").value = result.addition2;
      // document.getElementById("id_addition3").value = result.addition3;
      // document.getElementById("id_diff_invoice_recipient").value =
      //   result.diff_invoice_recipient;
      // document.getElementById("id_price_group").value = result.price_group;
      // if (result.private_person == 1) {
      //   document.getElementById("id_private_person").checked = true;
      // } else {
      //   document.getElementById("id_private_person").checked = false;
      // }
      // if (result.document_lock == 1) {
      //   document.getElementById("id_document_lock").checked = true;
      // } else {
      //   document.getElementById("id_document_lock").checked = false;
      // }
      // if (result.payment_bock == 1) {
      //   document.getElementById("id_payment_bock").checked = true;
      // } else {
      //   document.getElementById("id_payment_bock").checked = false;
      // }
      // document.getElementById("id_delivery_terms").value =
      //   result.delivery_terms;
      // document.getElementById("id_special_discount").value =
      //   result.special_discount;
      document.getElementById("id_debitor_number").value =
        result.debitor_number;
       document.getElementById("id_price_group").value = result.price_group;
      // document.getElementById("id_dunning").value = result.dunning;
      // document.getElementById("id_company").value = result.company;
      // document.getElementById("id_perm_street").value = result.perm_street;
      // document.getElementById("id_perm_pin").value = result.perm_pin;
      // document.getElementById("id_perm_place").value = result.perm_place;
      // document.getElementById("id_perm_country").value = result.perm_country;

      // $("#id_price_group").val(result.price_group).trigger('change');
    },
  });
}

function loadSupplierDetails(id) {
  window.location="#supplier-form-section"
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/supplier_detail/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_supplier_name").value = result.supplier_name;
      // document.getElementById("id_salutation").value = result.salutation;
      // document.getElementById("id_first_name").value = result.first_name;
      document.getElementById("id_name").value = result.name;
      document.getElementById("id_street").value = result.street;
      document.getElementById("id_pin").value = result.pin;
      // document.getElementById("id_fax").value = result.fax;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_country").value = result.country;
      // document.getElementById("id_post_office_box").value = result.post_office_box;
      document.getElementById("id_contact_person1_email").value =
        result.contact_person1_email;
      document.getElementById("id_project_number").value =
        result.project_number;
      // document.getElementById("id_contact_person2_email").value = result.contact_person2_email;
      // document.getElementById("id_contact_person3_email").value = result.contact_person3_email;
      document.getElementById("id_contact_person1_phone").value =
        result.contact_person1_phone;
      // document.getElementById("id_contact_person2_phone").value = result.contact_person2_phone;
      // document.getElementById("id_contact_person3_phone").value = result.contact_person3_phone;
      // document.getElementById("id_website").value = result.website;
      // document.getElementById("id_cost_centre").value = result.cost_centre;
      // document.getElementById("id_warehouse").value = result.warehouse;
      // document.getElementById("id_creditor_number").value = result.creditor_number;
      document.getElementById("id_addition1").value = result.addition1;
      document.getElementById("id_addition2").value = result.addition2;
      document.getElementById("id_addition3").value = result.addition3;
    },
  });
}


function loadPlaceOfDeliveryDetails(id) {
  window.location="#customer-form-section"
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/place_of_delivery_detail/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_name1").value = result.name1;
      document.getElementById("id_name2").value = result.name2;
      // document.getElementById("id_description").value = result.description;
      document.getElementById("id_street").value = result.street;
      document.getElementById("id_pin").value = result.pin;
      document.getElementById("id_fax").value = result.fax;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_country").value = result.country;
      // document.getElementById("id_website").value = result.website;
      document.getElementById("id_contact_person1_email").value =
        result.contact_person1_email;
      document.getElementById("id_contact_person2_email").value =
        result.contact_person2_email;
      // document.getElementById("id_contact_person3_email").value =
      //   result.contact_person3_email;
      // document.getElementById("id_contact_person1_phone").value =
      //   result.contact_person1_phone;
      // document.getElementById("id_contact_person2_phone").value =
      //   result.contact_person2_phone;
      // document.getElementById("id_contact_person3_phone").value =
      //   result.contact_person3_phone;
      // document.getElementById("id_customer_type").value = result.customer_type;
      // document.getElementById("id_classification").value =
      //   result.classification;
      // document.getElementById("id_sector").value = result.sector;
      // document.getElementById("id_company_size").value = result.company_size;
      // document.getElementById("id_area").value = result.area;
      // document.getElementById("id_warehouse").value = result.warehouse;
      document.getElementById("id_phone_number").value =
        result.phone_number;
      document.getElementById("id_salutation").value = result.salutation;
      document.getElementById("id_addition1").value = result.addition1;
      document.getElementById("id_addition2").value = result.addition2;
      // document.getElementById("id_addition3").value = result.addition3;
      document.getElementById("id_diff_invoice_recipient").value =
        result.diff_invoice_recipient;
      // document.getElementById("id_price_group").value = result.price_group;
      // if (result.private_person == 1) {
      //   document.getElementById("id_private_person").checked = true;
      // } else {
      //   document.getElementById("id_private_person").checked = false;
      // }
      // if (result.document_lock == 1) {
      //   document.getElementById("id_document_lock").checked = true;
      // } else {
      //   document.getElementById("id_document_lock").checked = false;
      // }
      // if (result.payment_bock == 1) {
      //   document.getElementById("id_payment_bock").checked = true;
      // } else {
      //   document.getElementById("id_payment_bock").checked = false;
      // }
      document.getElementById("id_delivery_terms").value =
        result.delivery_terms;
      document.getElementById("id_special_discount").value =
        result.special_discount;
      document.getElementById("id_kreditoren_number").value =
        result.kreditoren_number;
        document.getElementById("id_term_of_payment").value =
        result.term_of_payment;
      // document.getElementById("id_dunning").value = result.dunning;
      // document.getElementById("id_company").value = result.company;
      // document.getElementById("id_perm_street").value = result.perm_street;
      // document.getElementById("id_perm_pin").value = result.perm_pin;
      // document.getElementById("id_perm_place").value = result.perm_place;
      // document.getElementById("id_perm_country").value = result.perm_country;

      // $("#id_price_group").val(result.price_group).trigger('change');
    },
  });
}

function loadForwardersDetails(id) {
  window.location="#forwarder-form-section";
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/forwarders_detail/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_name").value = result.name;
      document.getElementById("id_firstname").value = result.firstname;
      document.getElementById("id_second_name").value = result.second_name;
      document.getElementById("id_street").value = result.street;
      document.getElementById("id_pin").value = result.pin;
      document.getElementById("id_telephone").value = result.telephone;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_country").value = result.country;
    },
  });
}

function loadYardListDetails(id) {
   $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/yard_list_detail/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_name").value = result.name;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_country").value = result.country;
    },
  });
}

function loadArticleDetails(id) {
  window.location="#article-form-section";
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/article_detail/" + id,
    success: function (result) {
      console.log(result)
      document.getElementById("id").value = id;
      document.getElementById("id_name").value = result.data.name;
      document.getElementById("id_short_name").value = result.data.short_name;
      document.getElementById("id_article_number").value = result.data.article_number;
      document.getElementById("id_description").value = result.data.description;
      //      document.getElementById("id_entry_weight").value = result.entry_weight;
      // document.getElementById("id_balance_weight").value = result.balance_weight;
      // document.getElementById("id_outgoing_weight").value = result.outgoing_weight;
      // document.getElementById("id_group").value = result.data.group;
      var vat = result.data.vat ? Math.ceil(result.data.vat)+"%" : null;
      var options = document.getElementById("id_vat").options;
      for (var i = 0; i < options.length; i++) {
        if (options[i].text == vat) {
          options[i].selected = true;
          break;
        }
      }

      // document.getElementById("id_minimum_amount").value =
      //   result.data.minimum_amount;
      document.getElementById("id_price1").value = result.data.price1;
      document.getElementById("id_price2").value = result.data.price2;
      document.getElementById("id_price3").value = result.data.price3;
      document.getElementById("id_price4").value = result.data.price4;
      document.getElementById("id_price5").value = result.data.price5;

      document.getElementById("id_second_price1").value = result.data.second_price1;
      document.getElementById("id_second_price2").value = result.data.second_price2;
      document.getElementById("id_second_price3").value = result.data.second_price3;
      document.getElementById("id_second_price4").value = result.data.second_price4;
      document.getElementById("id_second_price5").value = result.data.second_price5;
      // document.getElementById("id_discount").value = result.data.discount;
      document.getElementById("id_avv_num").value = result.data.avv_num;
    //  document.getElementById("id_account").value = result.data.account;
      document.getElementById("id_density").value = result.data.density;
      // document.getElementById("id_cost_center").value = result.data.cost_center;
      document.getElementById("id_unit").value = result.data.unit;
      // document.getElementById("id_min_quantity").value =
      //   result.data.min_quantity;
      // document.getElementById("id_revenue_group").value =
      //   result.data.revenue_group;
      // document.getElementById("id_revenue_account").value =
      //   result.data.revenue_account;
      // document.getElementById("id_list_price_net").value =
      //   result.data.list_price_net;
      // document.getElementById("id_list_price_gross").value =
      //   result.data.list_price_gross;
   //   document.getElementById("id_ean").value = result.data.ean;
      if (result.data.ware_house) {
        document.getElementById("id_ware_house").value = result.data.ware_house;
      }
      document.getElementById("id_supplier").value = result.data.supplier;
    },
  });
}

function loadBuildingSiteDetails(id) {
   $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/building_site_detail/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_name").value = result.name;
      document.getElementById("id_short_name").value = result.short_name;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_street").value = result.street;
      document.getElementById("id_pin").value = result.pin;
      document.getElementById("id_infotext").value = result.infotext;
    },
  });
}

function loadVehicleDetails(id) {
  
  window.location="#vehicle-form-section";
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/vehicle_detail/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
      document.getElementById("id_license_plate").value = result.license_plate;
      document.getElementById("id_license_plate2").value =
        result.license_plate2;
      document.getElementById("id_forwarder").value = result.forwarder;
      document.getElementById("id_country").value = result.country;
      document.getElementById("id_street").value = result.street;
      document.getElementById("id_pin").value = result.pin;
      document.getElementById("id_place").value = result.place;
      document.getElementById("id_vehicle_weight_date").value = result.vehicle_weight_date;
      document.getElementById("id_vehicle_weight_date2").value = result.vehicle_weight_date2;
      document.getElementById("id_owner").value = result.owner;
      document.getElementById("id_self_tara").checked = result.self_tara;
      document.getElementById("id_vehicle_weight").value = result.vehicle_weight;
      document.getElementById("id_vehicle_weight2").value = result.vehicle_weight2;
      document.getElementById("id_vehicle_weight_time").value = result.vehicle_weight_time;
      document.getElementById("id_vehicle_weight_time2").value = result.vehicle_weight_time;
      
      if(result.vehicle_weight_time!=null){
        var vehice_time_split = result.vehicle_weight_time.split(':');
        document.getElementById("id_vehicle_weight_time").value = vehice_time_split[0]+":"+vehice_time_split[1]+":00"; 
        }
        if(result.vehicle_weight_time2 !=null){
          var vehice_time_split2 = result.vehicle_weight_time2.split(':');
          document.getElementById("id_vehicle_weight_time2").value = vehice_time_split2[0]+":"+vehice_time_split2[1]+":00"; 
        }

      document.getElementById("id_vehicle_weight").value =
        result.vehicle_weight;
      // document.getElementById("id_vehicle_weight_id").value =
      //   result.vehicle_weight_id;
      // document.getElementById("id_vehicle_weight2").value =
      //   result.vehicle_weight2;
      // document.getElementById("id_vehicle_weight_id2").value =
      //   result.vehicle_weight_id2;
    //  document.getElementById("id_vehicle_type").value = result.vehicle_type;
    //   document.getElementById("id_owner").value = result.owner;
      document.getElementById("id_driver_name").value = result.driver_name;
      // document.getElementById("id_fahrzeugtypen").value = result.fahrzeugtypen;
      // document.getElementById("id_trailor_weight").value =
      //   result.trailor_weight;
      if (result.self_tara == true) {
        $("#id_self_tara").prop("checked", true);
      } else {
        $("#id_self_tara").prop("checked", false);
      }
    },
  });
}

// function autocompleteSearch(id_field, search_url, func_id) {
//   var search = $('#' + id_field).val()
//   var data = {
//     'search': search
//   };
//   $('#' + id_field).autocomplete({
//     source: function (request, response) {
//       $.ajax({
//         url: search_url,
//         type: "GET",
//         data: data,
//         success: function (data) {
//           response($.map(data.list, function (el) {
//             return {
//               label: el.label,
//               value: el.value
//             };
//           }));
//         }
//       });
//     },
//     select: function (event, ui) {
//       // Prevent value from being put in the input:
//       this.value = ui.item.label;
//       var id = ui.item.value;
//       if (func_id == "kunden") {
//         populateCustomerDetails(id);
//       } else if (func_id == "fahrzeuge") {
//         populateFahrzeugeDetails(id);
//       } else if (func_id == "lieferanten") {
//         populateSupplierDetails(id);
//       } else if (func_id == "artikel") {
//         populateArtikelDetails(id);
//       }
//       // Set the next input's value to the "value" of the item.
//       // $(this).next("input").val(ui.item.value);
//       event.preventDefault();
//     }
//   });
// }

function populateCustomerDetails(id) {
  $.ajax({
    type: "GET",
    url: "/customer_details/" + id,
    beforeSend: function (xhr) {
      $(".kunden").addClass("loading");
      console.log("beforeSend");
    },
    success: function (result) {
      $("#customer_id").val(id);
      $("#customer_name2").val(result.name2);
      // if (result.salutation == null) {
      //   $("#customer_salutation").text("Name");
      // } else {
      //   $("#customer_salutation").text(result.salutation);
      // }
      $("#customer_street").val(result.street);
      $("#customer_pin").val(result.pin);
      $("#customer_place").val(result.place);
      $("#customer_price_group").val(result.price_group);
      if (result.price_group == undefined) {
        $("#customer_price_group").val("price1");
      } else {
        $("#customer_price_group").val(result.price_group);
      }
      $("#customer_price_group").trigger("change");
    },
    complete: function () {
      $(".kunden").removeClass("loading");
    },
  });
}

function populatePlaceOfDeliveryDetails(id) {
  $.ajax({
    type: "GET",
    url: "/place_of_delivery_detail/" + id,
    beforeSend: function (xhr) {
      $('.kunden').addClass('loading')
      console.log('beforeSend');
    },
    success: function (result) {
      console.log("hello")
      $("#place_of_delivery_id").val(id);
      $("#id_place_of_delivery_name2").val(result.name2);
      $("#id_place_of_delivery_street").val(result.street);
      $("#id_place_of_delivery_pin").val(result.pin);
      $("#id_place_of_delivery_place").val(result.place);
      $("#place_of_delivery_price_group").val(result.price_group);
      if (result.price_group==undefined){
        $("#place_of_delivery_price_group").val('price1');
      }else{
      $("#place_of_delivery_price_group").val(result.price_group);
      }
      $("#place_of_delivery_price_group").trigger('change');

    },
    complete: function () {
      $('.kunden').removeClass('loading')
    },
  })
}

function populateVehicleDetails(id) {
  $.ajax({
    type: "GET",
    url: "/vehicle_detail/" + id,
    beforeSend: function (xhr) {
      $(".fahrzeuge").addClass("loading");
      console.log("beforeSend");
    },
    success: function (result) {
      $("#vehicle_id").val(id);
      $("#vehicle_forwarder").val(result.forwarder);
      $("#license_plate2").val(result.license_plate2);
      $("#vehicle_weight").val(result.vehicle_weight);

      date = result.vehicle_weight_date;
      time = result.vehicle_weight_time;
      if ($("#datetime_firstw").val() === "2000-01-01T00:00:00") {
        if (date != null) {
          if (time != null) {
            date_time = date + "T" + time;
            $("#datetime_firstw").val(date_time);
          } else {
            date_time = date + "T" + "00:00:00";
            $("#datetime_firstw").val(date_time);
          }
        }
      }
    },
    complete: function () {
      $(".fahrzeuge").removeClass("loading");
    },
  });
}

function populateSupplierDetails(id) {
  if ($("#id_project_number_transaction_select").val() == id) {
    return;
  }
  $.ajax({
    type: "GET",
    url: "/supplier_detail/" + id,
    beforeSend: function (xhr) {
      $(".lieferanten").addClass("loading");
      console.log("beforeSend");
    },
    success: function (result) {
      $("#supplier_id").val(id);
      //      $("#supplier_short_name").val(result.short_name);
      $("#supplier_street").val(result.street);
      $("#supplier_pin").val(result.pin);
      $("#id_project_number_transaction").val(result.project_number);
      $("#id_project_number_transaction_select").val(id).trigger("change.select2");
      $("#supplier_place").val(result.place);
    },
    complete: function () {
      $(".lieferanten").removeClass("loading");
    },
  });
}
function populateSupplierDetailsFromNumber(id) {
  if ($("#id_supplier").val() == id) {
    return;
  }
  $.ajax({
    type: "GET",
    url: "/supplier_detail/" + id,
    beforeSend: function (xhr) {
      $(".lieferanten").addClass("loading");
      console.log("beforeSend");
    },
    success: function (result) {
      $("#supplier_id").val(id);
      //      $("#supplier_short_name").val(result.short_name);
      $("#supplier_street").val(result.street);
      $("#supplier_pin").val(result.pin);
      $("#id_supplier").val(id).trigger("change");
      $("#supplier_place").val(result.place);
      $("#id_project_number_transaction").val(result.project_number);
    },
    complete: function () {
      $(".lieferanten").removeClass("loading");
    },
  });
}

function populateArticleDetails(id) {
  $.ajax({
    type: "GET",
    url: "/article_detail/" + id,
    beforeSend: function (xhr) {
      $(".artikel").addClass("loading");
      console.log("beforeSend");
    },
    success: function (result) {
      var price_group = document.getElementById("customer_price_group").value;
      $("#article_id").val(id);
      $("#article_ware_house").val(result.ware_house.name);
      $("#id_avv_number").val(result.data.avv_num);
      $("#article_short_name").val(result.data.short_name);
      if (result.data.group == undefined) {
        $("#article_group").val(1);
      } else {
        $("#article_group").val(result.data.group);
      }
      if (result.data.id != null) {
        $(".showR").css("display", "block");
      } else {
        $(".showR").css("display", "none");
      }
      // if (price_group == "price5"){
      //   $("#article_price").val(result.data.price5);
      // }
      // else if(price_group == "price2"){
      //    $("#article_price").val(result.data.price2);
      // }
      // else if(price_group == "price3"){
      //    $("#article_price").val(result.data.price3);
      // }
      // else if(price_group == "price4"){
      //    $("#article_price").val(result.data.price4);
      // }
      // else{
      $("#article_price").val(result.data.price1);
      $("#article_vat").val(result.data.vat);
      //}
    },
    complete: function () {
      $(".artikel").removeClass("loading");
    },
  });
}

function populateCombinationDetails(id) {
  var id_ident = $("#id_ident option:selected").text();
  $.ajax({
    type: "GET",
    url: "/combination/" + id,
    beforeSend: function (xhr) {
      $("#id_selected_combi").val(id_ident);
      $(".artikel").addClass("loading");
      $(".lieferanten").addClass("loading");
      $(".fahrzeuge").addClass("loading");
      $(".kunden").addClass("loading");
      $(".container").addClass("loading");
      console.log("beforeSend");
    },
    success: function (result) {
      console.log("hello")
      $("#id_customer").val(result.customer).trigger("change");
      $("#id_vehicle").val(result.vehicle).trigger("change");
      $("#id_supplier").val(result.supplier).trigger("change");
      $("#id_article").val(result.article).trigger("change");
      $("#id_container").val(result.container).trigger("change");
      $("#id_transaction_id").val(result.transaction_id).trigger("change");
      
    },
    complete: function () {
      $(".artikel").removeClass("loading");
      $(".lieferanten").removeClass("loading");
      $(".fahrzeuge").removeClass("loading");
      $(".kunden").removeClass("loading");
      $(".container").removeClass("loading");
    },
  });
}

function populateContractDetails(id) {
  $.ajax({
    type: "GET",
    url: "/contract/" + id,
    beforeSend: function (xhr) {
      // $("#id_selected_combi").val(id_ident)
      $(".artikel").addClass("loading");
      $(".lieferanten").addClass("loading");
      $(".fahrzeuge").addClass("loading");
      $(".kunden").addClass("loading");
      // $('.container').addClass('loading')
      console.log("beforeSend");
    },
    success: function (result) {
      // $("#id_vehicle").val(result.vehicles[0].id).trigger('change');


      if(result.vehicles.length > 0) {
        let vehicle_select = $("#id_vehicle");
        vehicle_select.val(null).empty().trigger("change");
        for (let vehicle of result.vehicles) {
          let option = new Option(vehicle.license_plate, vehicle.id, null, null);
          vehicle_select.append(option).trigger("change");
        }
      }
      else{
         for (let vehicle of result.vehicles) {
          let option = new Option(vehicle.license_plate, vehicle.id, null, null);
          vehicle_select.append(option).trigger("change");
        }
      }

      let supplier_select = $("#id_supplier");
      supplier_select.val(null).empty().trigger("change");
      for (let supplier of result.supplier) {
        let option = new Option(supplier.name, supplier.id, null, null);
        supplier_select.append(option).trigger("change");
      }

      let article_select = $("#id_article");
      article_select.val(null).empty().trigger("change");
      for (let material of result.required_materials) {
        let mat_obj = material.material;
        let option = new Option(mat_obj.name, mat_obj.id, null, null);
        article_select.append(option).trigger("change");
      }

      $("#selected_contract").val(result.contract_number).trigger("change");
      $("#id_customer").val(result.customer.id).trigger("change");
      $("#id_customer").prop("disabled", true);

      // $("#id_article").val(result.required_materials[0]["material"]["id"]).trigger('change');
      // $("#id_container").val(result.container).trigger('change');
      // $("#id_transaction_id").val(result.transaction_id).trigger('change');
    },
    complete: function () {
      $(".artikel").removeClass("loading");
      $(".lieferanten").removeClass("loading");
      $(".fahrzeuge").removeClass("loading");
      $(".kunden").removeClass("loading");
      // $('.container').removeClass('loading')
    },
  });
}

function daily_close() {
  var txt;
  var r = confirm("Tagesabschluß durchführen ?");
  if (r == true) {
    $.ajax({
      type: "GET",
      url: "/stats/daily_closing/",
      beforeSend: function (xhr) {},
      success: function (result) {
        alert(result.msg);
      },
      complete: function () {},
    });
  } else {
    txt = "You pressed Cancel!";
  }
}

function loadContainerDetails(id) {
  window.location="#container-form-section";
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/container_details/" + id,
    success: function (result) {
      document.getElementById("id").value = id;
       document.getElementById("id_curb_weight").value = result.curb_weight;
       document.getElementById("id_total_weight").value = result.total_weight;
       document.getElementById("id_check_digit").value = result.check_digit;
       document.getElementById("id_payload").value = result.payload;
    //  document.getElementById("id_name").value = result.name;
      document.getElementById("id_container_type").value =
        result.container_type;
     // document.getElementById("id_group").value = result.group;
      document.getElementById("id_container_weight").value =
        result.container_weight;
    //  document.getElementById("id_volume").value = result.volume;
      document.getElementById("id_container_number").value = result.container_number;
      document.getElementById("id_payload_container_volume").value = result.payload_container_volume;
      document.getElementById("id_next_exam").value = result.next_exam;
    //  document.getElementById("id_unit").value = result.unit;
      document.getElementById("id_maximum_gross_weight").value = result.maximum_gross_weight;
      document.getElementById("id_tare_weight").value = result.tare_weight;
     // document.getElementById("id_waste_type").value = result.waste_type;
      document.getElementById("id_hazard_warnings").value = result.hazard_warnings;
      // document.getElementById("id_last_site").value = result.last_site;
    },
  });
}

function loadHandTransmitterDetails(id) {
  window.location = "#entry";
  window.scrollTo(
      0,
      document.body.scrollHeight || document.documentElement.scrollHeight
  );
  $.ajax({
      type: "GET",
      url: "/hand_transmitter_details/" + id,
      success: function (result) {
          console.log(result);
          document.getElementById("id").value = id;
          document.getElementById("id_name").value = result.name;
          document.getElementById("id_combination").value = result.combination;
          document.getElementById("id_devise_id").value = result.devise_id;
      },
  });
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////
function loadCombinationDetails(id) {
  window.location.href="#entry";
  // $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  $.ajax({
    type: "GET",
    url: "/comb_details/" + id,
    success: function (result) {
      contracts = []
      for(var i = 0; i < JSON.parse(result.contracts).length; i++){
        contracts.push(JSON.parse(result.contracts)[i].pk)
      }
      console.log("result")
      console.log(result)
      $("#id_contracts").val(contracts).trigger('change')
      document.getElementById("id").value = id;
      document.getElementById("id_ident").value = result.ident;
      document.getElementById("id_short_name").value = result.short_name;
      document.getElementById("id_supplier").value = result.supplier;
      // document.getElementById("id_yard").value = result.yard;
      document.getElementById("id_vehicle").value = result.vehicle;
      document.getElementById("id_delivery_customer").value = result.customer;
      document.getElementById("id_article").value = result.article;
      document.getElementById("id_place_of_delivery").value = result.place_of_delivery;
      if (result.forwarders != null){
        document.getElementById("id_forwarders").value = result.forwarders;
      }
      
    //  document.getElementById("id_container").value = result.container;
      if(result.status == null){
         document.getElementById("id_status").value = 0;
      }
      else {
        document.getElementById("id_status").value = result.status;
      }
  
      if (result.tara_with_mobile) {
        $("#id_tara_with_mobile").val(result.tara_with_mobile).trigger("change");
        $("#id_tara_with_mobile").click();
      }
    },
  });
}

function loadWarehouseDetails(id) {
  window.location="#warehouse-form-section";
  //  $("html, body").animate({ scrollTop: $(document).height()-$(window).height() });
  // window.scrollTo(
  //   0,
  //   document.body.scrollHeight || document.documentElement.scrollHeight
  // );
  $.ajax({
    type: "GET",
    url: "/warehouse_detail/" + id,
    success: function (result) {
     document.getElementById("id").value = id;
    //  document.getElementById("id_name").value = result.name;
      document.getElementById("id_stock_designation").value = result.stock_designation;
      document.getElementById("id_stock_number").value = result.stock_number;
       document.getElementById("id_warehouse_street").value = result.warehouse_street;
        document.getElementById("id_storage_location").value = result.storage_location;
        document.getElementById("id_parking_space").value = result.parking_space;
    },
  });
}

// $("#id_customer,#customer_price_group").change(function(){
//  var id = document.getElementById("id_article").value
//  if(id!=0){
//     var price_group = document.getElementById("customer_price_group").value
//     $.ajax({
//       type: "GET",
//       url: "/article_detail/" + id,
//       beforeSend: function (xhr) {
//       },
//       success: function (result) {
//           if (price_group == "price5"){
//             $("#article_price").val(result.price5);
//           }
//           else if(price_group == "price2"){
//              $("#article_price").val(result.price2);
//           }
//           else if(price_group == "price3"){
//              $("#article_price").val(result.price3);
//           }
//           else if(price_group == "price4"){
//              $("#article_price").val(result.price4);
//           }
//           else{
//              $("#article_price").val(result.price1);
//           }
//             },
//     })
//  }
// });

function populateContainerdetails(id) {
  $.ajax({
    type: "GET",
    url: "/container_details/" + id,
    beforeSend: function (xhr) {
      $(".container").addClass("loading");
    },
    success: function (result) {
      $("#container_id").val(id);
      $("#contr_type").val(result.container_type);
     // $("#contr_group").val(result.group);
      $("#contr_weight").val(result.container_weight);
      if (result.id == null) {
        $("#contr_on").css("display", "none");
        $("#contr_on").prop("checked", false);
      } else {
        $("#contr_on").css("display", "initial");
      }
    },
    complete: function (xhr) {
      $(".container").removeClass("loading");
    },
  });
}


$(".confirmdelete").click(function (e) {
  if (!confirm("Möchten Sie wirklich löschen?")) {
    return false;
  }
});

$("#e_sign").click(function () {
  var title = "Elektronische Unterschrift";
  // var body = "Material list";
  $("#MyPopup .modal-title").html(title);

  $("#btnClosePopup").click(function () {
    $("#MyPopup").modal("hide");
  });
  $("#MyPopup .modal-body").load("/e_sign");
  $("#MyPopup").modal();
});

$("#id_signature").change(function () {
  $("#label_signature").text(this.value);
});

$("#new_entry").click(function (e) {
  e.preventDefault();
  $("input").not("[name='csrfmiddlewaretoken'],[id='id_salutation']").val("");
  return false;
});

$("#new_entry1").click(function (e) {
  e.preventDefault();
  $("input").not("[name='csrfmiddlewaretoken'],[id='id_salutation']").val("");
  $("#id_role").prop("readonly", false);
  $("#id_role").prop("tabindex", "0");
  $("#id_role").css("pointer-events", "all");
  $("#id_role").css("background-color", "#fff");
  return false;
});




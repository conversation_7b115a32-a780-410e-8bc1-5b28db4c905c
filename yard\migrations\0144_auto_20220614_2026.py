# Generated by Django 3.1.1 on 2022-06-14 20:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0143_customer_price_group'),
    ]

    operations = [
        migrations.AddField(
            model_name='article',
            name='second_price1',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='article',
            name='second_price2',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='article',
            name='second_price3',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='article',
            name='second_price4',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='article',
            name='second_price5',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='cost_centre',
            field=models.PositiveIntegerField(blank=True, default=1, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='price_group',
            field=models.CharField(blank=True, choices=[('price1', 'Price 1'), ('price2', 'Price 2'), ('price3', 'Price 3'), ('price4', 'Price 4'), ('price5', 'Price 5')], default=1, max_length=10, null=True),
        ),
    ]

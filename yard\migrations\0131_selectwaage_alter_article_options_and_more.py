# Generated by Django 4.0.4 on 2022-06-09 10:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0130_merge_20220601_1733'),
    ]

    operations = [
        migrations.CreateModel(
            name='SelectWaage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('yes', models.BooleanField(default=True)),
                ('number', models.IntegerField(blank=True, default=1, null=True)),
                ('selected', models.IntegerField(blank=True, default=1, null=True)),
            ],
        ),
        migrations.AlterModelOptions(
            name='article',
            options={'ordering': ('name',), 'verbose_name_plural': 'Artikel'},
        ),
        migrations.AlterModelOptions(
            name='buildingsite',
            options={'verbose_name_plural': '<PERSON>ustellen'},
        ),
        migrations.AlterModelOptions(
            name='combination',
            options={'verbose_name_plural': 'Kombinationen'},
        ),
        migrations.AlterModelOptions(
            name='contract',
            options={'verbose_name_plural': 'Verträge'},
        ),
        migrations.AlterModelOptions(
            name='customer',
            options={'ordering': ('name1',), 'verbose_name_plural': 'Kunden'},
        ),
        migrations.AlterModelOptions(
            name='delivery_note',
            options={'verbose_name_plural': 'Lieferscheine'},
        ),
        migrations.AlterModelOptions(
            name='foreignflag',
            options={'verbose_name_plural': 'Ausländische Flaggen'},
        ),
        migrations.AlterModelOptions(
            name='forwarders',
            options={'verbose_name_plural': 'Spediteure'},
        ),
        migrations.AlterModelOptions(
            name='settings',
            options={'verbose_name_plural': 'Einstellungen'},
        ),
        migrations.AlterModelOptions(
            name='supplier',
            options={'ordering': ('supplier_name',), 'verbose_name_plural': 'Lieferanten'},
        ),
        migrations.AlterModelOptions(
            name='transaction',
            options={'verbose_name_plural': 'Transaktion'},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'verbose_name': 'user', 'verbose_name_plural': 'Benutzer'},
        ),
        migrations.AlterModelOptions(
            name='vehicle',
            options={'ordering': ('license_plate',), 'verbose_name_plural': 'Fahrzeuge'},
        ),
        migrations.AlterModelOptions(
            name='yard_list',
            options={'verbose_name_plural': 'Hofliste'},
        ),
    ]

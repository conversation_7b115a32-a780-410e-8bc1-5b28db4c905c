{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% block content %}

<div class="row">
  <div class="col-md-6 col-lg-6 col-xl-6 edit-form">
  	<div class="accordion active">
      Settings
    </div>
    <div class="panel panel-default" style="max-height: 590px;">
        <form method="POST" enctype="multipart/form-data">
        {% csrf_token %}
            <input type="hidden" name="id" id="id">
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.name.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.name}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.customer.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.customer}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.article.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.article}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.supplier.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.supplier}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.show_article.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.show_article}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.show_supplier.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.show_supplier}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.show_yard.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.show_yard}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.show_forwarders.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.show_forwarders}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.show_storage.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.show_storage}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.show_building_site.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.show_building_site}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.read_number_from_camera.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.read_number_from_camera}}
				</div>
			</div>
			<div class="row ipt1">
				<div class="col-md-4 col-lg-4 col-xl-4">
				  <label>{{form.language.label}}</label>
				</div>
				<div class="col-md-8 col-lg-8 col-xl-8">
				  <!-- <input class="col-md-11" type="text" placeholder=""> -->{{ form.language}}
				</div>
			</div>
          <div class="row ipt1">
                <div class="col-md-4 col-lg-4 col-xs-4">
                    <button id="submit" type="submit" class="cus-button-1" >Save</button>
                </div>
            </div>
        </form>
    </div>

  </div>
</div>

{% endblock%}
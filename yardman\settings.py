
from pathlib import Path
import os
import django
from django.contrib.messages import constants as messages
from django.db import connection

MESSAGE_TAGS = {
    messages.ERROR: 'danger'
}

BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = '*cxa-#2h5y3hv(8pb!7j$n=jwdv+@8!yo+xwb4fqb$8t9xpqm5'

DEBUG = True
PROD = False
LOGIN_URL = 'sign_in'
LOGIN_REDIRECT_URL = 'home'
LOGOUT_REDIRECT_URL = 'sign_in'

ALLOWED_HOSTS = ['*']

APPEND_SLASH = True


INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',
    'crispy_forms',
    'rest_framework',
    "corsheaders",
    "yard",
    "container",
    "stats",
    'scale_app',
    "import_export",
    "django_filters",
    "rest_framework.authtoken"
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.auth.middleware.RemoteUserMiddleware',
]


REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES':
        (
            'rest_framework.permissions.IsAuthenticated',
        ),
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
}


CORS_ORIGIN_ALLOW_ALL = True

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

X_FRAME_OPTIONS = 'SAMEORIGIN'

ROOT_URLCONF = 'yardman.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'yard.contextprocessor.barrier'
            ],
        },
    },
]

WSGI_APPLICATION = 'yardman.wsgi.application'


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': 'has_db',
        'USER': 'yardman_admin',
        'PASSWORD': 'yardman_pass',
        'HOST': 'localhost',
        'PORT': '5432',
    }

}
AUTH_USER_MODEL = 'yard.User'


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'de'

TIME_ZONE = 'Europe/Berlin'

USE_I18N = True

USE_L10N = True

USE_TZ = False


STATIC_URL = '/static/'
STATIC_ROOT = 'static'
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'yard/static'),
    os.path.join(BASE_DIR, 'container/static'),

]

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880


import configparser
config = configparser.ConfigParser()
config.read(str(BASE_DIR)+"/yardman/config.ini")
printer = config["SETTINGS"]["PRINTER"]
windows = config["SETTINGS"]["WINDOWS"]
SMS_URL = config["SETTINGS"]["SMS_URL"]
SMS_USERNAME = config["SETTINGS"]["SMS_USERNAME"]
SMS_PASSWORD = config["SETTINGS"]["SMS_PASSWORD"]

PRINTER = str(printer)
WINDOWS = True

if int(windows) == 0:
    WINDOWS = False
else:
    WINDOWS = True    


MEDIA_LOCATION = None

def check_db_conn():
    try:
        connection.ensure_connection()
    except Exception as e:
        connected = False
        print(e)
        exit(1)
    else:
        connected = True


check_db_conn()

CITIES_LIGHT_TRANSLATION_LANGUAGES = ['fr', 'en', 'de', 'pt', 'it', 'es']

# only if django version >= 3.0
X_FRAME_OPTIONS = "SAMEORIGIN"
SILENCED_SYSTEM_CHECKS = ["security.W019"]

DEV_HOST = "127.0.0.1"
DEV_PORT = 3200
IMAGE_HOST = "127.0.0.1"
IMAGE_PORT = 8050


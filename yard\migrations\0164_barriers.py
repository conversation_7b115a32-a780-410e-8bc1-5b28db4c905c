# Generated by Django 3.1.1 on 2022-11-23 21:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0163_driverid'),
    ]

    operations = [
        migrations.CreateModel(
            name='Barriers',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('id_code', models.IntegerField(blank=True, null=True)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('open_command', models.CharField(blank=True, max_length=200, null=True)),
                ('close_command', models.CharField(blank=True, max_length=200, null=True)),
                ('status', models.CharField(blank=True, choices=[('close', 'close'), ('open', 'open'), ('open_permanently', 'open permanently')], max_length=100, null=True)),
            ],
        ),
    ]

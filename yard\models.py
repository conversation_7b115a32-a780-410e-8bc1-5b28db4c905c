from contextlib import nullcontext
from distutils.command.upload import upload
from sre_parse import <PERSON><PERSON><PERSON><PERSON>
from tabnanny import verbose
from django.db import models
from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.auth.models import PermissionsMixin
from django.utils.translation import gettext_lazy as _

from django.core.validators import MinValueValidator

from .managers import UserManager

SALUTATION_CHOICES = (
    (_('Mr'), _('Mr')),
    (_('Mrs'), _('Mrs')),
)

CONTRACT_STATUS = (
    # ('en-us', 'English'),
    ('Active', 'ACTIVE'),
    ('Inactive', 'INACTIVE'),
)


class User(AbstractBaseUser, PermissionsMixin):
    name = models.CharField(_('name'), max_length=30, blank=True)
    email = models.EmailField(_('email address'), unique=True)
    # password = models.Char<PERSON>ield(_('password'),max_length=100)
    date_joined = models.DateTimeField(_('date joined'), auto_now_add=True)
    is_active = models.BooleanField(_('active'), default=True)
    is_staff = models.BooleanField(_('staff status'), default=True)
    # yard_id = models.CharField(_('yard ID'), max_length=30,)
    yard = models.ForeignKey('Yard_list', on_delete=models.CASCADE, null=True)
    role = models.CharField(_('role'), max_length=30, blank=True)
    address = models.CharField(max_length=200, blank=True, null=True)
    telephone = models.CharField(max_length=20, blank=True, null=True)

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.email

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('Benutzer')

    # def get_full_name(self):
    #     '''
    #     Returns the first_name plus the last_name, with a space in between.
    #     '''
    #     full_name = '%s %s' % (self.first_name, self.last_name)
    #     return full_name.strip()


class Signature(models.Model):
    user = models.ForeignKey('User', on_delete=models.CASCADE)
    signature = models.FileField(upload_to='signatures/', null=True)


class images_base64(models.Model):
    """docstring for images_base64"""
    transaction = models.ForeignKey(
        'Transaction', on_delete=models.CASCADE, null=True)
    image1 = models.ImageField(upload_to='trans_images/', null=True)
    image2 = models.ImageField(upload_to='trans_images/', null=True)
    image3 = models.ImageField(upload_to='trans_images/', null=True)
    image4 = models.ImageField(upload_to='trans_images/', null=True)
    image5 = models.ImageField(upload_to='trans_images/', null=True)
    image6 = models.ImageField(upload_to='trans_images/', null=True)
    image7 = models.ImageField(upload_to='trans_images/', null=True)
    image8 = models.ImageField(upload_to='trans_images/', null=True)
    image9 = models.ImageField(upload_to='trans_images/', null=True)
    image10 = models.ImageField(upload_to='trans_images/', null=True)
    image11 = models.ImageField(upload_to='trans_images/', null=True)
    image12 = models.ImageField(upload_to='trans_images/', null=True)


class Article(models.Model):
    GROUP_CHOICES = (
        (1, _('Type 1')),
        (2, _('Type 2')),
        (3, _('Type 3')),
        (4, _('Type 4')),
        (5, _('Type 5')),
        (6, _('Other Type')),)
    REVENUE_GROUP_CHOICES = (
        ('revenue1', _('Revenue 1')),
        ('revenue2', _('Revenue 2')),
        ('revenue3', _('Revenue 3')),
        ('revenue4', _('Revenue 4')),
        ('revenue5', _('Revenue 5')),
    )
    VAT_CHOICES = (
        (0.0, "0%"),
        (7.0, "7%"),
        (19.0, "19%"),
    )
    UNIT_CHOICES = (
        ("0", "kg"),
        ("1", "t"),
    )
    name = models.CharField(max_length=100, unique=True)
    description = models.CharField(max_length=250, blank=True, null=True)
    short_name = models.CharField(max_length=40, blank=True, null=True)
    article_number = models.CharField(max_length=40, blank=True, null=True)
    yard = models.ForeignKey(
        'Yard_list', on_delete=models.CASCADE, blank=True, null=True)
    entry_weight = models.IntegerField(null=True, blank=True, default=0)
    balance_weight = models.IntegerField(null=True, blank=True, default=0)
    outgoing_weight = models.IntegerField(null=True, blank=True, default=0)
    price1 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    price2 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    price3 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    price4 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    price5 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)

    second_price1 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    second_price2 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    second_price3 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    second_price4 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    second_price5 = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)

    vat = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True, choices=VAT_CHOICES, default=0.0)
    minimum_amount = models.IntegerField(null=True, blank=True, default=0)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True, null=True)

    avv_num = models.CharField(max_length=250, blank=True, null=True)
    account = models.CharField(max_length=250, blank=True, null=True)
    min_quantity = models.PositiveIntegerField(blank=True, null=True)
    density = models.CharField(max_length=250, blank=True, null=True)
    deduct_weight = models.DecimalField(
        max_digits=10, decimal_places=0, null=True, blank=True)
    list_price_net = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True)
    list_price_gross = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True)
    ean = models.CharField(max_length=250, blank=True, null=True)
    ware_house = models.ForeignKey(
        'Warehouse', on_delete=models.CASCADE, blank=True, null=True)
    supplier = models.ForeignKey(
        'Supplier', on_delete=models.CASCADE, null=True)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)
    unit = models.CharField(max_length=250, blank=True, null=True, choices=UNIT_CHOICES, default="0")

    class Meta:
        ordering = ('name',)
        unique_together = ('name', 'yard',)
        verbose_name_plural = "Artikel"

    def __str__(self):
        return self.name


class Article_meta(models.Model):
    article = models.ForeignKey('Article', on_delete=models.CASCADE, null=True)
    yard = models.ForeignKey('Yard_list', on_delete=models.CASCADE)
    entry_weight = models.DecimalField(
        max_digits=10, decimal_places=3, blank=True, null=True)
    balance_weight = models.DecimalField(
        max_digits=10, decimal_places=3, blank=True, null=True)
    outgoing_weight = models.DecimalField(
        max_digits=10, decimal_places=3, blank=True, null=True)

    class Meta:
        unique_together = ('article', 'yard',)

    def __str__(self):
        return self.balance_weight


class BuildingSite(models.Model):
    name = models.CharField(max_length=100)
    short_name = models.CharField(max_length=40, blank=True, null=True)
    place = models.CharField(max_length=100, blank=True, null=True)
    street = models.CharField(max_length=100, blank=True, null=True)
    pin = models.CharField(max_length=100, blank=True, null=True)
    infotext = models.CharField(max_length=100, blank=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Baustellen"


class Delivery_note(models.Model):
    lfd_nr = models.CharField(max_length=100)
    file_name = models.CharField(max_length=100)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.lfd_nr

    class Meta:
        verbose_name_plural = "Lieferscheine"


class Vehicle(models.Model):
    VEHICLE_CHOICES = (
        ('type1', _('Type 1')),
        ('type2', _('Type 2')),
        ('type3', _('Type 3')),
        ('type4', _('Type 4')),
        ('type5', _('Type 5')),)

    # VEHICLE_TYPE_CHOICES = (
    #     ('0',_('Firmenwaage')),
    #     ('1',_('Fremdwaage')),
    # )
    license_plate = models.CharField(max_length=100, unique=True)
    license_plate2 = models.CharField(max_length=100, blank=True, null=True)
    forwarder = models.ForeignKey(
        'Forwarders', on_delete=models.CASCADE, null=True)
    place = models.CharField(max_length=100, blank=True, null=True)
    street = models.CharField(max_length=100, blank=True, null=True)
    pin = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    email = models.CharField(max_length=20, blank=True, null=True)
    # vehicle_weight = models.DecimalField(max_digits=10, decimal_places=0, default=0)
    vehicle_weight = models.IntegerField(blank=True, null=True, default=0)
    vehicle_weight2 = models.IntegerField(blank=True, null=True, default=0)
    # vehicle_weight2 = models.DecimalField(max_digits=10, decimal_places=0, default=0, blank=True, null=True)
    vehicle_weight_id = models.CharField(max_length=100, blank=True, null=True)
    vehicle_weight_date = models.DateField(blank=True, null=True)
    vehicle_weight_time = models.TimeField(blank=True, null=True)
    vehicle_weight_id2 = models.CharField(
        max_length=100, blank=True, null=True)
    vehicle_weight_date2 = models.DateField(blank=True, null=True)
    vehicle_weight_time2 = models.TimeField(blank=True, null=True)
    taken = models.PositiveIntegerField(null=True, blank=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    vehicle_type = models.CharField(
        max_length=10, choices=VEHICLE_CHOICES, null=True, blank=True)
    owner = models.CharField(max_length=100, blank=True, null=True)
    driver_name = models.CharField(max_length=100, blank=True, null=True)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)
    self_tara = models.BooleanField(default=False)

    # fahrzeugtypen = models.CharField(max_length=100, null=True, choices = VEHICLE_TYPE_CHOICES)

    def __str__(self):
        return self.license_plate

    class Meta:
        ordering = ('license_plate',)
        verbose_name_plural = "Fahrzeuge"


class Combination(models.Model):  # Kombinationen
    STATUS_CHOICES = (
        ('', _('----------')),
        ('0', _('Eingang')),
        ('1', _('Ausgang')),

    )
    ident = models.CharField(max_length=40, unique=True)
    short_name = models.CharField(
        max_length=40, blank=True, null=True)  # kurezel
    customer = models.ForeignKey(
        'Customer', on_delete=models.CASCADE, null=True, blank=True)
    vehicle = models.ForeignKey(
        'Vehicle', on_delete=models.CASCADE, null=True, blank=True)
    building_site = models.ForeignKey(
        'BuildingSite', on_delete=models.CASCADE, null=True, blank=True)
    supplier = models.ForeignKey(
        'Supplier', on_delete=models.CASCADE, null=True, blank=True)
    forwarders = models.ForeignKey(
        'Forwarders', on_delete=models.CASCADE, null=True, blank=True)  # spediteure
    article = models.ForeignKey(
        'Article', on_delete=models.CASCADE, null=True, blank=True)
    yard = models.ForeignKey(
        'Yard_list', on_delete=models.CASCADE, null=True, blank=True)
    container = models.ForeignKey(
        'Container', on_delete=models.CASCADE, null=True, blank=True)
    place_of_delivery = models.ForeignKey(
        'PlaceOfDelivery', on_delete=models.CASCADE, null=True, blank=True)
    transaction_id = models.ForeignKey(
        'Transaction', on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(max_length=50,
                              choices=STATUS_CHOICES,
                              blank=True,
                              null=True)
    contracts = models.ManyToManyField('Contract', related_name='contracts', null=True, blank=True)
    tara_with_mobile = models.BooleanField(default=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.ident

    class Meta:
        verbose_name_plural = "Kombinationen"


class Customer(models.Model):  # Kunden

    PRICE_CHOICES = (
        ('price1', _('Price 1')),
        ('price2', _('Price 2')),
        ('price3', _('Price 3')),
        ('price4', _('Price 4')),
        ('price5', _('Price 5')),
    )
    DELIVERY_CHOICES = (
        ('free', _('Free')),
        ('paid', _('Paid')),
        ('other', _('Other')),
    )
    name1 = models.CharField(max_length=40)
    name2 = models.CharField(max_length=40, blank=True, null=True)  # vorname
    company = models.CharField(max_length=40, blank=True, null=True)
    salutation = models.CharField(
        max_length=10, choices=SALUTATION_CHOICES, blank=True, null=True)
    addition1 = models.CharField(max_length=40, blank=True, null=True)
    addition2 = models.CharField(max_length=40, blank=True, null=True)
    addition3 = models.CharField(max_length=40, blank=True, null=True)
    post_office_box = models.CharField(max_length=40, blank=True, null=True)
    description = models.CharField(
        max_length=250, blank=True, null=True)  # bezeichnung
    street = models.CharField(max_length=250, blank=True, null=True)  # strasse
    price_group = models.CharField(
        max_length=10, choices=PRICE_CHOICES, blank=True, null=True)
    pin = models.CharField(max_length=10, blank=True, null=True)
    fax = models.CharField(max_length=15, blank=True, null=True)
    place = models.CharField(max_length=100, blank=True, null=True)
    website = models.CharField(max_length=100, blank=True, null=True)
    cost_centre = models.PositiveIntegerField(
        null=True, default=1, blank=True)  # kostenstelle
    country = models.CharField(max_length=100, blank=True, null=True)
    # email = models.CharField(max_length=40, blankHi M=True, null=True)
    contact_person1_email = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person2_email = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person3_email = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person1_phone = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person2_phone = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person3_phone = models.CharField(
        max_length=40, blank=True, null=True)
    diff_invoice_recipient = models.CharField(
        max_length=40, blank=True, null=True)
    customer_type = models.CharField(max_length=40, blank=True, null=True)
    # price_group = models.CharField(
    #     max_length=10, choices=PRICE_CHOICES, blank=True, null=True)
    # classification = models.CharField(max_length=40, blank=True, null=True)
    sector = models.CharField(max_length=40, blank=True, null=True)
    company_size = models.CharField(max_length=40, blank=True, null=True)
    area = models.CharField(max_length=40, blank=True, null=True)
    # warehouse = models.ForeignKey(
    #     'Warehouse', on_delete=models.CASCADE, null=True)
    private_person = models.BooleanField(default=False)
    document_lock = models.BooleanField(default=False)
    payment_block = models.BooleanField(default=False)
    delivery_terms = models.CharField(
        max_length=10, choices=DELIVERY_CHOICES, blank=True, null=True)
    special_discount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    debitor_number = models.PositiveIntegerField(
        null=True, blank=True, unique=True)  # kostenstelle
    dunning = models.CharField(max_length=10, null=True, blank=True)
    perm_street = models.CharField(
        max_length=100, blank=True, null=True)  # strasse
    perm_pin = models.CharField(max_length=10, blank=True, null=True)
    perm_place = models.CharField(max_length=100, blank=True, null=True)
    perm_country = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=100, blank=True, null=True)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name1

    class Meta:
        verbose_name_plural = "Kunden"
        ordering = ('name1',)


class Supplier(models.Model):  # Lieferanten

    supplier_name = models.CharField(
        max_length=100, unique=True)  # Lieferanten_name
    name = models.CharField(max_length=40, blank=True, null=True)
    # first_name = models.CharField(
    #     max_length=40, blank=True, null=True)  # kurezel
    street = models.CharField(max_length=100, blank=True, null=True)  # strasse
    pin = models.CharField(max_length=10, blank=True, null=True)
    # fax = models.CharField(max_length=15, blank=True, null=True)
    place = models.CharField(max_length=100, blank=True, null=True)
    # infotext = models.CharField(max_length=100, blank=True, null=True)
    # salutation = models.CharField(
    #     max_length=10, choices=SALUTATION_CHOICES, blank=True, null=True)
    addition1 = models.CharField(max_length=100, blank=True, null=True)
    addition2 = models.CharField(max_length=100, blank=True, null=True)
    addition3 = models.CharField(max_length=100, blank=True, null=True)
    # post_office_box = models.CharField(max_length=40, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    contact_person1_email = models.CharField(
        max_length=40, blank=True, null=True)
    project_number = models.CharField(
        max_length=40, blank=True, null=True)
    # contact_person2_email = models.CharField(
    #     max_length=40, blank=True, null=True)
    # contact_person3_email = models.CharField(
    #     max_length=40, blank=True, null=True)
    contact_person1_phone = models.CharField(
        max_length=40, blank=True, null=True)
    # contact_person2_phone = models.CharField(
    #     max_length=40, blank=True, null=True)
    # contact_person3_phone = models.CharField(
    #     max_length=40, blank=True, null=True)
    # website = models.CharField(max_length=100, blank=True, null=True)
    # cost_centre = models.PositiveIntegerField(null=True, default=1)
    # warehouse = models.ForeignKey(
    #     'Warehouse', on_delete=models.CASCADE, null=True)
    # creditor_number = models.PositiveIntegerField(null=True, blank=True)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.supplier_name

    class Meta:
        ordering = ('supplier_name',)
        verbose_name_plural = "Lieferanten"


class PlaceOfDelivery(models.Model):
    # name = models.CharField(max_length=40, blank=True, null=True)
    # first_name = models.CharField(
    #     max_length=40, blank=True, null=True)  # kurezel
    PRICE_CHOICES = (
        ('price1', _('Price 1')),
        ('price2', _('Price 2')),
        ('price3', _('Price 3')),
        ('price4', _('Price 4')),
        ('price5', _('Price 5')),
    )

    TERM_OF_PAYMENT_CHOICES = (
        ('Sofort', _('Sofort')),
        ('7 Tage', _('7 Tage')),
        ('14 Tage', _('14 Tage')),
        ('30 Tage', _('30 Tage')),
    )

    DELIVERY_CHOICES = (
        ('free', _('Free')),
        ('paid', _('Paid')),
        ('other', _('Other')),
    )
    name1 = models.CharField(max_length=40, unique=True, null=True)
    name2 = models.CharField(max_length=40, blank=True, null=True)  # vorname
    company = models.CharField(max_length=40, blank=True, null=True)
    salutation = models.CharField(
        max_length=10, choices=SALUTATION_CHOICES, blank=True, null=True)
    addition1 = models.CharField(max_length=40, blank=True, null=True)
    addition2 = models.CharField(max_length=40, blank=True, null=True)
    addition3 = models.CharField(max_length=40, blank=True, null=True)
    post_office_box = models.CharField(max_length=40, blank=True, null=True)
    description = models.CharField(
        max_length=250, blank=True, null=True)  # bezeichnung
    street = models.CharField(max_length=250, blank=True, null=True)  # strasse
    pin = models.CharField(max_length=10, blank=True, null=True)
    fax = models.CharField(max_length=15, blank=True, null=True)
    place = models.CharField(max_length=100, blank=True, null=True)
    website = models.CharField(max_length=100, blank=True, null=True)
    cost_centre = models.PositiveIntegerField(
        null=True, default=1)  # kostenstelle
    country = models.CharField(max_length=100, blank=True, null=True)
    # email = models.CharField(max_length=40, blankHi M=True, null=True)
    contact_person1_email = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person2_email = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person3_email = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person1_phone = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person2_phone = models.CharField(
        max_length=40, blank=True, null=True)
    contact_person3_phone = models.CharField(
        max_length=40, blank=True, null=True)
    diff_invoice_recipient = models.CharField(
        max_length=40, blank=True, null=True)
    customer_type = models.CharField(max_length=40, blank=True, null=True)
    # price_group = models.CharField(
    #     max_length=10, choices=PRICE_CHOICES, blank=True, null=True)
    # classification = models.CharField(max_length=40, blank=True, null=True)
    sector = models.CharField(max_length=40, blank=True, null=True)
    company_size = models.CharField(max_length=40, blank=True, null=True)
    area = models.CharField(max_length=40, blank=True, null=True)
    # warehouse = models.ForeignKey(
    #     'Warehouse', on_delete=models.CASCADE, null=True)
    private_person = models.BooleanField(default=False)
    document_lock = models.BooleanField(default=False)
    payment_block = models.BooleanField(default=False)
    delivery_terms = models.CharField(
        max_length=10, choices=DELIVERY_CHOICES, blank=True, null=True)
    term_of_payment = models.CharField(
        max_length=10, choices=TERM_OF_PAYMENT_CHOICES, blank=True, null=True)
    special_discount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True)
    kreditoren_number = models.PositiveIntegerField(
        null=True, blank=True)  # kostenstelle
    dunning = models.CharField(max_length=10, null=True, blank=True)
    perm_street = models.CharField(
        max_length=100, blank=True, null=True)  # strasse
    perm_pin = models.CharField(max_length=10, blank=True, null=True)
    perm_place = models.CharField(max_length=100, blank=True, null=True)
    perm_country = models.CharField(max_length=100, blank=True, null=True)
    phone_number = models.CharField(max_length=100, blank=True, null=True)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name1

    class Meta:
        ordering = ('name1',)


class Forwarders(models.Model):  # Spediteure
    name = models.CharField(max_length=100, unique=True)
    firstname = models.CharField(
        max_length=100, blank=True, null=True)  # vorname
    second_name = models.CharField(max_length=100, blank=True, null=True)
    street = models.CharField(max_length=100, blank=True, null=True)  # strasse
    pin = models.CharField(max_length=10, blank=True, null=True)  # plz
    telephone = models.CharField(
        max_length=15, blank=True, null=True)  # telefon
    place = models.CharField(max_length=100, blank=True, null=True)  # ort
    country = models.CharField(max_length=100, blank=True, null=True)  # ort
    contact_person = models.CharField(
        max_length=100, blank=True, null=True)  # ansprech_partner
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Spediteure"


class Transaction(models.Model):
    TRANS_CHOICES = (
        (0, 'Initial_Weighing'),
        (1, 'Second_Weighing'),
        (2, 'Closed_Weighing'),
        (3, 'Parking'),
    )
    vehicle = models.ForeignKey(
        'Vehicle', on_delete=models.SET_NULL, null=True, blank=True)
    article = models.ForeignKey(
        'Article', related_name='article', on_delete=models.SET_NULL, null=True, blank=True)
    deduction = models.ManyToManyField('yard.Article', null=True, blank=True)
    deduction_weight = models.DecimalField(
        max_digits=10, decimal_places=0, null=True, blank=True)
    material_weight = models.DecimalField(
        max_digits=10, decimal_places=0, null=True, blank=True)
    customer = models.ForeignKey(
        'Customer', on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(
        'Supplier', on_delete=models.SET_NULL, null=True, blank=True)
    place_of_delivery = models.ForeignKey(
        'PlaceOfDelivery', on_delete=models.SET_NULL, null=True, blank=True)
    container = models.ForeignKey(
        'Container', on_delete=models.SET_NULL, null=True, blank=True)
    yard = models.ForeignKey(
        'Yard_list', on_delete=models.SET_NULL, null=True, blank=True)
    item_price = models.BooleanField(default=False, null=True, blank=True)
    combination_id = models.CharField(max_length=100, blank=True, null=True)
    carrier = models.CharField(max_length=250, blank=True, null=True)
    delivery_location = models.TextField(blank=True, null=True)
    export_goods = models.BooleanField(default=False)
    first_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0, null=True)
    second_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0, null=True)
    net_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0, null=True)
    total_price = models.DecimalField(
        max_digits=10, decimal_places=0, null=True, blank=True)
    lfd_nr = models.CharField(max_length=40, null=True, blank=True)
    firstw_date_time = models.DateTimeField(blank=True, null=True)
    secondw_date_time = models.DateTimeField(blank=True, null=True)
    firstw_alibi_nr = models.CharField(max_length=40, null=True, blank=True)
    secondw_alibi_nr = models.CharField(max_length=40, null=True, blank=True)
    vehicle_weight_flag = models.IntegerField(default=0, blank=True, null=True)
    vehicle_second_weight_flag = models.IntegerField(
        default=0, blank=True, null=True)
    hand_delieverynote = models.CharField(max_length=40, null=True, blank=True)
    estimated_fm = models.CharField(max_length=40, null=True, blank=True)
    # is_deleted = models.BooleanField(default=False, verbose_name="Is Deleted")
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    trans_flag = models.PositiveIntegerField(null=True, choices=TRANS_CHOICES)
    forwarders = models.ForeignKey('Forwarders', on_delete=models.CASCADE, null=True, blank=True)
    price_per_item = models.DecimalField(
        max_digits=10, decimal_places=2, default=00, blank=True, null=True)
    status = models.CharField(max_length=20, blank=True, null=True)
    unloading_type = models.CharField(max_length=100, blank=True, null=True)
    contract_number = models.ForeignKey(
        "yard.Contract", on_delete=models.CASCADE, blank=True, null=True)
    lang = models.CharField(max_length=40, null=True, blank=True)
    driver_name = models.CharField(max_length=300, null=True, blank=True)
    temp = models.CharField(max_length=40, null=True, blank=True)
    comment = models.CharField(max_length=300, null=True, blank=True)
    gate = models.CharField(max_length=300, null=True, blank=True)
    trailer = models.CharField(max_length=50, null=True, blank=True)
    container_weighing = models.BooleanField(default=False)
    external_weighing = models.BooleanField(default=False)
    waage_number = models.CharField(max_length=40, null=True, blank=True)
    project_number = models.CharField(max_length=50, null=True, blank=True)
    approved = models.BooleanField(default=False)
    first_scale = models.CharField(max_length=250, blank=True, null=True)
    second_scale = models.CharField(max_length=250, blank=True, null=True)
    store_number = models.CharField(max_length=50, null=True, blank=True)
    store_number_hold = models.CharField(max_length=10, null=True, blank=True)
    mobile_number = models.CharField(max_length=20, null=True, blank=True)
    load_securing = models.CharField(max_length=100, null=True, blank=True)
    register_parking = models.BooleanField(default=False)
    crane = models.BooleanField(default=False)

    def get_context_transaction(self, request):
        context = {}
        context["absolute_url"] = "http://" + request.get_host()
        context['logo'] = Logo.objects.all()
        context['role'] = request.user.role
        context['user_name'] = request.user.name
        context['sign'] = Signature.objects.filter(user=request.user).last()
        context["dataset"] = self
        context["images"] = self.images_base64_set.first()
        return context

    def __str__(self):
        return str(self.id) if self.id else ''

    class Meta:
        verbose_name_plural = "Transaktion"


class Yard_list(models.Model):
    # license_plate = models.CharField(max_length=100, null=True, blank=True)#kennung
    # vehicle_weight = models.PositiveIntegerField(blank=True, null=True)#tara
    # vehicle_weight_id = models.CharField(max_length=100, null=True, blank=True)#tara_id
    # created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    # updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    name = models.CharField(max_length=100, unique=True)
    street = models.CharField(max_length=40, blank=True, null=True)
    pin = models.CharField(max_length=10, blank=True, null=True)
    place = models.CharField(max_length=40, blank=True, null=True)
    country = models.CharField(max_length=40, blank=True, null=True)
    telephone = models.CharField(max_length=15, blank=True, null=True)
    email = models.CharField(max_length=40, blank=True, null=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Hofliste"


class Settings(models.Model):
    CUSTOMER_CHOICES = (
        ('Kunde', 'Kunde'),
        ('Erzeuger', 'Erzeuger'),
        # ('Customer', 'Customer'),
    )  # KUNDE_CHOICES
    SUPPLIER_CHOICES = (
        ('Baustelle', 'Baustelle'),
        ('Schlag', 'Schlag'),
        # ('Supplier', 'Supplier'),
    )  # LIEFERANT_CHOICES
    ARTICLE_CHOICES = (
        ('Material', 'Material'),
        ('Produkt', 'Produkt'),
        # ('kultur', 'Kultur'),
        ('Artikel', 'Artikel'),
        # ('Article', 'Article'),
    )
    LANGUAGE_CHOICES = (
        # ('en-us', 'English'),
        ('de', 'Deutsch'),
        ('fr', 'French'),
        ('ru', 'Russian'),
    )
    name = models.CharField(max_length=40, blank=True, null=True, unique=True)
    customer = models.CharField(
        max_length=40, null=True, choices=CUSTOMER_CHOICES)  # kunde
    supplier = models.CharField(
        max_length=40, null=True, choices=SUPPLIER_CHOICES)  # lieferant
    article = models.CharField(
        max_length=40, null=True, choices=ARTICLE_CHOICES)  # artikel
    show_article = models.BooleanField(default=True)  # show_artikel
    show_supplier = models.BooleanField(default=True)  # show_lieferant
    show_yard = models.BooleanField(default=True)
    show_forwarders = models.BooleanField(default=True)  # show_spediteure
    show_storage = models.BooleanField(default=True)
    show_building_site = models.BooleanField(default=True)  # show_baustellen
    company_email = models.EmailField(null=True, blank=True)
    yard = models.ForeignKey('Yard_list', on_delete=models.CASCADE)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    read_number_from_camera = models.BooleanField(
        default=False)  # read_fahr_camera
    language = models.CharField(
        max_length=40, null=True, choices=LANGUAGE_CHOICES, default="en-us")  # LANGUAGE
    smtp_support = models.BooleanField(default=False)
    smtp_creds = models.ForeignKey(
        'SMTPCred', on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        # return self.identifier
        return str(self.id)

    class Meta:
        verbose_name_plural = "Einstellungen"


class SMTPCred(models.Model):
    host = models.CharField(max_length=1000, unique=True)
    port = models.IntegerField()
    username = models.CharField(max_length=1000, unique=True)
    password = models.CharField(max_length=1000)
    sender_address = models.EmailField()


class Contract(models.Model):
    PRICE_CHOICES = (
        ('price1', _('Price 1')),
        ('price2', _('Price 2')),
        ('price3', _('Price 3')),
        ('price4', _('Price 4')),
        ('price5', _('Price 5')),
    )

    STATUS_CHOICES = (
        ('', _('----------')),
        ('0', _('Eingang')),
        ('1', _('Ausgang')),
        ('2', _('Fremdweigung')),
    )
    UNIT_CHOICES = (
        ("0", "kg"),
        ("1", "tonne"),
    )

    name = models.CharField(max_length=300)
    contract_number = models.CharField(
        max_length=1000, primary_key=True, unique=True)
    customer = models.ForeignKey(
        "yard.Customer", on_delete=models.CASCADE, blank=True, null=True)
    required_materials = models.JSONField(blank=False)
    status = models.CharField(max_length=50,
                              choices=STATUS_CHOICES,
                              blank=True,
                              null=True)
    project_number = models.CharField(max_length=1000)
    contract_status = models.CharField(choices=CONTRACT_STATUS, max_length=100)
    forwarders = models.ForeignKey(
        'Forwarders', on_delete=models.SET_NULL, null=True, blank=True)
    reserved_date = models.DateField(blank=True, null=True)
    allow_tara = models.BooleanField(default=False)
    vehicles = models.ManyToManyField("yard.Vehicle", blank=True, null=True)
    price_group = models.CharField(
        max_length=10, choices=PRICE_CHOICES, blank=True, null=True)
    supplier = models.ManyToManyField("yard.Supplier", blank=True, null=True)
    target_value = models.CharField(max_length=100, blank=True, null=True)
    unit = models.CharField(max_length=100, blank=True, null=True, choices=UNIT_CHOICES)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Verträge"


class Container(models.Model):
    UNIT_CHOICES = (
        ("0", "kg"),
        ("1", "tonne"),
    )

    CONTAINER_TYPE_CHOICES = (
        ("20′", "20′"),
        ("40′", "40′"),
        ("45′HC", "45′HC"),
        ("45′PW", "45′PW"),
        ("48′HC", "48′HC"),
        ("53′HC", "53′HC")
    )

    HAZARD_WARNING_CHOICES = (
        ("Explosionsgefährlich", "Explosionsgefährlich"),
        ("Hochentzündlich", "Hochentzündlich"),
        ("Leichtentzündlich", "Leichtentzündlich"),
        ("Brandfördernd", "Brandfördernd"),
        ("Sehr giftig", "Sehr giftig"),
        ("Giftig", "Giftig"),
        ("Gesundheitsschädlich", "Gesundheitsschädlich"),
        ("Ätzend", "Ätzend"),
        ("Reizend", "Reizend"),
        ("Umweltgefährlich", "Umweltgefährlich")
    )

    container_type = models.CharField(max_length=40, blank=True, null=True, choices=CONTAINER_TYPE_CHOICES)
    container_weight = models.IntegerField(blank=True, default=0)
    volume = models.CharField(max_length=40, blank=True, null=True)
    last_site = models.ForeignKey(
        'BuildingSite', on_delete=models.CASCADE, null=True, blank=True)
    check_digit = models.CharField(max_length=40, blank=True, null=True)
    curb_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0, null=True)
    total_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0, null=True)

    payload = models.DecimalField(
        max_digits=10, decimal_places=0, default=0, null=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    # NEW DEMANDED FIELDS
    container_number = models.PositiveIntegerField(null=True, blank=True)
    maximum_gross_weight = models.DecimalField(
        max_digits=10, decimal_places=2, default=0, blank=True)
    tare_weight = models.DecimalField(
        max_digits=10, decimal_places=2, default=0, blank=True, null=True)
    payload_container_volume = models.CharField(
        max_length=40, null=True, blank=True)
    next_exam = models.DateField(blank=True, null=True)
    waste_type = models.CharField(max_length=50, blank=True, null=True)
    hazard_warnings = models.CharField(max_length=200, blank=True, null=True, choices=HAZARD_WARNING_CHOICES)
    ss_role_access = models.ManyToManyField('yard.User', blank=True)
    unit = models.CharField(max_length=250, blank=True, null=True, choices=UNIT_CHOICES)

    def __str__(self):
        return str(self.container_number) if self.container_number else ''


class Warehouse(models.Model):
    name = models.CharField(max_length=100, blank=True, null=True)
    stock_designation = models.CharField(max_length=100, blank=True, null=True)
    stock_number = models.CharField(max_length=100, null=True)
    stock_item = models.BooleanField(default=True)
    locked_warehouse = models.BooleanField(default=True)
    # ordered = models.BooleanField(default=True)
    # production = models.CharField(max_length=100, blank=True, null=True)
    reserved = models.CharField(max_length=100, blank=True, null=True)
    available = models.PositiveIntegerField(blank=True, null=True)
    total_stock = models.PositiveIntegerField(blank=True, null=True)
    store = models.PositiveIntegerField(blank=True, null=True)
    outsource = models.PositiveIntegerField(blank=True, null=True)
    parking_space = models.CharField(max_length=100, blank=True, null=True)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    # NEW DEMANDED FIELDS
    storage_location = models.CharField(max_length=100, blank=True, null=True)
    warehouse_street = models.CharField(max_length=100, blank=True, null=True)

    # minimum_quantity = models.PositiveIntegerField(blank=True, null=True)

    def __str__(self):
        return str(self.stock_number) if self.stock_number else ''


class ForeignWeigh(models.Model):
    customer = models.CharField(max_length=100, blank=True, null=True)
    vehicle = models.CharField(max_length=100, blank=True, null=True)
    supplier = models.CharField(max_length=100, blank=True, null=True)
    article = models.CharField(max_length=100, blank=True, null=True)
    first_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)
    second_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)
    net_weight = models.DecimalField(
        max_digits=10, decimal_places=0, default=0)
    total_price = models.DecimalField(
        max_digits=10, decimal_places=0, null=True, blank=True)
    status = models.CharField(max_length=100, blank=True, null=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    secondw_alibi_nr = models.CharField(max_length=100, blank=True, null=True)
    transaction_id = models.IntegerField(blank=True, null=True)


class Logo(models.Model):
    heading = models.TextField(max_length=1000, blank=True, null=True)
    logo = models.ImageField(upload_to="logo/", null=True)
    invoice_header = models.ImageField(upload_to='invoice/', null=True, blank=True)
    footer = models.ImageField(upload_to='footer/', null=True, blank=True)


class SelectCamera(models.Model):
    yes = models.BooleanField(default=True)
    number = models.IntegerField(default=1, blank=True, null=True)


class SelectWaage(models.Model):
    yes = models.BooleanField(default=True)
    number = models.IntegerField(default=1, blank=True, null=True)
    selected = models.IntegerField(default=1, blank=True, null=True)


class Barrier(models.Model):
    barrier = models.BooleanField(default=False)
    count = models.IntegerField(default=1, blank=True, null=True)


class TrafficLight(models.Model):
    status = models.BooleanField(default=False)


class PriceGroup(models.Model):
    status = models.BooleanField(default=False)


class ContainerShow(models.Model):
    status = models.BooleanField(default=False)


class ShowTonne(models.Model):
    status = models.BooleanField(default=False)


class Io(models.Model):
    status = models.BooleanField(default=False)


class ExternalWeigh(models.Model):
    status = models.BooleanField(default=False)


class DriverSignEnable(models.Model):
    status = models.BooleanField(default=False)


class DeductionShow(models.Model):
    status = models.BooleanField(default=False)


class ForeignFlag(models.Model):
    status = models.IntegerField(default=0)

    class Meta:
        verbose_name_plural = "Ausländische Flaggen"


class AutoCapture(models.Model):
    status = models.BooleanField(default=False)


class ShowInvoice(models.Model):
    status = models.BooleanField(default=False)


class ShowTaraWeight(models.Model):
    status = models.BooleanField(default=False)


class FirstWeightCameras(models.Model):
    cam1 = models.BooleanField(default=False)
    cam2 = models.BooleanField(default=False)
    cam3 = models.BooleanField(default=False)


class SecondWeightCameras(models.Model):
    cam1 = models.BooleanField(default=False)
    cam2 = models.BooleanField(default=False)
    cam3 = models.BooleanField(default=False)


class DeliveryNoteTemplate(models.Model):
    option = models.IntegerField(blank=True, null=True)


class DriverSignature(models.Model):
    transaction_id = models.ForeignKey('Transaction', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='drivers_sign/', null=True)


class IDtext(models.Model):
    text = models.CharField(max_length=200, blank=True, null=True)


class HandTransmitter(models.Model):
    name = models.CharField(max_length=40, blank=True,
                            null=True)  # kurezel
    combination = models.ForeignKey('Combination',
                                    on_delete=models.CASCADE,
                                    null=True,
                                    blank=True)
    devise_id = models.CharField(max_length=50, blank=True, null=True)


class HandTransmitterSetting(models.Model):
    status = models.BooleanField(default=False)


class WeightPageSetting(models.Model):
    forwarder = models.IntegerField(default=1, blank=True, null=True)
    customer = models.IntegerField(default=1, blank=True, null=True)
    supplier = models.IntegerField(default=1, blank=True, null=True)
    material = models.IntegerField(default=1, blank=True, null=True)
    comment = models.IntegerField(default=1, blank=True, null=True)


class Invoice(models.Model):
    invoice_no = models.IntegerField()
    invoice = models.ForeignKey(Transaction, on_delete=models.CASCADE, unique=True, blank=True, null=True)
    time = models.DateTimeField(auto_now_add=True)


class CompanyTelePhone(models.Model):
    tele_phone_number = models.CharField(max_length=200)


class IdDeliveryNoteOption(models.Model):
    option = models.IntegerField(blank=True, null=True)


class ApiDeliveryNoteOption(models.Model):
    option = models.IntegerField(blank=True, null=True)


class MasterContainer(models.Model):
    status = models.BooleanField(default=False)


class DriverID(models.Model):
    ident_code = models.CharField(max_length=50, unique=True)
    fahrzeug = models.CharField(max_length=50, null=True, blank=True)
    spediteur = models.CharField(max_length=50, null=True, blank=True)
    fahrer = models.CharField(max_length=50, null=True, blank=True)
    time_type = models.CharField(max_length=50, null=True, blank=True)
    start_period = models.DateTimeField(null=True)
    end_period = models.DateTimeField(null=True)
    always_valid = models.BooleanField(default=False)
    block = models.BooleanField(default=False)
    one_time = models.BooleanField(default=False)
    gate = models.CharField(max_length=250, null=True, blank=True, default='all')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.ident_code)


class Barriers(models.Model):
    STATUS_CHOICES = (
        ('close', 'close'),
        ('open', 'open'),
        ('open_permanently', 'open permanently'),
    )

    id_code = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=200, blank=True, null=True)
    open_command = models.CharField(max_length=200, blank=True, null=True)
    close_command = models.CharField(max_length=200, blank=True, null=True)
    group = models.IntegerField(blank=True, null=True)
    status = models.CharField(max_length=100, blank=True, null=True, choices=STATUS_CHOICES)


class OfficeName(models.Model):
    ident_code = models.CharField(max_length=10, unique=True, null=True)
    name = models.CharField(max_length=50, null=True)
    camera = models.BooleanField(default=False)
    signature = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.ident_code} | {self.name}"


class OfficeTiming(models.Model):
    office_name_id = models.ForeignKey(OfficeName, related_name='officetiming', on_delete=models.CASCADE, null=True)
    day_name = models.CharField(max_length=50)
    show = models.BooleanField(default=False)
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)

    def __str__(self):
        return f"{str(self.office_name_id)} | {self.day_name}"


class RouteImage(models.Model):
    route_code = models.CharField(max_length=50, null=True, unique=True)
    image = models.ImageField(upload_to="route_images/", null=True)

    def __str__(self):
        return f"{str(self.route_code)}"


class Notification(models.Model):
    key = models.CharField(max_length=50, null=True)
    msg_type = models.CharField(max_length=50, null=True, blank=True)
    msg = models.TextField(max_length=500, null=True, blank=True)
    weight = models.CharField(max_length=50, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    status = models.BooleanField(default=False)

    def __str__(self):
        return f"{str(self.key)}"


class VehicleType(models.Model):
    id_code = models.IntegerField(blank=True, null=True)
    vehicle_type = models.CharField(max_length=250, null=True, unique=True)
    process_code = models.CharField(max_length=250, blank=True, null=True)

    def __str__(self):
        return f"{str(self.vehicle_type)}"


class FireAlarm(models.Model):
    status = models.BooleanField(default=False)


class Phone(models.Model):
    Mobile = models.CharField(max_length=20, blank=False)
    isVerified = models.BooleanField(blank=False, default=False)
    counter = models.IntegerField(default=0, blank=False)   # For HOTP Verification

    def __str__(self):
        return str(self.Mobile)
    

class DisplayText(models.Model):
    text = models.TextField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id} | {self.text}"


class TourApprove(models.Model):
    vehicle = models.CharField(max_length=250, null=True, blank=True)
    tour_number = models.CharField(max_length=250, null=True, blank=True)
    status = models.CharField(null=True, blank=True, max_length=250)
    planned_date = models.CharField(null=True, blank=True, max_length=250)
    deleted = models.BooleanField(default=False)
    time = models.DateTimeField(auto_now_add=True, blank=True)

    def __str__(self):
        return f"{str(self.vehicle)} | {self.status}"

class LoadingBox(models.Model):
    loading_box_number = models.CharField(max_length=20, unique=True)
    status = models.BooleanField(default=True)

    def __str__(self):
        return f"{str(self.loading_box_number)} | {self.status}"

class MaxVehicle(models.Model):
    process_code = models.CharField(max_length=20, unique=True)
    process_name = models.CharField(max_length=50)
    max_number = models.IntegerField(default=0)

    def __str__(self):
        return f"{str(self.process_code)} | {self.process_name} | {self.max_number}"
{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{% load i18n %}
{% block content %}
     <input type="hidden" id="cache_smtp_creds" value="">
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate "OVERVIEW" %}</p>
            </div>
            <div class="heding">
              <p>{% translate "Settings" %}</p>
            </div>
          </div>
        </div>
    <!---------------------------------------------------------------->
    <!-- {{form.errors}} -->
    <div class="card">
      <div class="row">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
          <h5 class="card-header info-color white-text py-3">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18">
                  <div class="row">
                    <div class="col-sm-12 col-12 text-left">
                      <p class="mb-0 pt-2 text-color text_color">{% translate "Settings" %}</p>
                    </div>
                  </div>
                </a>
              </h4>
            </div>
          </h5>
        </div>
      </div>

      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12"></div>
      <div id="collapse_18" class="collapse show" >
        <div class="panel-body">  
           <div class="card-body text-left">
          <form class="form-group" method="POST" enctype="multipart/form-data">
            {% csrf_token %}
            <input type="hidden" name="id" id="id">
              <input type="hidden" value="{% if form.smtp_creds.value == None %}{{ "" }}{% else %}{{ form.smtp_creds.value }}{% endif %}" name="smtp_creds" id="smtp_creds">
          {% if form.smtp_support.value %}
                <input type="checkbox" style="display: none" checked id="smtp_supp_hidden" name="smtp_support">
            {% else %}
              <input type="checkbox" style="display: none" id="smtp_supp_hidden" name="smtp_support">
          {% endif %}
            <div class="row">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                {% comment %} 
                <div class="md-form mb-3">
                  <label>{{form.name.label}}</label>
                </div> {% endcomment %}
                <div class="md-form mb-3">
                  <label>{{form.customer.label}}</label>
                </div>
                <div class="md-form mb-3">
                  <label>{{form.article.label}}</label>
                </div>
                <div class="md-form mb-3">
                  <label>{{form.supplier.label}}</label>
                </div>
                <div class="md-form mb-3">
                  <label>{{form.language.label}}</label>
                </div>
                <div class="md-form mb-3">
                    <label>{{ form.company_email.label }}</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Show PriceGroup' %} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Show Container' %} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Weight Unit' %} ?</label>
                </div>
                   <div class="md-form mb-3">
                  <label>{{ "Tarawägung" }} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Show Material Status' %} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Show External Weighing' %} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Show Deduction Value' %} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans 'Automatic Capture' %} ?</label>
                </div>
                <div class="md-form mb-3">
                  <label>{% trans "Driver's Signature" %} ?</label>
                </div>
                <div class="md-form mb-3">
                    <label>{% translate "SMTP Support" %}</label>
                </div>
              </div>
             
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
              {% comment %} 
              <div class="md-form mb-3">
                {{ form.name}} {{ form.name.errors}}
              </div> {% endcomment %}
              <div class="row ">
                {% for customer_choice in form.customer %}
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                    <div class="form-check formCheck_">
                      {{ customer_choice }}
                    </div>
                  </div>
                {% endfor %}
                    {{form.customer.errors}}
              </div>
              <div class="row mt-3">
                {% for article_choice in form.article %}
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12 ">
                <div class="form-check formCheck_">
                  {{ article_choice }}
                </div>
                </div>
                {% endfor %}
                {{form.article.errors}}
              </div>
              <div class="row mt-3">
                {% for supplier_choice in form.supplier %}
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                <div class="form-check formCheck_">
                  {{ supplier_choice }}
                </div>
                </div>
                {% endfor %}
                {{form.supplier.errors}}
              </div>
              <div class="row">
                <div class="col-12">
                  {{ form.language}}
                  {{form.language.errors}}
                </div>              
              </div>
            <div class="row mt-lg-1">
              <div class="col-12">
                {{ form.company_email }}
              </div>
            </div>
            <div class="row mt-2">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="p_grp" type="checkbox" name="p_grp_y" {% if price.status == 1 %}checked {% endif %} > {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="p_grp" type="checkbox" name="p_grp_n" {% if price.status == 0 %}checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-2">
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                <label class="alignCenter"><input id="contr" type="checkbox" name="contr_y" {% if contr.status == 1 %} checked {% endif %}> {% trans 'Yes' %}</label>
                </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                <label class="alignCenter"><input id="contr" type="checkbox" name="contr_n" {% if contr.status == 0 %}checked {% endif %}> {% trans 'No' %}</label>
                </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="showt" type="checkbox" name="show_ty" {% if show_t.status == 1 %} checked {% endif %}>  kg</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="showt" type="checkbox" name="show_tn" {% if show_t.status == 0 %}checked {% endif %}>  Tonne</label>
                  </div>
            </div>
               <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="show_tara_weight" type="checkbox" name="show_tara_weight" value="Yes" {% if show_tara_weight.status == 1 %} checked {% endif %}>{% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="show_tara_weight" type="checkbox" name="show_tara_weight" value="No" {% if show_tara_weight.status == 0 %}checked {% endif %}>{% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="io" type="checkbox" name="io_y" {% if io.status == 1 %} checked {% endif %}> {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="io" type="checkbox" name="io_n" {% if io.status == 0 %}checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="ew" type="checkbox" name="ew_y" {% if ew.status == 1 %} checked {% endif %}> {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="ew" type="checkbox" name="ew_n" {% if ew.status == 0 %}checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            <div class="row mt-3">
              <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
              <label><input id="dw" type="checkbox" name="dw_y" {% if dw.status == 1 %} checked {% endif %}> {% trans 'Yes' %}</label>
              </div>
              <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
              <label><input id="dw" type="checkbox" name="dw_n" {% if dw.status == 0 %} checked {% endif %}> {% trans 'No' %}</label>
              </div>
        </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="ac" type="checkbox" name="ac_y" {% if ac.status == 1 %} checked {% endif %}> {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label><input id="ac" type="checkbox" name="ac_n" {% if ac.status == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                  </div>
                  <button id="ac_camera" type='button' class='btn btn-dark'>Select</button>
            </div>
            <div class="row mt-3">
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="ds" type="checkbox" name="ds_y" {% if ds.status == 1 %} checked {% endif %}> {% trans 'Yes' %}</label>
                  </div>
                  <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="ds" type="checkbox" name="ds_n" {% if ds.status == 0 %} checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            </div>
            

            <div class="row mt-2">
                 <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="check_smtp" type="checkbox" name="check_smtp_yes" {% if form.smtp_support.value %}checked {% endif %}> {% trans 'Yes' %}</label>
                  </div>
                <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                  <label class="alignCenter"><input id="check_smtp" type="checkbox" name="check_smtp_no" {% if form.smtp_support.value|lower == 'false' %}checked {% endif %}> {% trans 'No' %}</label>
                  </div>
            <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">
                {% if form.smtp_support.value %}
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" id="smtpModal">Einstellung</button>
                {% endif %}
            </div>
             </div>
          </div>
          
            <div class="col-12 p-3">
              <button id="submit" type="submit" class="btn btn-primary"><i class="fas fa-save ml-2"></i> {% translate "Save2" %}</button>
              <a href="/adv_set" ><button type="button" class="btn btn-secondary ml-3"><i class="fas fa-wrench ml-2"></i> {% translate "Advance Settings" %}</button></a>
              <button type="button" id="weight_set" class="btn btn-secondary ml-3"><i class="fas fa-home ml-2"></i> {% translate "Weight Page Settings" %}</button>
            </div>
            
          </div>
        </form>
{#           <br>#}
{#           <br>#}
{#           <form class="form-group" id="smtp_form" method="POST">#}
{#               {% csrf_token %}#}
{#               <input type="hidden" name="id" id="id">#}
{#               <div class="row">#}
{#                   <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                       <div class="md-form mb-3">#}
{#                            <h4>SMTP Credential</h4>#}
{#                       </div>#}
{#                       <div class="md-form mb-3">#}
{#                            <label>{{smtp_form.host.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-3">#}
{#                            <label>{{smtp_form.port.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-3">#}
{#                            <label>{{smtp_form.username.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-4">#}
{#                            <label>{{smtp_form.password.label}}</label>#}
{#                       </div>#}
{#                   </div>#}
{#               <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                    <div class="row mt-3">#}
{#                    </div>#}
{#                <div class="row mt-4">#}
{#                    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='text' value="{{ smtp_form.host }}" class="col-sm-12 col-form-label" id='smtp_host' name='host' placeholder="Host">#}
{#                        {{ smtp_form.host }}#}
{#                    </div>#}
{#                </div>#}
{#                   <div class="row mt-4">#}
{#                       <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">#}
{#                        <input required type='number' class="col-sm-12 col-form-label" id='smtp_port' name='port' placeholder="Port">#}
{#                           {{ smtp_form.port }}#}
{#                    </div>#}
{#                   </div>#}
{#                <div class="row mt-4">#}
{#                   <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='text' class="col-sm-12 col-form-label" id='smtp_user' name='username' placeholder="Username">#}
{#                       {{ smtp_form.username }}#}
{#                    </div>#}
{#                </div>#}
{#                   <div class="row mt-4">#}
{#                       <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
{#                           {{ smtp_form.password }}#}
{#                    </div>#}
{#                   </div>#}
{#            </div>#}
{#                   <br>#}
{#                   <button id="test_smtp" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Test SMTP" %}</button>#}
{#                   <button id="save_smtp" type="submit" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Save SMTP" %}</button>#}
{#               </div>#}
{#           </form>#}
{#           <br>#}
{#           <br>#}
           <!--div class="custom-file md-form"-->
            <div class="">
            <form method='post' action="{% url 'logo' %}" enctype="multipart/form-data" class=" p-3"> {% csrf_token%}
              <hr/><p style="font-weight:600;"></p>
            <div class="row">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-1">
                    <input  type="file" accept='image/*' class="custom-file-input col-sm-12 col-form-label " id="customFile" name="logo">
                    <label class="custom-file-label col-sm-12 col-form-label" id="labelCustomFile" for="customFile">Bild für Logo auswählen</label>
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-3">
                      <button type="submit" name="save_logo" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Upload" %} </button>
                </div>
              </div>
            </div>
              <hr/><p style="font-weight:600;"></p>
              <div class="row">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-1">
                    <input  type="file" accept='image/*' class="custom-file-input col-sm-12 col-form-label " id="customFile" name="header">
                    <label class="custom-file-label col-sm-12 col-form-label" id="labelCustomFile" for="customFile">Bild für Rechnung auswählen</label>
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-3">
                      <button type="submit" name="save_invoice_header" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Upload" %} </button>
                </div>
              </div>
            </div>
            <hr/><p style="font-weight:600;"></p>
              <div class="row">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-1">
                    <input  type="file" accept='image/*' class="custom-file-input col-sm-12 col-form-label " id="customFile" name="footer">
                    <label class="custom-file-label col-sm-12 col-form-label" id="labelCustomFile" for="customFile">Bild für Fußzeile auswählen</label>
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-3">
                      <button type="submit" name="save_footer" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Upload" %} </button>
                </div>
              </div>
            </div>
              <hr/><p style="font-weight:600;"></p>
              <div class="row" >
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-4">
                  <textarea type='text' rows="5" class="form-control col-sm-12 col-form-label" id='heading' name='heading' placeholder="current">{% for h in heading %} {{h.heading}} {% endfor %}</textarea>
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-4">
                  <button type="submit" id="save_heading" name="save_heading" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Save" %} </button>
                </div>
              </div>
            </div>
                <div class="row" >
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-4">
                   <input  type="text" name="tele_phone_number" class="form-control" value="{{ tele_phone_number.tele_phone_number }}" placeholder="Telefonnummer">
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
                <div class="row mt-4">
                  <button type="submit" id="save_tele_phone" name="save_tele_phone" class="btn btn-primary ml-4"><i class="fas fa-save ml-2"></i> {% translate "Save" %} </button>
                </div>
              </div>
            </div>
            </form>
          </div>
            {% if messages %}
						{% for message in messages %}
            <div class=" mt-4">
              <div id="msg" class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}" role="alert">{{ message }}</div>
            </div>
						{% endfor %}
            {% endif %}                  
           </div>
          </div>
        </div>
      </div>
    <br>
    <br>
{#    <di id="smtp_div" class="card">#}
{#        <div class="row">#}
{#            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">#}
{#              <h5 class="card-header info-color white-text py-3">#}
{#                <div class="panel-heading">#}
{#                  <h4 class="panel-title">#}
{#                    <a data-toggle="collapse" data-parent="#accordion" href="#collapse_19">#}
{##}
{#                      <div class="row">#}
{#                        <div class="col-sm-12 col-12 text-left">#}
{#                          <p class="mb-0 pt-2 text-color text_color">{% translate "SMTP Form" %}</p>#}
{#                        </div>#}
{#                      </div>#}
{##}
{#                    </a>#}
{#                  </h4>#}
{#                </div>#}
{#              </h5>#}
{#            </div>#}
{#          </div>#}
{#    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12"></div>#}
{#      <div id="collapse_19" class="collapse show" >#}
{#        <di class="panel-body">#}
{#           <div class="card-body text-left">#}
{#               <form class="form-group" id="smtp_form" method="POST">#}
{#               {% csrf_token %}#}
{#               <input type="hidden" name="id" id="id">#}
{#               <div class="row">#}
{#                   <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                       <div class="md-form mb-3">#}
{#                            <h4>{% translate "SMTP Credential" %}</h4>#}
{#                       </div>#}
{#                       <div class="md-form mb-3">#}
{#                            <label>{{smtp_form.host.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-3">#}
{#                            <label>{{smtp_form.port.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-3">#}
{#                            <label>{{smtp_form.username.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-4">#}
{#                            <label>{{smtp_form.sender_address.label}}</label>#}
{#                       </div>#}
{#                       <div class="md-form mb-4">#}
{#                            <label>{{smtp_form.password.label}}</label>#}
{#                       </div>#}
{#                   </div>#}
{#               <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                    <div class="row mt-3">#}
{#                    </div>#}
{#                <div class="row mt-4">#}
{#                    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='text' value="{{ smtp_form.host }}" class="col-sm-12 col-form-label" id='smtp_host' name='host' placeholder="Host">#}
{#                        {{ smtp_form.host }}#}
{#                    </div>#}
{#                </div>#}
{#                   <div class="row mt-4">#}
{#                       <div class="col-xl-3 col-lg-3 col-md-3 col-sm-12 col-12">#}
{#                        <input required type='number' class="col-sm-12 col-form-label" id='smtp_port' name='port' placeholder="Port">#}
{#                           {{ smtp_form.port }}#}
{#                    </div>#}
{#                   </div>#}
{#                <div class="row mt-4">#}
{#                   <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='text' class="col-sm-12 col-form-label" id='smtp_user' name='username' placeholder="Username">#}
{#                       {{ smtp_form.username }}#}
{#                    </div>#}
{#                </div>#}
{#                   <div class="row mt-4">#}
{#                       <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
{#                           {{ smtp_form.sender_address }}#}
{#                    </div>#}
{#                   </div>#}
{#                   <div class="row mt-4">#}
{#                       <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">#}
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
{#                           {{ smtp_form.password }}#}
{#                    </div>#}
{#                   </div>#}
{#            </div>#}
{#                   <br>#}
{#                   <button id="test_smtp" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Test SMTP" %}</button>#}
{#                   <button id="save_smtp" type="submit" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Save SMTP" %}</button>#}
{#               </div>#}
{#           </form>#}
{#           </div>#}
{#        </di>#}
{#      </div>#}
{#    </di>#}
     <div id="MyPopup" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
   <div class="modal-dialog modal-lg">
      <!-- Modal content-->
      <div class="modal-content p-4">
         <div class="modal-header modal-header-custom">
            <h4 class="modal-title">
            </h4>
            <button type="button" class="close" data-dismiss="modal">
            &times;</button>
         </div>
         <div class="modal-body">
         </div>
         <div class="modal-footer">
            <input type="button" id="btnClosePopup" value="Schließen" class="btn btn-secondary" />
         </div>
      </div>
   </div>
</div>

<div id="weight_set_pop" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
  <div class="modal-dialog modal-lg">
     <!-- Modal content-->
     <div class="modal-content p-4">
        <div class="modal-header modal-header-custom">
           <h4 class="modal-title">
           </h4>
           <button type="button" class="close" data-dismiss="modal">
           &times;</button>
        </div>
        <div class="modal-body">
        </div>
        <div class="modal-footer">
           <input type="button" data-dismiss="modal" value="Schließen" class="btn btn-secondary" />
        </div>
     </div>
  </div>
</div>

<div id="smtpModalPop" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
  <div class="modal-dialog">
     <!-- Modal content-->
     <div class="modal-content p-4">
        <div class="modal-header modal-header-custom">
           <h4 class="modal-title">
           </h4>
           <button type="button" class="close" data-dismiss="modal">
           &times;</button>
        </div>
        <div class="modal-body">

                            <h4>{% translate "SMTP Credential" %}</h4>


             <form class="form-group" id="smtp_form" method="POST">
               {% csrf_token %}
               <input type="hidden" name="id" id="id">
               <div class="row">
                   <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
                       <div class="md-form mb-3">
                            <label>{{smtp_form.host.label}}</label>
                       </div>
                       <div class="md-form mb-3">
                            <label>{{smtp_form.port.label}}</label>
                       </div>
                       <div class="md-form mb-3">
                            <label>{{smtp_form.username.label}}</label>
                       </div>
                       <div class="md-form mb-4">
                            <label>{{smtp_form.sender_address.label}}</label>
                       </div>
                       <div class="md-form mb-4">
                            <label>{{smtp_form.password.label}}</label>
                       </div>
                   </div>
               <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12 col-12">
                    <div class="row mt-3">
                    </div>
                <div class="row">
                    <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12 col-12">
{#                        <input required type='text' value="{{ smtp_form.host }}" class="col-sm-12 col-form-label" id='smtp_host' name='host' placeholder="Host">#}
                        {{ smtp_form.host }}
                    </div>
                </div>
                   <div class="row mt-2">
                       <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12 col-12">
{#                        <input required type='number' class="col-sm-12 col-form-label" id='smtp_port' name='port' placeholder="Port">#}
                           {{ smtp_form.port }}
                    </div>
                   </div>
                <div class="row mt-3">
                   <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12 col-12">
{#                        <input required type='text' class="col-sm-12 col-form-label" id='smtp_user' name='username' placeholder="Username">#}
                       {{ smtp_form.username }}
                    </div>
                </div>
                   <div class="row mt-4">
                       <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12 col-12">
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
                           {{ smtp_form.sender_address }}
                    </div>
                   </div>
                   <div class="row mt-3">
                       <div class="col-xl-8 col-lg-8 col-md-8 col-sm-12 col-12">
{#                        <input required type='password' class="col-sm-12 col-form-label" id='smtp_pass' name='password' placeholder="Password">#}
                           {{ smtp_form.password }}
                    </div>
                   </div>
            </div>
                   <br>
                   <button id="test_smtp" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Test SMTP" %}</button>
                   <button id="save_smtp" type="submit" class="btn btn-primary ml-3"><i class="fas fa-save ml-2"></i> {% translate "Save SMTP" %}</button>
               </div>
           </form>

        </div>
        <div class="modal-footer">
           <input type="button" id="btnSmtpClosePopup" value="Schließen" class="btn btn-secondary" />
        </div>
     </div>
  </div>
</div>
{% endblock %} 

{% block scripts %}

<script type="text/javascript">

    $(document).ready(function () {

      if ($("input[name=check_yes]").prop('checked') == true){
        $("#total_camera").css('display','block');
        $("#total_camera").prop('required',true);
      }
      $('select').addClass('form-control mt-3');

      $('input:radio').removeClass('form-control').removeClass('form-radio').addClass('form-check-input');

      var radios = $('input:radio');

      var input_name = []

      for (i = 0; i < radios.length; i++) {

          radios[i].type = "checkbox";

          if(jQuery.inArray(radios[i].name, input_name)==-1){

              input_name.push(radios[i].name)

             // $("input:checkbox[name='" + radios[i].name + "']").attr({"oninvalid":"this.setCustomValidity('Please select one of these options')"});

              var x = $("[name='"+radios[i].name+"']")

              if(x.is(":checked")== true){

                x.removeAttr('required');
              }
            }
      }
      if($("#check_smtp").prop("checked")){
          $("#smtp_div").css("display","block");
      }
      else {
          $("#smtp_div").css("display","none");
      }
      if($("#ac").prop("checked")){
        $("#ac_camera").css('display','block')
      } else {
        $("#ac_camera").css('display','none')
      }
    });
    
    $("input:checkbox, input:radio").on('click', function() {

      var $box = $(this);

      if ($box.is(":checked")) {

        var group = "input:checkbox[name='" + $box.attr("name") + "']";

        $(group).prop("checked", false);

        $(group).removeAttr('required');

        $box.prop("checked", true);

      } else {

        $box.prop("checked", false);

        $("input:checkbox[name='" + $box.attr("name") + "']").attr("required",false)

      }
    });

    $('input#check').on('change', function() {
    $('input#check').not(this).prop('checked', false);
    });

    $('input#schrank').on('change', function() {
    $('input#schrank').not(this).prop('checked', false);
    });

    $('input#t_light').on('change', function() {
    $('input#t_light').not(this).prop('checked', false);
    });

    $('input#p_grp').on('change', function() {
    $('input#p_grp').not(this).prop('checked', false);
    });

    $('input#contr').on('change', function() {
    $('input#contr').not(this).prop('checked', false);
    });
    
    $('input#showt').on('change', function() {
    $('input#showt').not(this).prop('checked', false);
    });

    $('input#io').on('change', function() {
    $('input#io').not(this).prop('checked', false);
    });

    $('input#ew').on('change', function() {
    $('input#ew').not(this).prop('checked', false);
    });

    $('input#dw').on('change', function() {
    $('input#dw').not(this).prop('checked', false);
    });

    $('input#ac').on('change', function() {
    $('input#ac').not(this).prop('checked', false);
    });

    $('input#ds').on('change', function() {
    $('input#ds').not(this).prop('checked', false);
    });
    
    save_cache_smtp_cred = () =>{
       let cached_input  =  $("#cache_smtp_creds");
       let smtp_form_input = $("#smtp_creds");
       if(smtp_form_input.attr("value") != ""){
           cached_input.attr("value",smtp_form_input.attr("value"))
       }
    }

    get_cache_smtp_cred = () =>{
        let cached_input  =  $("#cache_smtp_creds");
        let smtp_form_input = $("#smtp_creds");
        if(cached_input.attr("value") != ""){
           smtp_form_input.attr("value",cached_input.attr("value"))
       }
    }

    $('input#check_smtp').on('change', function() {
        $('input#check_smtp').not(this).prop('checked', false);
        if($('input[name="check_smtp_yes"]').is(":checked")){
            $("#smtp_div").css("display","block");
            $("#smtp_supp_hidden").prop("checked","true");
            get_cache_smtp_cred()
        }
        else {
            $("#smtp_div").css("display","none");
            $("#smtp_supp_hidden").prop("checked",false);
            save_cache_smtp_cred();
            $("#smtp_creds").attr("value","")
        }
    });

    const postTestSMTP = async (form_data) =>{
        let url = "/test_smtp/";
        form_data = {'csrfmiddlewaretoken': '{{ csrf_token }}',...form_data}
        delete form_data["name"];
        delete form_data["value"]
        let response = await $.ajax({
                type: "POST",
                url:url,
                data: form_data
            }
        );
        return response
    }

    const postSaveSMTP = async (form_data) =>{
        let url = "/save_smtp/";
        form_data = {'csrfmiddlewaretoken': '{{ csrf_token }}',...form_data}
        delete form_data["name"];
        delete form_data["value"]
        let response = await $.ajax({
                type: "POST",
                url:url,
                data: form_data
            }
        );
        return response
    }

    $("#test_smtp").on('click', (e) =>{
        $("#smtp_form").validate().destroy();
        $("#smtp_form").validate({
            rules:{
                host: "required",
                port: "required",
                user: "required",
                pass: "required",
                sender_address:"required"
            },
            messages:{
                host:"Host Required"
            },
            submitHandler: function (form,event) {
                {#var data =Object.fromEntries(new FormData(form))#}
                event.preventDefault();
                let data = $(form).serializeArray().reduce((o,p) => ({...o, [p.name]: p.value}))
                postTestSMTP(data).then(response =>{
                    alert("Email Successfully sent to User")
                }).catch(err=>{
                    alert(err.responseText)
                });
                return false;
            }
        });
    });

    $("#save_smtp").on('click', (e) =>{
        $("#smtp_form").validate().destroy();
        $("#smtp_form").validate({
            rules:{
                host: "required",
                port: "required",
                user: "required",
                pass: "required",
                sender_address:"required"
            },
            messages:{
                host:"Host Required"
            },
            submitHandler: async function (form,event) {
                {#var data =Object.fromEntries(new FormData(form))#}
                event.preventDefault();
                let data = $(form).serializeArray().reduce((o,p) => ({...o, [p.name]: p.value}))
                await postSaveSMTP(data).then(response => {
                    alert("SMTP Credentials Successfully created.");
                    $("#smtp_creds").attr("value",response.id);
                });
                return false;
            }
        });
    });

    $("#save_heading").click(function(){
      head = $("#heading").val();
      if (head === ''){
        if (!confirm("Do you want to clear Heading!!")){
          return false;
        }
      }
    })

    $('#customFile').change(function () {
    $("#labelCustomFile").text(this.value);
  });

  $('input').change(function(){
    if ($("[name=check_yes]").prop('checked') == true){
      $("#total_camera").css('display','block');
      $("#total_camera").prop('required',true);
    } else {
      $("#total_camera").css('display','none');
      $("#total_camera").prop('required',false);
    }

    // if ($("input[name=schrank_yes").prop('checked') == true){
    //   $("#total_shrank").css('display','block');
    //   $("#total_shrank").prop('required','true');
    // } else {
    //   $("#total_shrank").css('display','none');
    //   $("#total_shrank").prop('required','false');
    // }


  })
  
$("#ac_camera").click(function(){
  var title = "Wählen Sie die Kamera für die automatische Aufnahme";
  // var body = "Material list";
  $("#MyPopup .modal-title").html(title);
 
  $("#btnClosePopup").click(function () {
      $("#MyPopup").modal("hide");
  });
   $("#MyPopup .modal-body").load("/auto_capture");
$("#MyPopup").modal();
});

$("#weight_set").click(function(){
  var title = "Willkommen bei den Einstellungen für die Gewichtsseite";
  // var body = "Material list";
  $("#weight_set_pop .modal-title").html(title);
 
  $("#btnClosePopup").click(function () {
      $("#weight_set_pop").modal("hide");
  });
   $("#weight_set_pop .modal-body").load("/weight_page_set");
$("#weight_set_pop").modal();
});

$("#smtpModal").click(function (){
    $("#smtpModalPop").modal();
      $("#btnSmtpClosePopup").click(function () {
      $("#smtpModalPop").modal("hide");
  });
})

</script>

{% endblock %}


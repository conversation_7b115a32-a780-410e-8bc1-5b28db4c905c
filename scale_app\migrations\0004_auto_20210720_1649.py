# Generated by Django 3.1.1 on 2021-07-20 16:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('scale_app', '0003_auto_20210701_0819'),
    ]

    operations = [
        migrations.AddField(
            model_name='devices',
            name='device_type',
            field=models.CharField(choices=[('Scale', 'Scale'), ('Camera', 'Camera'), ('Door', 'Door'), ('Display', 'Display')], default='Scale', max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='devices',
            name='is_simulation',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='devices',
            name='mac_addr',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]

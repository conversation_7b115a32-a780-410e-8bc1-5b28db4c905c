import base64
import json
import math
import os
import re
from django.core.paginator import Paginator
from django.core import serializers
from datetime import datetime
from io import BytesIO
from urllib import parse as urlparse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.files.base import ContentFile
from django.core.serializers import serialize
from django.db.models import Sum
from django.forms import model_to_dict
from django.http import HttpResponseRedirect, JsonResponse, HttpResponse, QueryDict
from django.shortcuts import get_object_or_404, render, redirect
from django.template.loader import get_template
from django.urls import reverse
from django.views import View
from django.views.decorators.csrf import csrf_exempt

from yard.forms import WarehouseForm, SmtpForm, SettingsForm, VehicleForm, \
    CustomerForm, ForwardersForm, SupplierForm, \
    ContractForm, CombinationForm

from yard.models import DeductionShow, HandTransmitterSetting, Logo, ShowInvoice, Vehicle, Forwarders, Signature, \
    Combination, \
    Warehouse, SMTPCred, SelectCamera, Barrier, Settings, \
    Customer, Supplier, TrafficLight, PriceGroup, ContainerShow, \
    ShowTonne, Io, ExternalWeigh, ForeignWeigh, Contract, Article, \
    BuildingSite, AutoCapture, FirstWeightCameras, SecondWeightCameras, \
    Transaction, IDtext, DriverSignEnable, DeliveryNoteTemplate, \
    WeightPageSetting, ShowTaraWeight, PlaceOfDelivery, CompanyTelePhone, \
    IdDeliveryNoteOption, ApiDeliveryNoteOption, SelectWaage, MasterContainer

from django.contrib import messages
from yard.utils.view_handling import yard_check, set_settings_session, user_role, generate_qr_code
from .render import Render
from yard.utils.smtp_ops import test_smtp


def schrank(request):
    return render(request, "yard/schrank.html")


def copy(request):
    if 'down' in request.POST:
        file = open('Copyright.pdf', 'rb')
        response = HttpResponse(file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Urheberrecht.pdf"'
        return response
    return render(request, "yard/copyright.html")


# API for loading details from ajax to editform
@login_required(redirect_field_name=None)
@user_passes_test(yard_check)
def combination(request, identifier):
    try:
        obj = Combination.objects.get(ident=identifier)

    except:
        obj = None
    if obj:
        data = model_to_dict(obj)
    # data.update(foreign_data)
    else:
        data = {}
    return JsonResponse(data)


""" Customer Views """


@login_required
@user_passes_test(yard_check)
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def customer(request):
    context = {}
    context["master_container"] = MasterContainer.objects.last()
    form = CustomerForm(request.POST or None)
    if request.POST:
        idd = request.POST.get('id')
        try:
            obj = Customer.objects.get(id=idd)
        except:
            obj = None
        if obj == None:
            if form.is_valid():
                form.save()
                form = CustomerForm(None)
            context['form'] = form
            context["dataset"] = Customer.objects.all()
            return render(request, "yard/customer-2.html", context)
        # to handle update TBD
        form = CustomerForm(request.POST, instance=obj)
        print(form.is_valid())
        print(form.errors)
        if form.is_valid():
            form.save()
            form = CustomerForm(None)
        context["form"] = form
        context["dataset"] = Customer.objects.all()
        return render(request, "yard/customer-2.html", context)
    else:
        context["form"] = form
        context["dataset"] = Customer.objects.all()
        return render(request, "yard/customer-2.html", context)


# API for loading details from ajax to editform
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def customer_details(request, identifier):
    try:
        obj = Customer.objects.get(id=int(identifier))
    except Exception as e:
        print(e)
        obj = None
    if obj:
        data = model_to_dict(obj)
        del data["ss_role_access"]
    else:
        data = {}
    return JsonResponse(data)


def customer_contract(request, identifier):
    try:
        obj = Customer.objects.get(id=int(identifier))
    except Exception as e:
        print(e)
        obj = None
    if obj:
        data = Contract.objects.filter(customer=obj)
        serialized_data = serializers.serialize('json', data)
    else:
        serialized_data = []
    return JsonResponse(serialized_data, safe=False)


def customer_transactions(request, identifier):
    try:
        obj = Customer.objects.get(id=int(identifier))
    except Exception as e:
        print(e)
        obj = None
    if obj:
        data = Transaction.objects.filter(customer=obj)
        serialized_data = serializers.serialize('json', data)
    else:
        serialized_data = []
    return JsonResponse(serialized_data, safe=False)


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def customer_delete(request, identifier):
    context = {}
    obj = get_object_or_404(Customer, id=identifier)
    obj.delete()
    return HttpResponseRedirect("/customer")


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def customer_list(request):
    # if request.is_ajax():
    queryset = json.loads(serialize('json', Customer.objects.all()))
    list = []
    data = {
        'list': queryset,
    }
    return JsonResponse(data)


"""  vehicle data view """


@login_required(redirect_field_name=None)  # @login_required
@user_passes_test(yard_check)
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def vehicle(request):
    context = {}
    context["master_container"] = MasterContainer.objects.last()
    form = VehicleForm(request.POST or None)
    # vehicle_type = request.GET.get("vehicle_type")
    # if "vehicle_type" not in request.GET or vehicle_type == "all":
    #     context["dataset"] = Vehicle.objects.all()  
    # else:
    #     context["dataset"] = Vehicle.objects.filter(fahrzeugtypen=vehicle_type)

    if request.POST:
        id = request.POST.get('id')
        try:
            obj = Vehicle.objects.get(id=id)
        except:
            obj = None
        if obj == None:
            if form.is_valid():
                form.save()
                form = VehicleForm(None)
            else:
                print("Error in saving vehicle:", form.errors)
            context['form'] = form
            context["dataset"] = Vehicle.objects.all()
            return render(request, "yard/vehicle2.html", context)
        # to handle update TBD
        form = VehicleForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            form = VehicleForm(None)
        else:
            print("Error in updating vehicle:", form.errors)
        context["form"] = form
        context["dataset"] = Vehicle.objects.all()
        return render(request, "yard/vehicle2.html", context)
    else:
        context["form"] = form
        context["dataset"] = Vehicle.objects.all()
        return render(request, "yard/vehicle2.html", context)


# API for loading details from ajax to editform
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def vehicle_detail(request, identifier):
    try:
        obj = Vehicle.objects.get(id=identifier)
    except:
        obj = None
    if obj:
        vehicle_weight_date = {"updated_date_time": obj.updated_date_time}
        data = model_to_dict(obj)
        del data["ss_role_access"]
        data.update(vehicle_weight_date)
    else:
        data = {}
    return JsonResponse(data)


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def vehicle_delete(request, identifier):
    context = {}
    obj = get_object_or_404(Vehicle, id=identifier)
    obj.delete()
    return HttpResponseRedirect("/vehicle")


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def vehicle_list(request):
    # if request.is_ajax():
    queryset = json.loads(serialize('json', Vehicle.objects.all()))
    list = []
    data = {
        'list': queryset,
    }
    return JsonResponse(data)


""" Forwarders View """


@login_required
@user_passes_test(yard_check)
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def forwarders(request):
    context = {}
    form = ForwardersForm(request.POST or None)

    if request.POST:
        idd = request.POST.get('id')
        try:
            obj = Forwarders.objects.get(id=idd)
        except:
            obj = None
        if obj == None:
            if form.is_valid():
                form.save()
                form = ForwardersForm(None)
            else:
                print("Form not valid")
            context['form'] = form
            context["dataset"] = Forwarders.objects.all()
            return render(request, "yard/forwarders2.html", context)
        # to handle update TBD
        form = ForwardersForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            form = ForwardersForm(None)
        context["form"] = form
        context["dataset"] = Forwarders.objects.all()
        return render(request, "yard/forwarders2.html", context)
    else:
        context["form"] = form
        context["dataset"] = Forwarders.objects.all()
        return render(request, "yard/forwarders2.html", context)


# API for loading details from ajax to editform
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def forwarders_detail(request, identifier):
    try:
        obj = Forwarders.objects.get(id=identifier)
    except:
        obj = None
    if obj:
        data = model_to_dict(obj)
    else:
        data = {}
    return JsonResponse(data)


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def forwarders_delete(request, identifier):
    context = {}
    obj = get_object_or_404(Forwarders, id=identifier)
    obj.delete()
    return HttpResponseRedirect("/forwarders")


""" Supplier View """


# @staff_member_required
@login_required
@user_passes_test(yard_check)
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def supplier(request):
    context = {}
    context["master_container"] = MasterContainer.objects.last()
    form = SupplierForm(request.POST or None)

    if request.POST:
        idd = request.POST.get('id')
        try:
            obj = Supplier.objects.get(id=idd)
        except:
            obj = None
        if obj == None:
            if form.is_valid():
                form.save()
                form = SupplierForm(None)
            context['form'] = form
            context["dataset"] = Supplier.objects.all()
            return render(request, "yard/supplier2.html", context)
        # to handle update TBD
        form = SupplierForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            form = SupplierForm(None)
        context["form"] = form
        context["dataset"] = Supplier.objects.all()
        return render(request, "yard/supplier2.html", context)
    else:
        context["form"] = form
        context["dataset"] = Supplier.objects.all()
        return render(request, "yard/supplier2.html", context)


# API for loading details from ajax to editform
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def supplier_detail(request, identifier):
    try:
        obj = Supplier.objects.get(id=identifier)
    except:
        obj = None
    if obj:
        data = model_to_dict(obj)
        del data["ss_role_access"]

    else:
        data = {}
    return JsonResponse(data)


def place_of_delivery_detail(request, identifier):
    try:
        obj = PlaceOfDelivery.objects.get(id=identifier)
    except:
        obj = None
    if obj:
        data = model_to_dict(obj)
        del data["ss_role_access"]

    else:
        data = {}
    return JsonResponse(data)


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def supplier_delete(request, identifier):
    context = {}
    obj = get_object_or_404(Supplier, id=identifier)
    obj.delete()
    return HttpResponseRedirect("/supplier")


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def supplier_list(request):
    # if request.is_ajax():
    queryset = json.loads(serialize('json', Supplier.objects.all()))
    list = []
    data = {
        'list': queryset,
    }
    return JsonResponse(data)


def logo(request):
    if request.POST:
        try:
            model = Logo.objects.get(id=1)
        except:
            model = None
        if "save_invoice_header" in request.POST:
            img = request.FILES.get('header')
            if img is not None:
                ext = img.name.split('.')[-1].lower()
            else:
                messages.error(request, "There is no Header to Update")
                return HttpResponseRedirect('/settings')
            if ext != 'png' and ext != 'jpg' and ext != 'jpeg':
                messages.error(
                    request, "Please Upload a valid png or jpg file")
                return HttpResponseRedirect('/settings')
            if model is not None:
                model.invoice_header = img
                model.save()
                messages.success(request, "Kopfzeile aktualisiert.")
            else:
                logo = Logo(invoice_header=img)
                logo.save()
                messages.success(request, "Data Added")
        if "save_footer" in request.POST:
            img = request.FILES.get('footer')
            if img is not None:
                ext = img.name.split('.')[-1].lower()
            else:
                messages.error(request, "There is no Footer to Update")
                return HttpResponseRedirect('/settings')
            if ext != 'png' and ext != 'jpg' and ext != 'jpeg':
                messages.error(
                    request, "Please Upload a valid png or jpg file")
                return HttpResponseRedirect('/settings')
            if model is not None:
                model.footer = img
                model.save()
                messages.success(request, "Fußzeile aktualisiert.")
            else:
                logo = Logo(footer=img)
                logo.save()
                messages.success(request, "Data Added")
        if "save_logo" in request.POST:
            img = request.FILES.get('logo')
            if img is not None:
                ext = img.name.split('.')[-1].lower()
            else:
                messages.error(request, "There is no Logo to Update")
                return HttpResponseRedirect('/settings')
            if ext != 'png' and ext != 'jpg' and ext != 'jpeg':
                messages.error(
                    request, "Please Upload a valid png or jpg file")
                return HttpResponseRedirect('/settings')
            if model is not None:
                model.logo = img
                model.save()
                messages.success(request, "Logo aktualisiert")
            else:
                logo = Logo(logo=img)
                logo.save()
                messages.success(request, "Data Added")
        if "save_heading" in request.POST:
            head = request.POST.get('heading')
            if head is not None:
                if model is not None:
                    model.heading = head.lstrip()
                    model.save()
                    messages.success(request, "Anschrift aktualisiert")
                else:
                    logo = Logo(heading=head)
                    logo.save()
                    messages.success(request, "neue Anschrift hinzugefügt!")
            else:
                messages.error(request, "No Heading to update")
        if request.POST.get('tele_phone_number') is not None:
            CompanyTelePhone.objects.all().delete()
            p = CompanyTelePhone(tele_phone_number=request.POST.get('tele_phone_number'))
            p.save()

    return HttpResponseRedirect('/settings')


def vehicle_save(request):
    licen_plt = request.GET.get('licence_plate')
    licen_plt2 = request.GET.get('licence_plate2')
    weight = request.GET.get('weight')
    forwarder = request.GET.get('forwarder')
    albi_nr = request.GET.get('albi_nr')
    try:
        try:
            if re.match('^[0-9]*$', licen_plt):
                try:
                    vehicle = Vehicle.objects.get(id=licen_plt)
                except:
                    vehicle = Vehicle.objects.get(license_plate=licen_plt)
            else:
                vehicle = Vehicle.objects.get(license_plate=licen_plt)
        except:
            vehicle = Vehicle.objects.create(license_plate=licen_plt)
        try:
            vehicle.forwarder = Forwarders.objects.get(id=forwarder)
        except:
            pass

        from datetime import datetime
        nowd = datetime.now()
        date = nowd.strftime('%Y-%m-%d')
        time = nowd.strftime('%H:%M')
        vehicle.vehicle_weight_date = date
        vehicle.vehicle_weight_time = time
        vehicle.vehicle_weight = weight
        vehicle.license_plate2 = licen_plt2
        vehicle.vehicle_weight_id = albi_nr
        vehicle.save()
        return JsonResponse({'status': 1})
    except Exception as e:
        print(e)
        return JsonResponse({"status": 0})


def sign(request):
    if request.POST:
        sign = request.FILES.get('signature')
        if sign is None:
            messages.error(request, "No signature to save")
            return HttpResponseRedirect('/user_edit')
        try:
            obj, created = Signature.objects.update_or_create(
                user=request.user, signature=sign)
            obj.save()
            messages.success(request, "Signature Saved")
        except:
            messages.error(request, "Error occurred")

        return HttpResponseRedirect('/user_edit')
    else:
        return HttpResponseRedirect('/user_edit')


def comb_list(request):
    context = {}
    if request.POST:
        _id = request.POST.get('id')
        if _id is not None and _id != '':
            obj = Combination.objects.get(id=_id)
            print("obj", obj)
            form = CombinationForm(data=request.POST, instance=obj)
            if form.is_valid():
                form.save()
        else:
            form = CombinationForm(request.POST)
            if form.is_valid():
                form.save()
        return HttpResponseRedirect('/comb_list')
    form = CombinationForm()
    context["form"] = form
    context["data"] = Combination.objects.all()
    context["contracts"] = Contract.objects.all()
    return render(request, "yard/id.html", context)


def comb_match(request):
    val = request.GET.get('val')
    try:
        obj = Combination.objects.get(ident=val)
    except:
        obj = None
    if obj is not None:
        data = 1
    else:
        data = 0
    return JsonResponse({'data': data})


def comb_delete(request, identifier):
    obj = get_object_or_404(Combination, id=identifier)
    obj.delete()
    return HttpResponseRedirect("/comb_list")


def advance_set(request):
    if 'clear' in request.POST:
        request.session['perm'] = False

    code = request.session.get('code')
    permission = request.session.get('perm', False)
    password = request.POST.get('login_password')

    if not request.POST and permission == False:
        import random
        lst = []
        for i in range(8):
            lst.append(random.randint(2, 9))
        request.session['code'] = "".join([str(x) for x in lst])

    if code is not None:
        from datetime import date
        passcode = date.today().strftime('%d%m%Y')

    if password is not None and password != '':
        if password == passcode:
            request.session['perm'] = True
        else:
            request.session['perm'] = False
            messages.error(request, "Enter Password")

    if request.session.get('perm'):
        context = {}
        try:
            if request.POST.get('id_del_note_1') is not None:
                IdDeliveryNoteOption.objects.all().delete()
                id_del_note = IdDeliveryNoteOption(option=1)
                id_del_note.save()
            elif request.POST.get('id_del_note_2') is not None:
                IdDeliveryNoteOption.objects.all().delete()
                id_del_note = IdDeliveryNoteOption(option=2)
                id_del_note.save()

            if request.POST.get('id_external_del_note_1') is not None:
                ApiDeliveryNoteOption.objects.all().delete()
                id_del_note = ApiDeliveryNoteOption(option=1)
                id_del_note.save()
            elif request.POST.get('id_external_del_note_2') is not None:
                ApiDeliveryNoteOption.objects.all().delete()
                id_del_note = ApiDeliveryNoteOption(option=2)
                id_del_note.save()
            elif request.POST.get('id_external_del_note_3') is not None:
                ApiDeliveryNoteOption.objects.all().delete()
                id_del_note = ApiDeliveryNoteOption(option=3)
                id_del_note.save()
            elif request.POST.get('id_external_del_note_4') is not None:
                ApiDeliveryNoteOption.objects.all().delete()
                id_del_note = ApiDeliveryNoteOption(option=4)
                id_del_note.save()

            if request.POST.get('del_note_2') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=2)
                del_note.save()
            elif request.POST.get('del_note_3') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=3)
                del_note.save()
            elif request.POST.get('del_note_4') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=4)
                del_note.save()
            elif request.POST.get('del_note_5') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=5)
                del_note.save()
            elif request.POST.get('del_note_6') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=6)
                del_note.save()
            elif request.POST.get('del_note_7') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=7)
                del_note.save()
            elif request.POST.get('del_note_8') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=8)
                del_note.save()
            elif request.POST.get('del_note_9') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=9)
                del_note.save()
            elif request.POST.get('del_note_10') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=10)
                del_note.save()
            elif request.POST.get('del_note_11') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=11)
                del_note.save()
            elif request.POST.get('del_note_12') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=12)
                del_note.save()
            elif request.POST.get('del_note_13') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=13)
                del_note.save()
            elif request.POST.get('del_note_14') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=14)
                del_note.save()
            elif request.POST.get('del_note_1') is not None:
                DeliveryNoteTemplate.objects.all().delete()
                del_note = DeliveryNoteTemplate(option=1)
                del_note.save()
            if request.POST.get('check_yes') is not None:
                SelectCamera.objects.all().delete()
                camera = SelectCamera(yes=True, number=int(
                    request.POST.get('total_camera')))
                camera.save()
            if request.POST.get('check_No') is not None:
                SelectCamera.objects.all().delete()
                camera = SelectCamera(yes=False)
                camera.save()
            if request.POST.get('schrank_yes') is not None:
                Barrier.objects.all().delete()
                bar = Barrier(barrier=True)
                bar.save()
            if request.POST.get('schrank_no') is not None:
                Barrier.objects.all().delete()
                bar = Barrier(barrier=False)
                bar.save()
            if request.POST.get('handsender_yes') is not None:
                HandTransmitterSetting.objects.all().delete()
                HandTransmitterSetting.objects.create(status=True)
            if request.POST.get('handsender_no') is not None:
                HandTransmitterSetting.objects.all().delete()
                HandTransmitterSetting.objects.create(status=False)
            if request.POST.get('tl_yes') is not None:
                TrafficLight.objects.all().delete()
                tl = TrafficLight(status=True)
                tl.save()
            if request.POST.get('tl_no') is not None:
                TrafficLight.objects.all().delete()
                tl = TrafficLight(status=False)
                tl.save()
            if request.POST.get('si_yes') is not None:
                ShowInvoice.objects.all().delete()
                si = ShowInvoice(status=True)
                si.save()
            if request.POST.get('si_no') is not None:
                ShowInvoice.objects.all().delete()
                si = ShowInvoice(status=False)
                si.save()
            if request.POST.get('master_container_yes') is not None:
                MasterContainer.objects.all().delete()
                mc = MasterContainer(status=True)
                mc.save()
            if request.POST.get('master_container_no') is not None:
                MasterContainer.objects.all().delete()
                mc = MasterContainer(status=False)
                mc.save()
            if request.POST.get('check_waage_yes') is not None:
                waage, created = SelectWaage.objects.get_or_create(id=1)
                waage.yes = True
                waage.number = int(request.POST.get('total_waage'))
                waage.save()
            if request.POST.get('check_waage_no') is not None:
                waage, created = SelectWaage.objects.get_or_create(id=1)
                waage.yes = False
                waage.save()
        except Exception as e:
            print(e)
            pass
        context["camera"] = SelectCamera.objects.all().last()
        context["waage"] = SelectWaage.objects.all().last()

        context["barr"] = Barrier.objects.all().last()
        context["si"] = ShowInvoice.objects.all().last()
        context["ampel"] = TrafficLight.objects.all().last()
        context['dt'] = DeliveryNoteTemplate.objects.all().last()
        context['id_dt'] = IdDeliveryNoteOption.objects.all().last()
        context['external_id_dt'] = ApiDeliveryNoteOption.objects.all().last()
        context['handsender'] = HandTransmitterSetting.objects.first()
        context['master_container'] = MasterContainer.objects.first()

        return render(request, "yard/settings_advance.html", context)
    else:
        return render(request, "yard/perm.html")


@login_required(redirect_field_name=None)
@user_passes_test(yard_check)
@user_passes_test(user_role)
def settings(request):
    context = {}
    try:
        if request.POST.get('p_grp_y') is not None:
            PriceGroup.objects.all().delete()
            p = PriceGroup(status=True)
            p.save()
        if request.POST.get('p_grp_n') is not None:
            PriceGroup.objects.all().delete()
            p = PriceGroup(status=False)
            p.save()
        if request.POST.get('contr_y') is not None:
            ContainerShow.objects.all().delete()
            c = ContainerShow(status=True)
            c.save()
        if request.POST.get('contr_n') is not None:
            ContainerShow.objects.all().delete()
            c = ContainerShow(status=False)
            c.save()
        if request.POST.get('show_ty') is not None:
            ShowTonne.objects.all().delete()
            T = ShowTonne(status=True)
            T.save()
        if request.POST.get('show_tn') is not None:
            ShowTonne.objects.all().delete()
            T = ShowTonne(status=False)
            T.save()
        if request.POST.get('io_y') is not None:
            Io.objects.all().delete()
            I = Io(status=True)
            I.save()
        if request.POST.get('io_n') is not None:
            Io.objects.all().delete()
            I = Io(status=False)
            I.save()
        if request.POST.get('ew_y') is not None:
            ExternalWeigh.objects.all().delete()
            E = ExternalWeigh(status=True)
            E.save()
        if request.POST.get('ew_n') is not None:
            ExternalWeigh.objects.all().delete()
            E = ExternalWeigh(status=False)
            E.save()
        if request.POST.get('ac_y') is not None:
            AutoCapture.objects.all().delete()
            A = AutoCapture(status=True)
            A.save()
        if request.POST.get('ac_n') is not None:
            AutoCapture.objects.all().delete()
            A = AutoCapture(status=False)
            A.save()
        if request.POST.get('ds_y') is not None:
            DriverSignEnable.objects.all().delete()
            D = DriverSignEnable(status=True)
            D.save()
        if request.POST.get('ds_n') is not None:
            DriverSignEnable.objects.all().delete()
            D = DriverSignEnable(status=False)
            D.save()

        if request.POST.get('show_tara_weight') is not None:
            ShowTaraWeight.objects.all().delete()
            if request.POST.get('show_tara_weight') == 'Yes':
                D = ShowTaraWeight(status=True)
                D.save()
            else:
                D = ShowTaraWeight(status=False)
                D.save()

        if request.POST.get('dw_y') is not None:
            DeductionShow.objects.all().delete()
            d = DeductionShow(status=True)
            d.save()
        if request.POST.get('dw_n') is not None:
            DeductionShow.objects.all().delete()
            d = DeductionShow(status=False)
            d.save()
    except Exception as e:
        print(e)
        pass
    try:
        obj = Settings.objects.all()[0]
    except Exception as e:
        obj = None
    if obj:
        form = SettingsForm(request.POST or None, instance=obj)
    else:
        form = SettingsForm(request.POST or None)
    if request.POST:
        if form.is_valid():
            obj = form.instance
            obj.yard = request.user.yard
            smt_creds = SMTPCred.objects.get(id=request.POST["smtp_creds"]) if request.POST[
                                                                                   "smtp_creds"] != "" else None
            if smt_creds:
                obj.smtp_creds = smt_creds
            obj.save()
            form.save()
        else:
            return HttpResponse(form.errors, status=400)
    if obj:
        context["smtp_form"] = SmtpForm(instance=obj.smtp_creds)
    else:
        context["smtp_form"] = SmtpForm()
    set_settings_session(request)
    context["form"] = form
    context["heading"] = Logo.objects.all()
    context['price'] = PriceGroup.objects.all().last()
    context['contr'] = ContainerShow.objects.all().last()
    context['show_t'] = ShowTonne.objects.all().last()
    context['io'] = Io.objects.all().last()
    context['ew'] = ExternalWeigh.objects.all().last()
    context['ac'] = AutoCapture.objects.all().last()
    context['ds'] = DriverSignEnable.objects.all().last()
    context['tele_phone_number'] = CompanyTelePhone.objects.all().last()
    context['show_tara_weight'] = ShowTaraWeight.objects.all().last()
    return render(request, "yard/settings2.html", context)


@login_required(redirect_field_name=None)
@user_passes_test(yard_check)
def test_smtp_connection(request):
    if request.method == "POST":
        print(request.POST)
        try:
            test_smtp(request.POST)
        except Exception as e:
            return HttpResponse(str(e), status=400)
        return HttpResponse("Good")
    return HttpResponse(status=404)


@login_required(redirect_field_name=None)
@user_passes_test(yard_check)
def save_smtp_connection(request):
    if request.method == "POST":
        obj = SMTPCred.objects.first()
        obj_set = Settings.objects.all()[0]
        if obj:
            form = SmtpForm(request.POST, instance=obj)
        else:
            form = SmtpForm(request.POST or None)
        if form.is_valid():
            obj = form.save()
            obj_set.smtp_creds = obj
            obj_set.save()
            return JsonResponse(model_to_dict(obj), status=200)
        else:
            return HttpResponse(form.errors, status=400)
    return HttpResponse(status=404)


def e_sign(request):
    if request.POST:
        data = request.POST.get('signature')
        format, imgstr = data.split(';base64,')
        ext = format.split('/')[-1]
        sign = ContentFile(base64.b64decode(imgstr), name='temp.' + ext)
        if sign is None:
            messages.error(request, "No signature to save")
            return HttpResponseRedirect('/user_edit')
        try:
            obj, created = Signature.objects.update_or_create(
                user=request.user, signature=sign)
            obj.save()
            messages.success(request, "Signature Saved")
        except:
            messages.error(request, "Error occurred")

        return HttpResponseRedirect('/user_edit')

    return render(request, "yard/e_sign.html")


def auto_capture(request):
    if request.POST:
        fw = {'cam_1': False, 'cam_2': False, 'cam_3': False}
        sw = {'cam_1': False, 'cam_2': False, 'cam_3': False}

        if request.POST.get('fw_cam_1') is not None:
            fw['cam_1'] = True
        if request.POST.get('fw_cam_2') is not None:
            fw['cam_2'] = True
        if request.POST.get('fw_cam_3') is not None:
            fw['cam_3'] = True
        if request.POST.get('sw_cam_1') is not None:
            sw['cam_1'] = True
        if request.POST.get('sw_cam_2') is not None:
            sw['cam_2'] = True
        if request.POST.get('sw_cam_3') is not None:
            sw['cam_3'] = True

        fw_cam_1 = request.POST.get('fw_cam_1')
        fw_cam_2 = request.POST.get('fw_cam_2')
        fw_cam_3 = request.POST.get('fw_cam_3')
        sw_cam_1 = request.POST.get('sw_cam_1')
        sw_cam_2 = request.POST.get('sw_cam_2')
        sw_cam_3 = request.POST.get('sw_cam_3')

        if fw_cam_1 is not None or fw_cam_2 is not None or fw_cam_3 is not None:
            FirstWeightCameras.objects.all().delete()
            F = FirstWeightCameras(
                cam1=fw['cam_1'], cam2=fw['cam_2'], cam3=fw['cam_3'])
            F.save()
        else:
            FirstWeightCameras.objects.all().delete()
            F = FirstWeightCameras(
                cam1=False, cam2=False, cam3=False)
            F.save()

        if sw_cam_1 is not None or sw_cam_2 is not None or sw_cam_3 is not None:
            SecondWeightCameras.objects.all().delete()
            S = SecondWeightCameras(
                cam1=sw['cam_1'], cam2=sw['cam_2'], cam3=sw['cam_3'])
            S.save()
        else:
            SecondWeightCameras.objects.all().delete()
            S = SecondWeightCameras(
                cam1=False, cam2=False, cam3=False)
            S.save()

        return redirect('/settings')

    context = {}
    context['fw'] = FirstWeightCameras.objects.all().last()
    context['sw'] = SecondWeightCameras.objects.all().last()
    return render(request, "yard/automatic_capture.html", context)


@login_required
@user_passes_test(yard_check)
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def warehouse(request):
    context = {}
    form = WarehouseForm(request.POST or None)

    if request.POST:
        id = request.POST.get('id')
        try:
            obj = Warehouse.objects.get(id=id)
        except:
            obj = None
        if obj == None:
            if form.is_valid():
                form.save()
                form = WarehouseForm(None)
            context['form'] = form
            context["dataset"] = Warehouse.objects.all()
            # context["art_meta"] = Article_meta.objects.all()
            return render(request, "yard/warehouse.html", context)
        # to handle update TBD
        form = WarehouseForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            form = WarehouseForm(None)
        context["form"] = form
        context["dataset"] = Warehouse.objects.all()
        return render(request, "yard/warehouse.html", context)
    else:
        context["form"] = form
        context["dataset"] = Warehouse.objects.all()
        return render(request, "yard/warehouse.html", context)


@login_required
def warehouse_detail(request, identifier):
    try:
        obj = Warehouse.objects.get(id=identifier)
    except:
        obj = None
    if obj:
        data = model_to_dict(obj)
    else:
        data = {}
    return JsonResponse(data)


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def warehouse_delete(request, identifier):
    context = {}
    obj = get_object_or_404(Warehouse, id=identifier)
    obj.delete()
    return HttpResponseRedirect("/warehouse")


@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def warehouse_list(request):
    # if request.is_ajax():
    # queryset = json.loads(serialize('json', Article.objects.all()))
    queryset = json.loads(serialize('json', Warehouse.objects.all()))
    list = []
    data = {
        'list': queryset,
    }
    return JsonResponse(data)


def foreign_list(request, id=None):
    if id is not None:
        obj = get_object_or_404(ForeignWeigh, id=id)
        obj.delete()
        return HttpResponseRedirect("/foreign_list")
    else:
        context = {}
        context['dataset'] = ForeignWeigh.objects.all()
        return render(request, "yard/foreign.html", context)


# Contract View

def list_contracts(request):
    if request.method == "GET":
        contracts = Contract.objects.all()
        ctr_list = []
        for obj in contracts:
            material_list = []
            for mat in obj.required_materials:
                article = Article.objects.get(id=mat["material"])
                article_obj = {
                    "id": article.id,
                    "name": article.name
                }
                materials_obj = {
                    "material": article_obj,
                    "agreed_value": int(mat["agreed_value"]) if mat["agreed_value"] else None,
                    "remaining": int(mat["agreed_value"]) - sum(
                        Transaction.objects.filter(
                            article_id=mat["material"], customer=obj.customer, contract_number=obj.contract_number)
                            .values_list("net_weight", flat=True)) if mat["agreed_value"] else None
                }
                material_list.append(materials_obj)
            ctr = {
                "contract_number": obj.contract_number,
                "customer": {"name": obj.customer.name1, "id": obj.customer.id},
                "start_date": obj.start_date,
                "end_date": obj.end_date,
                "forwarders": obj.forwarders_id,
                "reserved_date": obj.reserved_date,
                "price_group": obj.price_group,
                "required_materials": material_list,
                "vehicles": [{"id": i[0], "license_plate": i[1]} for i in
                             obj.vehicles.all().values_list("id", "license_plate")],
                "supplier": [{"id": supp.id, "name": supp.name} for supp in
                             obj.supplier.all()] if obj.supplier else None
            }
            ctr_list.append(ctr)
        return JsonResponse(ctr_list, safe=False)
    else:
        return HttpResponse(status=405)


def get_delete_contract(request, contract_number):
    if request.method == "GET":
        try:
            obj = Contract.objects.get(contract_number=contract_number)
            material_list = []
            for mat in obj.required_materials:
                if mat.get('material') is not None:
                    if mat['agreed_value'] is None:
                        mat['agreed_value'] = 0

                    try:
                        article = Article.objects.get(id=mat["material"])
                    except:
                        article = None
                    if article is not None:
                        article_obj = {
                            "id": article.id,
                            "name": article.name
                        }
                        materials_obj = {
                            "material": article_obj,
                            "agreed_value": int(mat["agreed_value"]),
                            "remaining": int(mat["agreed_value"]) - sum(
                                Transaction.objects.filter(
                                    article_id=mat["material"], customer=obj.customer)
                                    .values_list("net_weight", flat=True))
                        }
                        material_list.append(materials_obj)
            return JsonResponse({
                "contract_number": obj.contract_number,
                "customer": {"name": obj.customer.name1, "id": obj.customer.id} if obj.customer is not None else '',
                "start_date": obj.start_date,
                "end_date": obj.end_date,
                "name": obj.name,
                "price_group": obj.price_group,
                "status": obj.status,
                "forwarders": obj.forwarders_id,
                "reserved_date": obj.reserved_date,
                "required_materials": material_list,
                "project_number": obj.project_number,
                "vehicles": [{"id": i[0], "license_plate": i[1], "self_tara": i[2]} for i in
                             obj.vehicles.all().values_list("id", "license_plate", "self_tara")],
                "supplier": [{"id": i[0], "name": i[1]} for i in
                             obj.supplier.all().values_list("id", "supplier_name")]
                # "supplier": [{"id": i[0], "name": i[1]} for i in obj.suppliers.all().values_list("id", "name")]
            })
        except Contract.DoesNotExist as e:
            return HttpResponse(status=404)
        pass
    elif request.method == "DELETE":
        try:
            obj = Contract.objects.get(contract_number=contract_number)
            obj.delete()
        except Contract.DoesNotExist as e:
            return HttpResponse(status=404)

    else:
        return HttpResponse(status=405)


def contract_pdf(request, contract_number):
    if request.method == "GET":
        obj = Contract.objects.get(contract_number=contract_number)
        url = request.build_absolute_uri(
            f"/contract_pdf/{obj.contract_number}")
        svg_value = generate_qr_code(obj.contract_number, 1)

        material_list = []
        newMaterialList = sorted(obj.required_materials, key=lambda d: d['material'])
        for mat in newMaterialList:
            if mat['material'] is not None:
                article = Article.objects.get(id=mat["material"])
                article_obj = {
                    "id": article.id,
                    "name": article.name
                }
                materials_obj = {
                    "material": article_obj,
                    "agreed_value": mat["agreed_value"],
                    "remaining": sum(Transaction.objects.filter(article_id=mat["material"], customer=obj.customer,
                                                                contract_number=obj.contract_number)
                                     .values_list("net_weight", flat=True))
                }
                material_list.append(materials_obj)
        supplier = None
        if obj.supplier.all().first() is not None:
            supplier = obj.supplier.all().first()

        ctx = {
            "contract": obj,
            "supplier": supplier,
            "vehicles": ",".join([i.license_plate for i in obj.vehicles.all()]),
            "construction_site": obj.supplier.name,
            "qr_code": svg_value,
            "materials": material_list,
            "logo": Logo.objects.all(),
            "email": Settings.objects.all().last(),
        }
        ctx["tele_phone_number"] = CompanyTelePhone.objects.last()
        return Render.render("yard/contract_pdf_template.html", ctx)
        # return render(request,"yard/contract_pdf_template.html", ctx)

    else:
        return HttpResponse(status=405)


def update_contract(request):
    form = ContractForm(data=request.POST)
    if form.is_valid():
        form.save()
        return JsonResponse({"status": "success"})
    else:
        return JsonResponse(form.errors, status=400)


class ContractView(View):
    def get(self, request):
        ctx = {}
        contracts = Contract.objects.all()
        contract_list = []
        for contract in contracts:
            material_list = []
            for mat in contract.required_materials:
                if mat.get('material') is not None:
                    remaining = math.floor(int(mat["agreed_value"]) - sum(
                        Transaction.objects.filter(article__id=mat["material"], customer=contract.customer,
                                                   contract_number=contract.contract_number).values_list(
                            "net_weight", flat=True))) if mat["agreed_value"] else 0

                    try:
                        article = Article.objects.get(id=mat["material"])
                    except:
                        article = None

                    if article is not None:
                        materials_obj = {
                            "material": article,
                            "agreed_value": mat["agreed_value"] if mat["agreed_value"] else 0,
                            "remaining": remaining,
                            "percentage": int(remaining / int(mat["agreed_value"]) * 100) if mat["agreed_value"] else 0
                        }
                        material_list.append(materials_obj)
            contract_obj = {
                "contract_number": contract.contract_number,
                "customer": contract.customer,
                "materials": material_list,
                "supplier": contract.supplier,
                "start_date": contract.start_date,
                "end_date": contract.end_date,
                "name": contract.name
            }
            contract_list.append(contract_obj)

        contracts_with_pagination = Paginator(contract_list, 50)
        ctx["contracts"] = contracts_with_pagination.page(request.GET.get("page") or 1)
        ctx["contract_form"] = ContractForm()
        ctx["customers"] = Customer.objects.all()
        ctx["total_pages"] = contracts_with_pagination.num_pages
        ctx["materials"] = Article.objects.all()
        ctx["vehicles"] = Vehicle.objects.all()
        ctx["suppliers"] = Supplier.objects.all()
        ctx["forwarders"] = Forwarders.objects.all()
        return render(request, "yard/contracts.html", context=ctx)

    def post(self, request):
        data = json.loads(request.body)
        try:
            contract = Contract.objects.get(
                contract_number=data['contract_number'])
        except:
            contract = None
        if contract is not None:
            form = ContractForm(data, instance=contract)
            if form.is_valid():
                form.save()
                return JsonResponse({"status": "success"})
            else:
                print(form.errors)
                return JsonResponse(form.errors, status=400)
        # if isinstance(data["customer"], dict):
        #     cust_form = CustomerForm(data=data["customer"])
        #     if cust_form.is_valid():
        #         cust_obj = cust_form.save()
        #         data["customer"] = cust_obj.id

        # if isinstance(data["supplier"], dict):
        #     supplier_form = SupplierForm(data=data["supplier"])
        #     if supplier_form.is_valid():
        #         supplier_obj = supplier_form.save()
        #         data["supplier"] = supplier_obj.id

        form = ContractForm(data=data)
        print("FORM IN VIEWS")
        print(data)
        if form.is_valid():
            form.save()
            return JsonResponse({"status": "success"})
        else:
            print(form.errors)
            return JsonResponse(form.errors, status=400)

    def put(self, request):
        data = json.loads(request.body)
        try:
            obj = Contract.objects.get(contract_number=data["contract_number"])
        except Contract.DoesNotExist as e:
            return JsonResponse({"status": "Not Found"}, status=404)

        form = ContractForm(data=data, instance=obj)
        if form.is_valid():
            form.save()
            return JsonResponse({"status": "success"})

        else:
            return JsonResponse(form.errors, status=400)


def contract_delete(request, number):
    obj = Contract.objects.get(contract_number=number)
    obj.delete()
    return HttpResponseRedirect('/contracts')


def id_pdf(request, id):
    context = {}
    context['logo'] = Logo.objects.all().last()

    if request.method == "GET":
        data = Combination.objects.get(id=id)
        url = data.ident
        context['qr_code'] = generate_qr_code(url, 5)
        context['data'] = data
        context["dataset"] = data
        context['date'] = datetime.now()
        context['email'] = Settings.objects.all().last()
        context['text'] = IDtext.objects.all().last()
        if IdDeliveryNoteOption.objects.all().last().option == 2:
            return Render.render('yard/sebald_id_pdf_template.html', context)
        else:
            return Render.render('yard/id_pdf.html', context)


def id_text(request):
    context = {}
    if request.POST:
        text = request.POST.get("text")
        IDtext.objects.all().delete()
        obj = IDtext(text=text)
        obj.save()

        return HttpResponseRedirect('/comb_list')

    context['data'] = IDtext.objects.all().last()
    return render(request, 'yard/id_text.html', context)


@csrf_exempt
def driver_sign(request):
    if request.POST:
        try:
            data = request.POST.get('signature')
            request.session['driver_sign'] = data
            # format, imgstr = data.split(';base64,')
            # ext = format.split('/')[-1]
            # sign = ContentFile(base64.b64decode(imgstr), name='temp.' + ext)
            return JsonResponse({'status': 1})
        except Exception as e:
            return JsonResponse({'error': e})
    return render(request, "yard/driver_sign.html")


def weight_page_set(request):
    if request.POST:
        weight_page = {'forwarder': 0, 'customer': 0, 'supplier': 0, 'material': 0}

        if request.POST.get('f_1') is not None:
            if request.POST.get('f_3') is not None:
                weight_page['forwarder'] = 2
            else:
                weight_page['forwarder'] = 1
        else:
            weight_page['forwarder'] = 0

        if request.POST.get('k_1') is not None:
            if request.POST.get('k_3') is not None:
                weight_page['customer'] = 2
            else:
                weight_page['customer'] = 1
        else:
            weight_page['customer'] = 0

        if request.POST.get('m_1') is not None:
            if request.POST.get('m_3') is not None:
                weight_page['material'] = 2
            else:
                weight_page['material'] = 1
        else:
            weight_page['material'] = 0

        if request.POST.get('s_1') is not None:
            if request.POST.get('s_3') is not None:
                weight_page['supplier'] = 2
            else:
                weight_page['supplier'] = 1
        else:
            weight_page['supplier'] = 0

        if request.POST.get('c_1') is not None:
            if request.POST.get('c_3') is not None:
                weight_page['comment'] = 2
            else:
                weight_page['comment'] = 1
        else:
            weight_page['comment'] = 0

        WeightPageSetting.objects.all().delete()

        obj = WeightPageSetting.objects.create(forwarder=weight_page['forwarder'], customer=weight_page['customer'],
                                               material=weight_page['material'], supplier=weight_page['supplier'],
                                               comment=weight_page['comment'])
        obj.save()

        return redirect('/settings')

    return render(request, 'yard/weight_page_setting.html')


{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{% load custom_template_filters %}
{% load l10n %}
{%load i18n%}
{% block content %}


    <!-- /#sidebar-wrapper -->
    <!-- <div id="content"> -->
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              <p> {% if request.session.article%} {{request.session.article}} {% else %} {% translate 'Materials' %} {% endif %}</p>
            </div>
            
          </div>
        </div>

        <div class="row">
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
              <label>{% translate "Show entries" %}:</label>
             <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
             </select>
           </div>
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
               <label>{% translate "Search" %}:</label>
               <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
           </div>
        </div> 
        <div class="slider_2">
        <div class="row">
        <!--table strat-->
          <table class="table table-striped table-hover table-bordered mt-3 ml-4 material_table" width="100%" id="articleTable">
            <thead>
              <tr> {% if request.user.is_superuser %}
                <th width="3%">{% translate "Action" %}</th> {%endif%}
                <th width="5%">{{form.name.label}}</th>
                <th width="5%">{{form.short_name.label}}</th>
                  <th width="5%">{{"Artikelnummer"}}</th>
                <th width="7%">{{form.description.label}}</th>
                <th width="5%">{% translate "Incoming Weight" %}  [t]</th>
                <th width="5%">{% translate "Weight" %}  [t]</th>
                <th width="5%">{% translate "Outgoing Weight" %}  [t]</th>
               <th width="5%">{{ form.density.label }}  [kg/m³]</th>
                <th width="5%">{{form.price1.label}}  </th>
                <th width="5%">{{form.price2.label}}  </th>
                <th width="5%">{{form.price3.label}}  </th>
                <th width="5%">{{form.price4.label}}  </th>
                <th width="5%">{{form.price5.label}}  </th>
                {% comment %}<th width="7%">{{form.group.label}}</th>{% endcomment %}
{#                <th width="3%">{{form.vat.label}}</th>#}
{#                <th width="7%">{{form.minimum_amount.label}}</th>#}
                <th width="7%">{{form.avv_num.label}}</th>
{#                <th width="7%">{{form.account.label}}</th>#}
                {% comment %}<th width="7%">{{form.cost_center.label}}</th>{% endcomment %}
                {% comment %}<th width="7%">{{form.unit.label}}</th>{% endcomment %}
{#                <th width="7%">{{form.min_quantity.label}}</th>#}
                {% comment %}<th width="7%">{{form.revenue_group.label}}</th>{% endcomment %}
                {% comment %}<th width="7%">{{form.revenue_account.label}}</th>{% endcomment %}
{#                <th width="7%">{{form.list_price_net.label}}</th>#}
{#                <th width="7%">{% translate "Listenpreis brutto" %}</th>#}
{#                <th width="7%">{{form.ean.label}}</th>#}
                <th width="7%">{{form.ware_house.label}}</th>
                <th width="7%">{{form.supplier.label}}</th>
              </tr>
            </thead>

            <tbody class="mt-4">
                {% for data in dataset %}
                <tr class="loadAD" ondblclick="javascript:loadArticleDetails('{{ data.id }}')">{% if request.user.is_superuser %}
                    <td><a class="loadA" href="javascript:loadArticleDetails('{{ data.id }}')"><i class="fas fa-pencil-alt text-primary"></i></a><a class="confirmdelete" href="{% url 'article_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a></td>
                    {% endif %}
                  <td>{{ data.name|default_if_none:"-" }}</td>
                  <td>{{ data.short_name|default_if_none:"-" }}</td>
                 <td>{{ data.article_number|default_if_none:"-" }}</td>
                  <td>{{ data.description|default_if_none:"-" }}</td>
                  <td>{{ data.entry_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
                  <td>{{ data.balance_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
                  <td>{{ data.outgoing_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
                  <td>{{ data.density|convert_to_kg|default_if_none:"0" }}</td>
                  <td>€&nbsp;{{ data.price1|default_if_none:"0" }}</td>
                  <td>€&nbsp;{{ data.price2|default_if_none:"0" }}</td>
                  <td>€&nbsp;{{ data.price3|default_if_none:"0" }}</td>
                  <td>€&nbsp;{{ data.price4|default_if_none:"0" }}</td>
                  <td>€&nbsp;{{ data.price5|default_if_none:"0" }}</td>
                  {% comment %}<td>{{ data.group|format_float }}</td>{% endcomment %}
{#                  <td>{{ data.vat|format_float|default_if_none:"0" }}</td>#}
{#                  <td>{{ data.minimum_amount|format_float|default_if_none:"0" }}</td>#}
                  <td>{{ data.avv_num|default_if_none:"0" }}</td>
{#                  <td>{{ data.account|default_if_none:"-" }}</td>#}
                  {% comment %}<td>{{ data.cost_center }}</td>{% endcomment %}
                  {% comment %}<td>{{ data.unit }}</td>{% endcomment %}
{#                  <td>{{ data.min_quantity|default_if_none:"-" }}</td>#}
                  {% comment %}<td>{{ data.revenue_group }}</td>{% endcomment %}
                  {% comment %}<td>{{ data.revenue_account }}</td>{% endcomment %}
{#                  <td>{{ data.list_price_net|default_if_none:"0" }}</td>#}
{#                  <td>{{ data.list_price_gross|default_if_none:"0" }}</td>#}
{#                  <td>{{ data.ean|default_if_none:"-" }}</td>#}
                  <td>{{ data.ware_house|default_if_none:"-" }}</td>
                  <td>{{ data.supplier|default_if_none:"-" }}</td>
                </tr>
            {% endfor %}
            </tbody>
<!--              <tfoot hidden>-->
<!--                  <tr>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text"class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text"class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->
<!--                     <th rowspan="1" colspan="1">-->
<!--                        <input type="text" class="form-control">-->
<!--                     </th>-->

<!--                  </tr>-->
<!--               </tfoot>-->

          </table>
        <!--table end-->
        </div>
      </div>
      </div>
      <div class="container">
        <div class="row mb-5">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
              <!-- Material form login -->
              <div class="card p-3 mt-4" id="article-form-section">
              {% if request.user.is_superuser %}
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                      <h5 class="card-header info-color white-text py-3">
                        <div class="panel-heading">
                          <h4 class="panel-title">
                            {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse2031"> {% endcomment %}
          
                              <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                                  <p class="mb-0 pt-2 mr-4 text-color text_color float-left">
                                    {%if request.session.article %}{{ request.session.article }} {%else%}{% translate "Material" %}{%endif%}
                                  </p>
                                <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill" style="float:right;">Neue Eingabe</button>
                                </div>
                                </div>
          
                            {% comment %} </a> {% endcomment %}
                          </h4>
                        </div>
                      </h5>
                    </div>
                  </div>
              {% endif %}
                  
            
                   <!--first forms satrat-->
                  <div id="collapse2031" class="collapse" >
                      <div class="panel-body"> 
                        <div class="contanier">      
                            <form class="form-group" method="POST" enctype="multipart/form-data">
                              {% csrf_token %}
                              <input type="hidden" name="id" id="id" value="">
                              <div class="row">        
                                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                                    <div>
                                    <label>{{form.name.label}}</label>
                                    {{ form.name}} {{ form.name.errors}}

                                  </div>
                                     <div>
                                    <label>{{"Artikelnummer"}}</label>
                                    {{ form.article_number}} {{ form.article_number.errors}}

                                  </div>
                                  <div>
                                    <label>{{form.short_name.label}}</label>
                                    {{ form.short_name}} {{ form.short_name.errors}}

                                    <!-- <label>Short Name</label> -->
                                    <!-- <input type="Short Name" class="form-control" id="Short Name" placeholder="Short Name"> -->
                                  </div>
                                      <div>
                                        <label>{{form.density.label}}</label>
                                        {{ form.density}} {{ form.density.errors}}
                                    </div>
                                  <div>
                                    <label>{{form.description.label}}</label>
                                    {{ form.description}} {{ form.description.errors}}
                  <!--                   <label>Description</label>
                                    <input type="Description" class="form-control" id="Description" placeholder="Description"> -->
                                  </div>
{#                                  <div>#}
{#<!--                                    <label>{{form.entry_weight.label}}</label>-->#}
{#<!--                                    {{ form.entry_weight}} {{ form.entry_weight.errors}}-->#}
{#                                     <label>{% translate 'Update Quantity' %}</label>#}
{#                                     <input type="number" step="any" value=0 required class="form-control" id="id_update_quantity" name="update_quantity" placeholder="{% translate 'Add material quanity' %}">#}
{#                                  </div>#}
                                  <!-- <div> -->
                                    <!-- <label>{{form.balance_weight.label}}</label> -->
                                    <!-- {{ form.balance_weight}} {{ form.balance_weight.errors}} -->
                                    <!-- <label>Remaining Weight</label> -->
                                    <!-- <input type="Remaining Weight" class="form-control" id="Remaining Weight" placeholder="Remaining Weight"> -->
                                  <!-- </div> -->
                                  <!-- <div> -->
                                    <!-- <label>{{form.outgoing_weight.label}}</label> -->
                                    <!-- {{ form.outgoing_weight}} {{ form.outgoing_weight.errors}} -->
                                    <!-- <label>Outgoing Weight</label> -->
                                    <!-- <input type="Outgoing Weight" class="form-control" id="Outgoing Weight" placeholder="Outgoing Weight"> -->
                                  <!-- </div> -->
                                  {% comment %}
                                  <div>
                                    <label>{{form.group.label}}</label>
                                    {{ form.group}} {{ form.group.errors}}
                                    <!-- <label>Group</label> -->
                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->
                                  </div>
                                  {% endcomment %}
                                  <div>
                                    <label>{{form.vat.label}}</label>
                                    {{ form.vat}} {{ form.vat.errors}}
                                    <!-- <label>Vat</label> -->
                                    <!-- <input type="Vat" class="form-control" id="Vat" placeholder="Vat"> -->
                                  </div>
                                  {% comment %}
                                   <div>
                                        <label>{{form.discount.label}}</label>
                                        {{ form.discount}} {{ form.discount.errors}}
                                    </div>
                                    {% endcomment %}
{#                                   <div>#}
{#                                        <label>{{form.list_price_net.label}}</label>#}
{#                                        {{ form.list_price_net}} {{ form.list_price_net.errors}}#}
{#                                    </div>#}
{#                                   <div>#}
{#                                        <label>{% translate "Listenpreis brutto" %}</label>#}
{#                                        {{ form.list_price_gross}} {{ form.list_price_gross.errors}}#}
{#                                    </div>#}
{#                                   <div>#}
{#                                        <label>{{form.ean.label}}</label>#}
{#                                        {{ form.ean}} {{ form.ean.errors}}#}
{#                                    </div>#}
                                    <div>
                                      <label>{{form.ware_house.label}}</label>
                                      {{ form.ware_house}} {{ form.ware_house.errors}}
                                    </div>
                                </div>
                                 <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                                <div>
                                      <label>{{form.supplier.label}}</label>
                                      {{ form.supplier}} {{ form.supplier.errors}}
                                  </div>
{#                                    <div>#}
{#                                        <label>{{form.minimum_amount.label}}</label>#}
{#                                        {{ form.minimum_amount}} {{ form.minimum_amount.errors}}#}
{#                                      <!-- <label>Minimum Amount</label> -->#}
{#                                      <!-- <input type="Minimum Amount" class="form-control" id="Minimum Amount" placeholder="Minimum Amount"> -->#}
{#                                    </div>#}
                                    <div class="row">
                                        <div class="col-md-6">
                                        <label>{{form.price1.label}}</label>
                                        {{ form.price1}} {{ form.price1.errors}}
                                        </div>
                                        <div class="col-md-6">
                                             <label>{{form.second_price1.label}}</label>
                                       <input type="text" name="second_price1" id="id_second_price1" readonly> {{ form.second_price1.errors}}
                                        </div>
                                      <!-- <label>price1</label> -->
                                      <!-- <input type="price1" class="form-control" id="price1" placeholder="price1"> -->
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                        <label>{{form.price2.label}}</label>
                                        {{ form.price2}} {{ form.price2.errors}}
                                        </div>
                                        <div class="col-md-6">
                                             <label>{{form.second_price2.label}}</label>
                                       <input type="text" name="second_price2" id="id_second_price2" readonly> {{ form.second_price2.errors}}
                                        </div>
                                      <!-- <label>price2</label> -->
                                      <!-- <input type="price2" class="form-control" id="price2" placeholder="price2"> -->
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                        <label>{{form.price3.label}}</label>
                                        {{ form.price3}} {{ form.price3.errors}}
                                        </div>
                                        <div class="col-md-6">
                                             <label>{{form.second_price3.label}}</label>
                                       <input type="text" name="second_price3" id="id_second_price3" readonly> {{ form.second_price3.errors}}
                                        </div>
                                      <!-- <label>price3</label> -->
                                      <!-- <input type="price3" class="form-control" id="price3" placeholder="price3"> -->
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                        <label>{{form.price4.label}}</label>
                                        {{ form.price4}} {{ form.price4.errors}}
                                        </div>
                                        <div class="col-md-6">
                                             <label>{{form.second_price4.label}}</label>
                                       <input type="text" name="second_price4" id="id_second_price4" readonly> {{ form.second_price4.errors}}
                                        </div>
                                      <!-- <label>price4</label> -->
                                      <!-- <input type="price4" class="form-control" id="price4" placeholder="price4"> -->
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                        <label>{{form.price5.label}}</label>
                                        {{ form.price5}} {{ form.price5.errors}}
                                        </div>
                                        <div class="col-md-6">
                                              <label>{{form.second_price5.label}}</label>
                                       <input type="text" name="second_price5" id="id_second_price5" readonly> {{ form.second_price5.errors}}
                                        </div>
                                      <!-- <label>price5</label> -->
                                      <!-- <input type="price5" class="form-control" id="price5" placeholder="price5"> -->
                                    </div>
                                    <div>
                                    <label>{{form.avv_num.label}}</label>
                                    {{ form.avv_num}} {{ form.avv_num.errors}}
                                    <!-- <label>Group</label> -->
                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->
                                  </div>
{#                                    <div>#}
{#                                    <label>{{form.account.label}}</label>#}
{#                                    {{ form.account}} {{ form.account.errors}}#}
{#                                    <!-- <label>Group</label> -->#}
{#                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->#}
{#                                  </div>#}
                                  {% comment %}
                                    <div>
                                    <label>{{form.cost_center.label}}</label>
                                    {{ form.cost_center}} {{ form.cost_center.errors}}
                                    <!-- <label>Group</label> -->
                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->
                                  </div>
                                    <div>
                                    <label>{{form.unit.label}}</label>
                                    {{ form.unit}} {{ form.unit.errors}}
                                    <!-- <label>Group</label> -->
                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->
                                  </div>
                                  {% endcomment %}
                                    <div>
{#                                    <label>{{form.min_quantity.label}}</label>#}
{#                                    {{ form.min_quantity}} {{ form.min_quantity.errors}}#}
{#                                    <!-- <label>Group</label> -->#}
{#                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->#}
{#                                  </div>#}


                                      <div>
                                        <label>{{form.unit.label}}</label>
                                        {{ form.unit}} {{ form.unit.errors}}
                                    </div>
                                  {% comment %}
                                    <div>
                                    <label>{{form.revenue_group.label}}</label>
                                    {{ form.revenue_group}} {{ form.revenue_group.errors}}
                                    <!-- <label>Group</label> -->
                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->
                                  </div>
                                    <div>
                                    <label>{{form.revenue_account.label}}</label>
                                    {{ form.revenue_account}} {{ form.revenue_account.errors}}
                                    <!-- <label>Group</label> -->
                                    <!-- <input type="Group" class="form-control" id="Group" placeholder="Group"> -->
                                  </div>
                                  {% endcomment %}
                                 </div>
                                 <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                    <button id="submit" type="submit" class="btn btn-primary mr-3 mt-3"><i class="fas fa-save ml-2"></i>{% translate "Save2" %}</button>
                                 </div>
                            </div>
                          </form>
                        </div>
                     </div>
                  </div>
              </div>
          </div>
        </div>
      </div>
    <!-- </div> -->
{% endblock %}

{% block scripts %}

<script>

     $(document).ready(function() {
         $("#id_price1").keyup(function (){
             var price_1 = (parseFloat($(this).val()) + (parseFloat($("#id_vat").val()) * parseFloat($(this).val()) / 100))
             if(price_1){
                 $("#id_second_price1").val(parseFloat(price_1.toFixed(2)))
             }
             else {
                 $("#id_second_price1").val("")
             }
         })

            $("#id_price2").keyup(function (){
             var price_2 = (parseFloat($(this).val()) + (parseFloat($("#id_vat").val()) * parseFloat($(this).val()) / 100))
             if(price_2){
                 $("#id_second_price2").val(parseFloat(price_2.toFixed(2)))
             }
             else {
                 $("#id_second_price2").val("")
             }
         })

            $("#id_price3").keyup(function (){
             var price_3 = (parseFloat($(this).val()) + (parseFloat($("#id_vat").val()) * parseFloat($(this).val()) / 100))
             if(price_3){
                 $("#id_second_price3").val(parseFloat(price_3.toFixed(2)))
             }
             else {
                 $("#id_second_price3").val("")
             }
         })

            $("#id_price4").keyup(function (){
             var price_4 = (parseFloat($(this).val()) + (parseFloat($("#id_vat").val()) * parseFloat($(this).val()) / 100))
             if(price_4){
                 $("#id_second_price4").val((price_4.toFixed(2)))
             }
             else {
                 $("#id_second_price4").val("")
             }
         })

            $("#id_price5").keyup(function (){
             var price_5 = (parseFloat($(this).val()) + (parseFloat($("#id_vat").val()) * parseFloat($(this).val()) / 100))
             if(price_5){
                 $("#id_second_price5").val((price_5.toFixed(2)))
             }
             else {
                 $("#id_second_price5").val("")
             }
         })
     })
    // DataTable
    $(document).ready(function() {
      $("#id_vat").addClass("form-control");
      $("#id_unit").addClass("form-control");

      var table = $('#articleTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
// <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
// <!--                       console.log("that.search()",that.search())-->
// <!--                       console.log("this.value",this.value)-->
// <!--                       if ( that.search() !== this.value ) {-->
// <!--                           that.search( this.value ).draw();-->
// <!--                       }-->
// <!--                   } );-->
               } );
           }
          });

      $("#articleTable_filter").hide()

      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      });

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      });

 });

     {% if request.user.is_superuser %}
         $(".loadA").click(function () {
             window.location = "#article-form-section";
             $("#collapse2031").addClass('show');
         })
         $(".loadAD").dblclick(function () {
             window.location = "#article-form-section";
             $("#collapse2031").addClass('show');
         })
     {% endif %}


  $("#new_entry").click(function(e){
    $("#collapse2031").addClass('show');
    $('input#id_update_quantity').val('0');
    $('input#id_minimum_amount').val('0');

    $('#id_ware_house').prop('selectedIndex',0);
    $('#id_supplier').prop('selectedIndex',0);
    return false;
  })
</script>
{% endblock %}
# Generated by Django 3.1.1 on 2021-09-10 11:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0075_merge_20210910_1029'),
    ]

    operations = [
        migrations.AlterField(
            model_name='transaction',
            name='article',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='yard.article'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='container',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='yard.container'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='yard.customer'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='yard.supplier'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='yard.vehicle'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='yard',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='yard.yard_list'),
        ),
    ]

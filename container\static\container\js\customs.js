function populateContainerDetails(id) {
    
    id = id
    ajax = 1
    $.ajax({
      type: "GET",
      url: "/container_type/",
      data:{"id":id,"ajax":ajax},
      success: function (result) {
       // $("#container_type").val(id);
        $("#container_designation").val(result.designation);
        $("#container_size").val(result.size);
        
        



        
      }
    })
  }

  function populateContainerDetails1(id) {
  
    id = id
    ajax = 1
    $.ajax({
      type: "GET",
      url: "/savecontainerdetails/",
      data:{"id":id,"ajax":ajax},
      success: function (result) {
        $("#type").val(result.container_type);
        $("#designation").val(result.designation);
        $("#size").val(result.size);
        $("#account_unit").val(result.account_unit);
        $("#maintenance_cycle").val(result.maintenance_cycle);
        $("#empty_weight").val(result.empty_weight);
        $("#maxweightallowed").val(result.maximum_weight_allowed);
        $("#costperday").val(result.cost_per_day);
        $("#costperhour").val(result.cost_per_hour);
        $("#workingdaypermonth").val(result.workingdays_permonth);
        $("#workinghourperday").val(result.workinghours_perday);
        $("#costcenter").val(result.cost_center);
       // $("#size").val(result.size);
        
      }
    })
  }
  function savecontainerdetails(id){
      let id = $('#id').val();
      let type = $('#type').val();
      let designation = $('#designation').val();
      let size = $('#size').val();
      let account_unit = $('#account_unit').val();
      let maintenance_cycle = $('#maintenance_cycle').val();
      let empty_weight = $('#empty_weight').val();
      let maximum_weight_allowed = $('#maxweightallowed').val();
      let cost_per_day = $('#costperday').val();
      let cost_per_hour = $('#costperhour').val();
      let workingdays_permonth = $('#workingdayspermonth').val();
      let workinghours_perday = $('#workinghourperday').val();
      let cost_center = $('#costcenter').val();

      $.ajax({
        url:"{% url 'container_details_save'/ + id %}", 
        type:"POST",
        data:{
            csrfmiddlewaretoken: '{{ csrf_token }}',
            type:type,
            designation:disgnation,
            size:size,
            account_unit:account_unit,
            maintenance_cycle:maintenance_cycle,
            empty_weight:empty_weight,
            maximum_weight_allowed:maximum_weight_allowed,
            
            cost_per_day:cost_per_day,
            cost_per_hour:cost_per_hour,
            workingdays_permonth:workingdays_permonth,
            workinghours_perday:workinghours_perday,
            cost_center:cost_center,
            
        },
        success:function(data){
            if(data=="success"){
                window.location.href = "/container_type";                          
            }
        









  }
  function populateCustomerDetails(id) {
    $.ajax({
      type: "GET",
      url: "/customer_details/" + id,
      beforeSend: function (xhr) { 
        $('.kunden').addClass('loading')
        console.log('beforeSend');
      },
      success: function (result) {
        $("#customer_id").val(id);
        $("#customer_name2").val(result.name2);
        if (result.salutation == null){
          $("#customer_salutation").text("Name");
        } else {
        $("#customer_salutation").text(result.salutation);
        }
        $("#customer_street").val(result.street);
        $("#customer_pin").val(result.pin);
        $("#customer_place").val(result.place);
        $("#customer_price_group").val(result.price_group);
        if (result.price_group==undefined){
          $("#customer_price_group").val('price1');
        }else{
        $("#customer_price_group").val(result.price_group);
        }
        $("#customer_price_group").trigger('change');
  
      },
      complete: function () {
        $('.kunden').removeClass('loading')
      },
    })
  }
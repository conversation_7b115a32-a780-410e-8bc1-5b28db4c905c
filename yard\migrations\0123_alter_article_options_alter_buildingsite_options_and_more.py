# Generated by Django 4.0.4 on 2022-05-28 12:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0122_merge_20220527_1113'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='article',
            options={'ordering': ('name',)},
        ),
        migrations.AlterModelOptions(
            name='buildingsite',
            options={},
        ),
        migrations.AlterModelOptions(
            name='combination',
            options={},
        ),
        migrations.AlterModelOptions(
            name='contract',
            options={},
        ),
        migrations.RemoveField(
            model_name='customer',
            name='tele_phone_number',
        ),
        migrations.RemoveField(
            model_name='supplier',
            name='project_number',
        ),
        migrations.AlterField(
            model_name='article',
            name='account',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='article_number',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='avv_num',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='balance_weight',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='deduct_weight',
            field=models.DecimalField(blank=True, decimal_places=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='density',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='description',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='entry_weight',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='list_price_gross',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='list_price_net',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='min_quantity',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='minimum_amount',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='outgoing_weight',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='price1',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='price2',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='price3',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='price4',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='price5',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='short_name',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='vat',
            field=models.DecimalField(blank=True, choices=[('7', '7%'), ('13', '13%'), ('19', '19%')], decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='combination',
            name='contracts',
            field=models.ManyToManyField(blank=True, null=True, related_name='contracts', to='yard.contract'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='supplier',
            field=models.ManyToManyField(blank=True, null=True, to='yard.supplier'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='vehicles',
            field=models.ManyToManyField(blank=True, null=True, to='yard.vehicle'),
        ),
        migrations.AlterField(
            model_name='invoice',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.transaction', unique=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='deduction',
            field=models.ManyToManyField(blank=True, null=True, to='yard.article'),
        ),
        migrations.AlterField(
            model_name='user',
            name='address',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='user',
            name='telephone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
    ]

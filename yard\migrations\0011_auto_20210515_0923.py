# Generated by Django 3.1.1 on 2021-05-15 09:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0010_auto_20210507_0904'),
    ]

    operations = [
        migrations.AlterField(
            model_name='article',
            name='balance_weight',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='entry_weight',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='minimum_amount',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='outgoing_weight',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='price1',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='yard',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list'),
        ),
        migrations.AlterField(
            model_name='settings',
            name='article',
            field=models.CharField(choices=[('Material', 'Material'), ('Produkt', 'Produkt'), ('Artikel', 'Artikel')], max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='settings',
            name='customer',
            field=models.CharField(choices=[('Kunde', 'Kunde'), ('Erzeuger', 'Erzeuger')], max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='settings',
            name='language',
            field=models.CharField(choices=[('de', 'German'), ('fr', 'French'), ('ru', 'Russian')], default='en-us', max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='settings',
            name='supplier',
            field=models.CharField(choices=[('Lieferant', 'Lieferant'), ('Baustelle', 'Baustelle'), ('Schlag', 'Schlag')], max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='vehicle',
            name='vehicle_weight',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='vehicle',
            name='vehicle_weight2',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
    ]

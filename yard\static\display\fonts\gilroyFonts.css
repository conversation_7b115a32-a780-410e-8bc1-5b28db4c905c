@font-face {
    font-family: '<PERSON><PERSON>', sans-serif;
    src: url('<PERSON>roy-Light.woff2') format('woff2'),
        url('Gilroy-Light.woff') format('woff'),
        url('<PERSON>roy-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Gilroy', sans-serif;
    src: url('Gilroy-Regular.woff2') format('woff2'),
        url('Gilroy-Regular.woff') format('woff'),
        url('Gilroy-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Gilroy', sans-serif;
    src: url('Gilroy-Medium.woff2') format('woff2'),
        url('Gilroy-Medium.woff') format('woff'),
        url('Gilroy-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: '<PERSON><PERSON>', sans-serif;
    src: url('Gilroy-Bold.woff2') format('woff2'),
        url('Gilroy-Bold.woff') format('woff'),
        url('Gilroy-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Gilroy', sans-serif;
    src: url('Gilroy-Heavy.woff2') format('woff2'),
        url('Gilroy-Heavy.woff') format('woff'),
        url('Gilroy-Heavy.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

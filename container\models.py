from django.db import models
from yard.models import Supplier, Customer
from django.utils.translation import gettext_lazy as _


# Create your models here.

class Container(models.Model):

    SETPRICEPERPIECE_CHOICES = (
        (1, _('Set Price 1')),
        (2, _('Set Price 2')),
        (3, _('Set Price 3')),)

    STANDPRICEPERDAY_CHOICES = (
        (1, _('Stand Price 1')),
        (2, _('Stand Price 2')),
        (3, _('Stand Price 3')),)
        


    
    container_type = models.CharField(max_length=10, unique=True, null=True, blank=True)
    container = models.CharField(max_length=100, blank=True, null=True)
    customer = models.CharField(max_length=100, blank=True, null=True) 
    construction_site = models.CharField(max_length=100, blank=True, null=True)
    disposer = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    bring_date_from = models.DateTimeField(auto_now_add=True, blank=True)
    bring_date_till = models.DateTimeField(auto_now_add=True, blank=True)
    pickup_date_from = models.DateTimeField(auto_now_add=True, blank=True)
    pickup_date_till = models.DateTimeField(auto_now_add=True, blank=True)
    company_number = models.CharField(max_length=100, blank=True, null=True)
    designation = models.CharField(max_length=100, blank=True, null=True)
    container_number = models.CharField(max_length=100, blank=True, null=True)
    quantity = models.CharField(max_length=100, blank=True, null=True) 
    unit_of_measurement = models.CharField(max_length=100, blank=True, null=True) 
    collectionpoint_number = models.CharField(max_length=100, blank=True, null=True) 
    point_of_occurence = models.CharField(max_length=100, blank=True, null=True) 
    point_of_occurence = models.CharField(max_length=100, blank=True, null=True)
    size = models.CharField(max_length=100, blank=True, null=True) 
    accounting_unit = models.CharField(max_length=100, blank=True, null=True) 
    maintenance_cycle = models.CharField(max_length=100, blank=True, null=True)
    empty_weight = models.CharField(max_length=100, blank=True, null=True) 
    maximum_weight_allowed = models.CharField(max_length=100, blank=True, null=True) 
    cost_perday = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    cost_perhour = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    workingdays_permonth = models.CharField(max_length=100, blank=True, null=True) 
    workinghours_perday = models.CharField(max_length=100, blank=True, null=True)
    cost_center = models.CharField(max_length=100, blank=True, null=True)
    setprice_perpiece= models.CharField(max_length=10, choices=SETPRICEPERPIECE_CHOICES, null=True, blank=True)
    standprice_perday = models.CharField(max_length=10, choices=STANDPRICEPERDAY_CHOICES, null=True, blank=True)

    def __str__(self):
        return str(self.container_type, self.id)

class containertype(models.Model):
    container_type = models.ForeignKey('container', on_delete=models.CASCADE, blank=True, null=True)










    

    class Meta:
        verbose_name = _("")
        verbose_name_plural = _("s")

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse("_detail", kwargs={"pk": self.pk})



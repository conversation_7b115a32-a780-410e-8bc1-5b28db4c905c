<!DOCTYPE html>
<html>
    {%load static%}
    {% load l10n %}
    {% load i18n %}
   <head>
         
          <link rel="stylesheet" type="text/css" href="{% static 'display/css/fontawesome.min.css' %}" />
        {% load i18n %}
        {% load l10n %}
   </head>
   <style>
      .font-12{
         font-size:12px
      }
      .pt-2{
         padding-top:2px
      }
      .ta-l{
         text-align:left
      }
      .ta-r{
         text-align:right
      }
      .ta-c{
         text-align:center
      }
   </style>
   <body>
<div>
   <table border="0" style="width:100%">
         <tr style="height:150px;">
            <th colspan="8" style="align-items:center;zoom:60%;"> {% if heading.logo.url is not None %} <img src={{heading.logo.url}} /> {% endif %}</th>
         </tr>
         <tr style="height:100px;">
            <th colspan="6" style="padding-top:3px;text-align:left;padding-left:2px;">
               <p>{{cust.name1|default_if_none:" "}} - <span style="font-size:10px; font-weight:100;">
                  {{cust.street|default_if_none:" "}} {{cust.pin|default_if_none:" "}}</span>
               </p>
               <p>{{cust.place|default_if_none:" "}}</p>
               <p>{{cust.country|default_if_none:" "}}</p>
            </th>
            <th colspan="1" style="padding-top:3px;padding-left:2px;">
               <p style="text-align:left;">Rechnungen-Nr.</p>
               <p style="text-align:left;">Kunden-Nr.</p>
               <p style="text-align:left;">Rechnungdatum</p>
               <p style="text-align:left;">Lieferdatum</p>

            </th>
            <th colspan="1" style="padding-top:3px;padding-left:2px;">
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{obj.invoice_no}}</span></p>
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{cust.id|default_if_none:" "}}</span></p>
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{obj.time|date:"d-m-Y"}}</span></p>
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{data.created|date:"d-m-Y"}}</span></p>
              
            </th>
         </tr>
         <tr style="height:150px;">
            <th colspan="8">
               <p style="font-size:20px;text-align:left;padding-right:10px">Rechnung</p>
               <p class="font-12" style="font-weight:200;text-align:left">Vielen dank für Ihren Auftrag, wir berechnen Ihnen folgende Lieferung bzw. Leistung:</p>
            </th>
         </tr>
         <tr style="height:50px;border-bottom:1px solid grey;">
            {% if data.material_weight is not None%}
            <td class="font-12" colspan="3" style="text-align:left;font-weight:500">Bezeichnung</td>
            <td class="font-12" colspan="1" style="text-align:left;font-weight:500">{% translate 'Quantity' %}</td>
            <td class="font-12" colspan="2" style="text-align:center;font-weight:500">{% translate 'Material Weight' %}</td>
            {% else %}
            <td class="font-12" colspan="4" style="text-align:left;font-weight:500">Bezeichnung</td>
            <td class="font-12" colspan="2" style="text-align:left;font-weight:500">{% translate 'Quantity' %}</td>
            {% endif %}
            <td class="font-12" colspan="1" style="text-align:left;font-weight:500"> {% translate 'Price' %} </td>
            <td class="font-12" colspan="1" style="text-align:right;font-weight:500"> {% translate 'Total'%} </td>
         </tr>
         <tr style="height:50px;border-bottom:1px solid grey;">
            {% if data.material_weight is not None%}
            <td colspan="3" class="font-12 pt-2">{{data.article|default_if_none:" "}}</td>
            <td colspan="1" class="font-12 ta-l">{{data.net_weight|default_if_none:"0"}}</td>
            <td colspan="2" class="font-12 ta-c">{{data.material_weight|default_if_none:"0"}}</td>
            {% else %}
            <td colspan="4" class="font-12 pt-2">{{data.article|default_if_none:" "}}</td>
            <td colspan="2" class="font-12 ta-l">{{data.net_weight|default_if_none:"0"}}</td>
            {% endif %}
            <td colspan="1" class="font-12 ta-l"> {{data.price_per_item|default_if_none:"0"}} </td>
            <td colspan="1" class="font-12 ta-r"> {{data.total_price|default_if_none:"0"}} </td>
         </tr>
         <tr style="height:120px;">
            <th colspan="5"></th>
            <th colspan="2" style="text-align:left;border-bottom:1px solid grey;border-width:8px;">
               <p style="font-weight:300">Summe Netto</p>
               <p style="font-weight:300">Mwst. {{tax|default_if_none:"0"}} %</p>
               <p>Gesampt</p>
            </th>
            <th colspan="1" style="text-align:right;border-bottom:1px solid grey;border-width:8px">
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{data.total_price|default_if_none:"0"}}</span></p>
               <p style="text-align:right;"><span style="font-size:10px; font-weight:400;">{{data.vat|default_if_none:"0"}}</span></p>
               <p style="text-align:right;"><span style="font-size:10px; font-weight:500;">{{data.price_after_tax|default_if_none:"0"}}</span></p>
            </th>
         </tr>

         

   </table>
   
</div>
</body>
</html>
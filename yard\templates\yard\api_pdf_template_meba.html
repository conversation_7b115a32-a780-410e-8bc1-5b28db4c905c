<!DOCTYPE html>
<html lang="en">

<head>
    {% block head %}
    {% load static %}
    {% endblock %}
    {% load i18n %}
    {% load l10n %}
    {% load custom_template_filters %}
    <meta charset="utf-8">
    <title>Delivery Note</title>
    <style>
        @page {
            margin: 20;
            size: 220mm 550mm;
            /*or width x height 150mm 50mm*/
        }

        body {
            font-size: 40px;
        }

        .Row {
            display: table;
            width: 100%;
            /*Optional*/
            table-layout: fixed;
            /*Optional*/
            border-spacing: 40px;
            /*Optional*/
        }

        .Column {
            display: table-cell;
            border-left: 10px;
        }
    </style>
</head>

<body>
    <div class="a4_sheet">

        <div>
            <br />

            <table style="width:100%">
                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong style="font-size:50px;"> Wageschein:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        <strong style="font-size:50px;"> {% if dataset.lfd_nr is not None and dataset.lfd_nr != "" %}{{ dataset.lfd_nr }}{% else %}{{ dataset.id }}{% endif %}</strong>
                    </td>
                </tr>

                <tr style="height: 40px">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">

                    </td>
                    <td colspan="2" style="padding-left: 50px;">

                    </td>
                </tr>

                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>KFZ. Ident:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.vehicle.id }}
                    </td>
                </tr>
                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">

                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.vehicle.license_plate }}
                    </td>
                </tr>
                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>Baust:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.supplier.id }}
                    </td>
                </tr>
                <tr style="height: 80px;">
                    <td colspan="1" style="padding-left: 10px;">
                        {{ dataset.supplier.supplier_name}}
                    </td>
                </tr>
                <tr style="height: 40px">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">

                    </td>
                    <td colspan="2" style="padding-left: 50px;">

                    </td>
                </tr>

                <tr style="height: 80px">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>Kunde:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.customer.id }}
                    </td>
                </tr>
                <tr style="height: 80px;">
                    <td colspan="2" style="padding-left: 10px;">
                        {{ dataset.customer.name1}}
                    </td>

                </tr>
                <tr style="height: 80px;" style="padding-left: 5px;font-size: 20px;">
                    <td colspan="2" style="padding-left: 10px;">
                        {{ dataset.customer.street}}
                    </td>
                </tr>
                <tr style="height: 80px;" style="padding-left: 5px;font-size: 20px;">
                    <td colspan="2" style="padding-left: 10px;">
                        {{ dataset.customer.pin}}
                    </td>
                    <td colspan="2" style="padding-left: 10px;">
                        {{ dataset.customer.place}}
                    </td>
                </tr>

                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>Material:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.article.id }}
                    </td>
                </tr>
                <tr style="height: 80px;" style="padding-left: 5px;font-size: 20px;">
                    <td colspan="2" style="padding-left: 10px;">
                        {{ dataset.article.name}}
                    </td>

                </tr>
                <tr style="height: 40px">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">

                    </td>
                    <td colspan="2" style="padding-left: 50px;">

                    </td>
                </tr>


                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>ID:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.firstw_alibi_nr }}
                    </td>
                </tr>

                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>DATUM:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.firstw_date_time|date:"d.m.Y" }}
                    </td>
                </tr>

                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>ERSTGEW:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.first_weight|format_weight}} t
                    </td>
                </tr>

                <tr style="height: 40px">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">

                    </td>
                    <td colspan="2" style="padding-left: 50px;">

                    </td>
                </tr>
                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>ID:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.secondw_alibi_nr }}
                    </td>
                </tr>

                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>DATUM:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.secondw_date_time|date:"d.m.Y" }}
                    </td>
                </tr>

                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>ZWEITGE:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        {{ dataset.second_weight|format_weight }}   t
                    </td>
                </tr>
                <tr style="height: 80px;">
                    <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                        <strong>NETTO:</strong>
                    </td>
                    <td colspan="2" style="padding-left: 50px;">
                        <strong>{{dataset.net_weight|format_weight}}    t</strong>
                    </td>
                </tr>

            </table>
        </div>
        <div>
            <p> Messwerte aus frei programmierbarer Zusatzeinrichtung. Die geeichten Messwerte können
                eingesehen werden. Für Überladungen haftet der Fahrzeuglenker.</p>
        </div>


    </div>
</body>

</html>
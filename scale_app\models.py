from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _

DEVICE_CHOICES = (
    ('Scale', _('Scale')),
    ('Camera', _('Camera')),
    ('Door', _('Door')),
    ('Display', _('Display'))
)


# Create your models here.
class Devices(models.Model):
    name = models.CharField(max_length=100, unique=True)
    ip_addr = models.CharField(max_length=100, blank=True, null=True)
    serial_num = models.CharField(max_length=100, default="None", null=True)
    mac_addr = models.CharField(max_length=100, blank=True, null=True)
    port = models.PositiveIntegerField(null=True, blank=True)
    device_type = models.CharField(choices=DEVICE_CHOICES, max_length=50)
    is_simulation = models.BooleanField(default=False)
    wx_btn = models.BooleanField(default=True)
    zero_btn = models.BooleanField(default=True)
    tara_btn = models.BooleanField(default=True)
    man_tara_btn = models.BooleanField(default=True)
    x10_btn = models.BooleanField(default=True)
    active = models.BooleanField(default=False)
    certi_num = models.CharField(max_length=100, default="None", blank=True, null=True)
    max_weight = models.PositiveIntegerField(blank=True, null=True)
    min_weight = models.PositiveIntegerField(blank=True, null=True)
    e_d = models.PositiveIntegerField(null=True, blank=True, default=1)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    ord_pos = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(4)

        ], blank=True, null=True
    )

    def save(self, *args, **kwargs):
        if self.pk is None:
            count = self.__class__.objects.filter(device_type=self.device_type).count()
            self.ord_pos = count + 1
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if self.ord_pos < 4:
            next_devices = self.__class__.objects.filter(device_type=self.device_type,ord_pos__gt=self.ord_pos)\
                            .order_by("ord_pos")
            for device in next_devices:
                device.ord_pos = device.ord_pos - 1
                device.save()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Transaction(models.Model):
    trans_id = models.AutoField(primary_key=True)
    device = models.ForeignKey('Devices', on_delete=models.CASCADE)
    created_date_time = models.DateTimeField(auto_now_add=True, blank=True)
    updated_date_time = models.DateTimeField(auto_now=True, blank=True)
    tara = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_weight = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def __str__(self):
        return self.trans_id

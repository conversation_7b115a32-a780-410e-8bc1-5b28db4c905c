<!DOCTYPE html>
<html lang="en">
	<head>
		<title>Scale View</title>
		{%block head%}
			{%load static%}
		{%endblock%}
		<meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
        <link rel="stylesheet" href="{% static 'scale_app/css/custom.css'%}">
		<link rel="stylesheet" href="{% static 'display/css/main-style.css'%}">

        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
	</head>
	<style>
		.container-custom{
			background-color: #ece9e9;
		}
		body{
			background-color: #ece9e9;
		}
		.row1{
			background-color: #000;
			color:white;
		}
		.f1{
			font-size: 20pt;
		}
		.f2{
			font-size: 15pt;
		}
		.row2{
			background-color: #f1a456eb;
		}
		.bdr-top{
			border-top: 1px solid black;
		}
		.f5{
			font-size: 100pt;
		}
		.btn-row{
			padding:10px; 
		}
		.btn-sqr{
			width: 50px !important;
			height: 50px !important;
			font-size: 10px;
			margin: 5px;
		}
		.f-black{
			color:black;
		}
		.btn-warning{
			background-color: 	#aa4224;
		}
		

	</style>
	<body>
		<div id="container" class="container container-custom">
			<div class="row">&nbsp;</div>
			<div class="panel panel-default">
				<div class="panel-heading flexHead">
					Edit Device 
					<div>
						<a href="/logout" class="">
							<button class="pull-right Btn_Link">Logout</button>
						</a>
                        <a href="/scaleview" class="">
							<button class="pull-right Btn_Link">Scale</button>
						</a>
						<a href="/edit_devices/0" class="">
							<button class="pull-right Btn_Link" >Add New Device</button>
						</a>
					</div>
				</div>
        		<div class="row">&nbsp;</div>
        		<table class="table table-sm table-striped table-bordered">
        			<thead class="thead-dark">
				        <tr>
				          <th>Name</th>
				          <th>IP Address</th>
				          <!-- <th>Serial</th> -->
{#				          <th>Port</th>#}
                            <th>Simulation</th>
					  <th>Trans List</th>
				          <th>Action</th>
				        </tr>
				    </thead>
				    <tbody>
                     {% for key,values in dataset.items %}
                         {% if values|length > 0 %}
                              <tr>
                                  <th class="thead-dark" colspan="5" scope="colgroup">{{ key }}</th>
                              </tr>
                              {% for data in values %}
                                  <tr>
                                      <td>{{data.name}}</td>
                                      <td>{% if data.ip_addr|length > 9 %}{{ data.ip_addr|truncatechars:9 }}...{% else %}{{data.ip_addr}}{% endif %}</td>
                                      <!-- <td>{{data.serial_num}}</td> -->
                                      <td>{{ data.is_simulation }}</td>
                                      <td><a href="/trans_list/{{data.id}}"><button>View</button></a></td>
                                      <td>
                                        <a href="/edit_devices/{{data.id}}">Edit</a>{%if not data.active  %}&nbsp;/&nbsp;<a href="/delete_device/{{data.id}}">Delete</a>{%endif%}
            {#					      	{%if data.active  %}&nbsp;<label class="btn-success">Active</label>{%else%} <label class="btn-danger"><a class="btn-danger" href="/activate_device/{{data.id}}" >Activate</a></label>{%endif%}#}
                                      </td>
                                  </tr>
                              {%endfor%}
                         {% endif %}
                    {% endfor %}
				  	</tbody>
        		</table>
			</div>
		</div>
	</body>
	<script type="text/javascript">
		$(document).ready(function() {

	});

	</script>
</html>

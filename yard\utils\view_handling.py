import base64
import io
import json

import pyqrcode
import xlwt
from django.contrib.humanize.templatetags.humanize import intcomma
from django.core.serializers import serialize
from django.db.models import Q
from django.shortcuts import redirect
from django.utils.translation import activate, get_language
from PIL import Image
from yard.models import Settings, images_base64, Customer, Vehicle, Forwarders, Supplier, Article, Combination, \
    SelectCamera, Logo, Container, Contract, Transaction, PlaceOfDelivery, SelectWaage, MasterContainer, AutoCapture, \
    FirstWeightCameras, SecondWeightCameras
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.conf import settings

def create_excel(data):
    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('Lieferschein')
    row_num = 0
    columns = ['Lfd Nr', 'Kennz.1', 'Richtung', 'Artikel', 'Kunde', 'Lieferant', '<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>',
               '<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>', 'Nettogewicht', '<PERSON><PERSON><PERSON>tpreis', '<PERSON>bi Nr.', 'Erzeugt am']
    for col_num in range(len(columns)):
        ws.write(row_num, col_num, columns[col_num])

    values = list(data.values_list('id', 'vehicle__license_plate', 'status', 'article__name',
                              'customer__name1', 'supplier__supplier_name', str('first_weight'),
                              str('second_weight'), str('net_weight'), 'total_price', 'secondw_alibi_nr',
                              'updated_date_time'))
    for row in values:
        row_num += 1
        for col_num in range(len(row)):
            if col_num == 2:
                if row[col_num] == '0':
                    ws.write(row_num, col_num, 'Eingang')
                elif row[col_num] == '1':
                    ws.write(row_num, col_num, 'Ausgang')
            elif col_num == 11:
                ws.write(row_num, col_num, row[col_num].isoformat())
            else:
                ws.write(row_num, col_num, row[col_num])
    return wb

def ready_to_csv(data):
    columns = ['Lfs Nr', 'Kennz.1', 'Richtung', 'Artikelnummer', 'Artikel','AVV Nummer','Preis','Kunde', 'Lieferant', 'Erstgewicht [kg]',
               'Zweitgewicht [kg]', 'Nettogewicht [kg]', 'Datum', "Uhrzeit"]
    values = data.values_list('id', 'vehicle__license_plate', 'status', 'article__article_number', 'article__name','article__avv_num', 'article__price1',
                              'customer__name1', 'supplier__supplier_name', str('first_weight'),
                              str('second_weight'), str('net_weight'),
                              'secondw_date_time',"vehicle_second_weight_flag", "firstw_date_time", "lfd_nr")
    list_value = [list(rows) for rows in values]

    for rows in list_value:
        status = rows[2]
        
        if status == '0':
            rows[2] = 'Eingang'
        elif status == "1":
            rows[2] = 'Ausgang'
        lfd = rows[-1] 
        if lfd is not None and lfd != "":
            rows[0] = lfd
            rows.pop()
        else:
            rows.pop()

        if str(rows[-2]) == "1":
            if rows[-1] != None:
                date_time = rows[-1]
                rows[-3] = date_time.strftime("%d.%m.%Y")
                rows.insert(-2 ,date_time.strftime("%H:%M:%S"))
        else:
            if rows[-3] != None:
                date_time = rows[-3]
                rows[-3] = date_time.strftime("%d.%m.%Y")
                rows.insert(-2 ,date_time.strftime("%H:%M:%S"))
        
        rows.pop()
        rows.pop()

    return columns, list_value


def set_cxt(request):
    customer_list = json.loads(serialize('json', Customer.objects.all(), fields=('name1', 'pk')))
    place_of_delivery_list = json.loads(serialize('json', PlaceOfDelivery.objects.all(), fields=('name1', 'pk')))
    vehicle_list = json.loads(serialize('json', Vehicle.objects.all(), fields=('license_plate', 'pk')))
    forwarder_list = json.loads(serialize('json', Forwarders.objects.all(), fields=('name', 'pk')))
    article_list = json.loads(
        serialize('json', Article.objects.filter(yard=request.user.yard), fields=('name', 'pk')))
    supplier_list = json.loads(serialize('json', Supplier.objects.all(), fields=('supplier_name', 'pk', 'project_number')))
    combination_list = json.loads(serialize('json', Combination.objects.all(), fields=('ident', 'pk')))
    container_list = json.loads(serialize('json', Container.objects.all(), fields=('container_number', 'pk')))
    camera = SelectCamera.objects.all().last()
    waage = SelectWaage.objects.last()
    mc = MasterContainer.objects.last()
    logo = Logo.objects.all()
    contracts = Contract.objects.all
    set_settings_session(request)
    context = {"customer_list": customer_list, "vehicle_list": vehicle_list, "article_list": article_list,
               "supplier_list": supplier_list, "combination_list": combination_list, "container_list": container_list,
               "forwarder_list": forwarder_list, "language": get_language(), "camera": camera, 'logo': logo,
               "contracts": contracts, "waage": waage, "place_of_delivery_list": place_of_delivery_list, "master_container":mc}
    return context


def set_ss_cxt(request):
    user = request.user
    customer_list = json.loads(serialize('json', user.customer_set.all(), fields=('name1', 'pk')))
    vehicle_list = json.loads(serialize('json', user.vehicle_set.all(), fields=('license_plate', 'pk')))
    forwarder_list = json.loads(serialize('json', Forwarders.objects.all(), fields=('name', 'pk')))
    article_list = json.loads(
        serialize('json', user.article_set.filter(yard=request.user.yard), fields=('name', 'pk')))
    supplier_list = json.loads(serialize('json', user.supplier_set.all(), fields=('supplier_name', 'pk')))
    combination_list = json.loads(serialize('json', Combination.objects.all(), fields=('ident', 'pk')))
    container_list = json.loads(serialize('json', Container.objects.all(), fields=('name', 'pk')))
    camera = SelectCamera.objects.all().last()
    logo = Logo.objects.all()
    set_settings_session(request)
    context = {"customer_list": customer_list, "vehicle_list": vehicle_list, "article_list": article_list,
               "supplier_list": supplier_list, "combination_list": combination_list, "container_list": container_list,
               "forwarder_list": forwarder_list, "language": get_language(), "camera": camera, 'logo': logo}
    return context


def generate_qr_code(url,scale:int):
    url = pyqrcode.create(url)
    # url.svg('uca.svg', scale=4)
    buffer = io.BytesIO()
    text_obj = url.png_as_base64_str(scale=scale)
    # byte_str = buffer.getvalue()
    # text_obj = byte_str.decode('UTF-8')
    return text_obj


def get_images(image_base64, trans_id, index):
    try:
        data = base64.b64decode(image_base64.encode('UTF-8'))
        buf = io.BytesIO(data)
        img = Image.open(buf)
        img_io = io.BytesIO()
        img.save(img_io, format='JPEG')
        return InMemoryUploadedFile(img_io, field_name=None, name=f"img_{trans_id}_{index}.jpg",
                                    content_type='image/jpeg', size=img_io.tell, charset=None)
    except Exception as e:
        print(e)
        return None


def save_base64(request, trans_id):
    try:
        trans_flag = str(request.POST["trans_flag"])
        vehicle_weight_flag = str(request.POST["vehicle_weight_flag"])
        vehicle_second_weight_flag = str(request.POST["vehicle_second_weight_flag"])
        ac = AutoCapture.objects.all().last()
        fw = FirstWeightCameras.objects.all().last()
        sw = SecondWeightCameras.objects.all().last()
        camera = SelectCamera.objects.all().last()
        img1 = None
        img2 = None
        img3 = None
        if camera.yes:
            if ac.status:
                if fw.cam1 != True and fw.cam2 != True and fw.cam3 != True and sw.cam1 != True and sw.cam2 != True and sw.cam3 != True:
                    
                    import requests
                    for i in range(int(camera.number)):
                        #cmd = f'GET IMAGE{i}'
                        HOST = settings.IMAGE_HOST #'127.0.0.1'  # The server's hostname or IP address
                        PORT = settings.IMAGE_PORT #3200
                        
                        try:
                            image_data = requests.get(f"http://{HOST}:{PORT}/get_image?id={i}", timeout=10).json()
                        # with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        #     s.connect((HOST, PORT))
                        #     s.sendall(cmd.encode("UTF-8"))
                        #     data = ""
                        #     while True:
                        #         part = s.recv(1024)
                        #         if len(part) == 0:
                        #             break
                        #         else:
                        #             data += part.decode("UTF-8")
                        
                            # image_data = json.loads(data)
                            if i == 0:
                                img1 = get_images(image_data["image_data"], trans_id, 1+(int(trans_flag)*3))
                            elif i == 1:
                                img2 = get_images(image_data["image_data"], trans_id, 2+(int(trans_flag)*3))
                            elif i == 2:
                                img3 = get_images(image_data["image_data"], trans_id, 3+(int(trans_flag)*3))
                        except:
                            pass
                else:
                    img1 = get_images(request.POST.get('image_loading1'), trans_id, 1)
                    img2 = get_images(request.POST.get('image_loading2'), trans_id, 2)
                    img3 = get_images(request.POST.get('image_loading3'), trans_id, 3)

        if vehicle_weight_flag == "1" or vehicle_second_weight_flag == "1":
            img_obj, create = images_base64.objects.update_or_create(transaction_id=trans_id,
                                                                 defaults={"image1": img1, "image2": img2,
                                                                           "image3": img3})
        else:
            if trans_flag == "1":
                img_obj, create = images_base64.objects.update_or_create(transaction_id=trans_id,
                                                                    defaults={"image4": img1, "image5": img2,
                                                                            "image6": img3})
            elif trans_flag == "0": 
                img_obj, create = images_base64.objects.update_or_create(transaction_id=trans_id,
                                                                    defaults={"image1": img1, "image2": img2,
                                                                            "image3": img3})
        # img_obj,create = images_base64.objects.update_or_create(transaction_id = trans_id, image1 = img1, image2 = img2, image3 = img3)
        img_obj.save()
    except Exception as e:
        print("error occured  during save base64 : ", e)


def set_settings_session(request):
    try:
        settings = Settings.objects.all()[0]
        request.session["customer"] = settings.customer
        request.session["supplier"] = settings.supplier
        request.session["article"] = settings.article
        request.session["show_article"] = settings.show_article
        request.session["show_supplier"] = settings.show_supplier
        request.session["show_yard"] = settings.show_yard
        request.session["show_forwarders"] = settings.show_forwarders
        request.session["show_storage"] = settings.show_storage
        request.session["show_building_site"] = settings.show_building_site
        request.session["read_number_from_camera"] = settings.read_number_from_camera
        request.session["language"] = settings.language
        activate(settings.language)
    except:
        pass


def get_agval_contract(contract_num, material_id):
    agreed_value = None
    for mat in contract_num.required_materials:
        if int(mat["material"]) == material_id:
            agreed_value = mat["agreed_value"]
    return agreed_value


def aggregate_material_val(contract_num, material_id):
    transaction_count = sum(Transaction.objects.filter(Q(contract_number=contract_num) & Q(article_id=material_id) & Q(net_weight__isnull=False))
                            .values_list("net_weight", flat=True))
    return transaction_count


# this code is used for changing the language
def changelanguage(request, lang):
    print(lang)
    # print(get_language())
    activate(lang)
    return redirect('/')


def yard_check(user):
    return user.yard is not None


def user_role(user):
    if user.role != "operator":
        return True
    else:
        return False


def format_currency(dollars):
    dollars = round(float(dollars), 2)
    return ",".join(("%s%s" % (intcomma(int(dollars)), ("%0.2f" % dollars)[-3:])).split("."))
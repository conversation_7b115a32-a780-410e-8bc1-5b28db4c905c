<!DOCTYPE html>
<html>
<head>
    {% block head %}
        {% load static %}
    {% endblock %}
    {% load i18n %}
    {% load l10n %}
    {% load custom_template_filters %}
    <title>PDF</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
        @page {
            size: letter portrait;
            @frame content_frame {
            left: 50pt;
            width: 512pt;
            top: 50pt;
            height: 692pt;
        }
        }

        body {
            font-size: 14px;
        }

        td.pt-4 {
            padding-top: 10pt;
        }

        td.pt-3 {
            padding-top: 7pt;
        }

        table.center {
            margin-left: auto;
            margin-right: auto;
        }

        .mt-4 {
            margin-top: 50px;
        }

        .mt-3 {
            margin-top: 5px;
        }


        .mr-4 {
            margin-right: 292px;
        }

        .Row {
            display: table;
            width: 100%; /*Optional*/
            table-layout: fixed; /*Optional*/
            border-spacing: 40px; /*Optional*/
        }

        .Column {
            display: table-cell;
            border-left: 10px;
        }
    </style>
</head>
<body>

<div class="p-5">

    <table>
        <tr>
            <td>
                <strong style="font-size: 21pt;">Wiegeschein</strong><br/>
                <strong>Lieferschein-Nr.: 1866</strong>
            </td>
            <td>
                {% for img in header_image %}
                    {% if img.logo.url is not None %}
                        <img src="{{ img.logo.url }}" style="width:200%;height:10%;"/>
                    {% endif %}
                {% endfor %}
            </td>
        </tr>
    </table>

    <table class="table-border" cellspacing="10" style="border-collapse: collapse; margin-top: 20px;">
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-left: 1px solid black; border-right: none;">
                Spedition
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: none;">
                Kennzeichen
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black;">
                ID-Name
            </td>
        </tr>
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;">
                <strong>Dürr</strong></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.vehicle.license_plate }}</strong></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.combination.ident }}</strong></td>
        </tr>
    </table>

    <table class="table-border" cellspacing="10" style="border-collapse: collapse; margin-top: 25px;">
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-left: 1px solid black; border-right: none;"></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black;  border-right: none;">
                Datum
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: none;">
                Uhrzeit
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black;">
                Gewicht
            </td>
        </tr>
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;">
                Erstwiegung
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.firstw_date_time|date:"d.m.y" }}</strong></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.firstw_date_time|time:"H:i" }}</strong></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.first_weight }} kg</strong></td>
        </tr>
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;">
                Zweitwiegung
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.secondw_date_time|date:"d.m.y" }}</strong></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.secondw_date_time|time:"H:i" }}</strong></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.second_weight }} kg</strong></td>
        </tr>
    </table>

    <table class="table-border" cellspacing="10" style="border-collapse: collapse; margin-top: 3px;">
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;">
                Nettogewicht
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;"></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-top: 1px solid black; border-bottom: 1px solid black;"></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black;">
                <strong>{{ dataset.net_weight }} kg</strong></td>
        </tr>
    </table>

    <table class="table-border" cellspacing="10" style="border-collapse: collapse; margin-top: 25px;">
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: none; border-top: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;"></td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: none; border-top: 1px solid black; border-bottom: 1px solid black;">
                Nördlich
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black;">
                Südlich
            </td>
        </tr>
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;">
                Erstwiegung
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-bottom: 1px solid black;">
                {% if images.image1 %}
                    {% with absolute_url|add:images.image1.url as url_string1 %}
                        <div class="Column"><img style="width: 180px; height: 120px;" src="{{ url_string1|to_base64 }}"
                                                 alt="">&nbsp;&nbsp;&nbsp;
                        </div>
                    {% endwith %}
                {% endif %}
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black;  border-bottom: 1px solid black;">
                {% if images.image2 %}
                    {% with absolute_url|add:images.image2.url as url_string2 %}
                        <div class="Column"><img style="width: 180px; height: 120px;" src="{{ url_string2|to_base64 }}"
                                                 alt="">&nbsp;&nbsp;&nbsp;
                        </div>
                    {% endwith %}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black;  border-bottom: 1px solid black; border-left: 1px solid black;">
                Zweitwiegung
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black;  border-bottom: 1px solid black;">
                {% if images.image3 %}
                    {% with absolute_url|add:images.image3.url as url_string3 %}
                        <div class="Column"><img style="width: 180px; height: 120px;" src="{{ url_string3|to_base64 }}"
                                                 alt="">&nbsp;&nbsp;&nbsp;
                        </div>
                    {% endwith %}
                {% endif %}
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-bottom: 1px solid black;">
                {% if images.image4 %}
                    {% with absolute_url|add:images.image4.url as url_string4 %}
                        <div class="Column"><img style="width: 180px; height: 120px;" src="{{ url_string4|to_base64 }}"
                                                 alt="">&nbsp;&nbsp;&nbsp;
                        </div>
                    {% endwith %}
                {% endif %}
            </td>
        </tr>
    </table>

    <table class="table-border" cellspacing="10" style="border-collapse: collapse; margin-top: 25px;">
        <tr>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: none; border-top: 1px solid black; border-bottom: 1px solid black; border-left: 1px solid black;">
                Unterschrift des Fahrers
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-right: none; border-top: 1px solid black; border-bottom: 1px solid black;">
                Unterschrift des Fahrers
            </td>
            <td style="padding-top: 5px; padding-bottom: 3px; text-align: center; border-top: 1px solid black; border-right: 1px solid black; border-bottom: 1px solid black;">
                Unterschrift des Empfängers
            </td>
        </tr>
        <tr>
            <td style="padding-top: 5px;  height: 85px; padding-bottom: 3px; text-align: center; border-right: 1px solid black;  border-bottom: 1px solid black; border-left: 1px solid black;">
                <img src="{{driver_sign.image.url}}" style="width:100%;height:10%;"/><br> {{user_name}}
            </td>
            <td style="padding-top: 5px; height: 85px; padding-bottom: 3px; text-align: center; border-right: 1px solid black;  border-bottom: 1px solid black;"></td>
            <td style="padding-top: 5px;  height: 85px; padding-bottom: 3px; text-align: center; border-right: 1px solid black; border-bottom: 1px solid black;"></td>
        </tr>
    </table>

    <p style="margin-top: 18px; font-size: 12px;">
        E: errechnet, PT: Preset Tara (voreingegebenes Tara) H: Handeingabe.
        Messwerte aus frei programmierbarer Zusatzeinrichtung. Die geeichten Messwerte können eingesehen werden. Für
        Überladungen
        haftet der Fahrzeuglenker.
    <hr style="margin-top: 5px; background-color: red; border: none;">
    </p>

    <p style="margin-top: 2px; text-align: center; font-size: 11px;">
        Sebald GmbH * Geschäftsführer: Dipl.-Ing.(FH) Angelika Birner, Dipl.-Ing. Ulrich Birner, Manuel Birner *
        Registergericht Amberg HRB 827
    </p>
</div>

</body>
</html>
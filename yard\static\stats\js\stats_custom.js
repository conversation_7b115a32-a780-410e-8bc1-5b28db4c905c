function loadDeliveryNoteDetails(id) {

$('#div_delivery_form').show();
window.location="#div_delivery_form";
// window.scrollTo(0, document.body.scrollHeight || document.documentElement.scrollHeight);
$('.errorlist').hide();
  $.ajax({
    type: "GET",
    url: "../deliverynote_detail/" + id,
    success: function (result) {
        console.log(result, "hello")
      if(result.deduction.length > 0){
        // $("#abzugs_div").html('')
        for(i=0;i<result.deduction.length;i++){
          html = `
          <div class="row">

                  <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                    <div class="md-form mb-3">
                      <label>Artikel</label>
                      <select class="form-control" name="deduction" id="id_deduction">
                        <option value="${result.deduction[i]['id']}" selected="">${result.deduction[i]['name']}</option>
                        {% for i in art%}
                        <option value={{i.id}}>{{i.name}}</option>
                        {% endfor%}
                      </select>
                      
                    </div>
              </div>
              <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12 text-left">
                <div class="md-form mb-2">
                  <label>Menge (kg)</label>
                  <input type="number" name="deduction_weight" step="1" value="${result.deduction[i]['weight']}"id="id_deduction_weight" class="form-control">
              </div>
            </div>
              <div class="col-xl-2 col-lg-2 col-md-2 col-sm-12 col-12 text-left">
                  <div class="md-form mb-2">
                    <label>Preis</label>
                    <input type="text" class="form-control" id="id_deduction_price" value="${result.deduction[i]['price']}" name="deduction_price" >
                  </div>
              </div><div class="col-xl-1 col-lg-1 col-md-1 col-sm-10 col-12 text-left">
                <div class="md-form mb-2">
                    <label>Mwst</label>
                    <input type="text" class="form-control" id="id_deduction_vat" value="${result.deduction[i]['vat']}" name="deduction_vat" >
                  </div>
                </div>
                <div class="col-xl-1 col-lg-1 col-md-1 col-sm-2 col-4 text-left">
                  <div class="md-form mb-2">
                    <label>Neuen</label>
                  <button onclick=javascript:remove_ded() type="button" class="btn btn-danger"><i class="fa fa-minus" style="pointer-events:none;"></i></button>
                  </div>
              </div>
              </div>`

              $("#abzugs_div").append(html);
        }
      }
        $('#load_images_btn').show();
        $("#id").val(id);
        $(".contract_number").val(result.contract_number).trigger("change")
         $("#id_forwarders").val(result.forwarders).trigger("change")
        $(".contract_number").attr("data-contract-value", 1)
        $("#anchor_inv").attr('href',`/stats/invoice/?id=${id}&loc=1`)
       $("#vehicle_id").val(result.vehicle).trigger('change');
       $("#id_vehicle").val($(".vehicleName"+id).text());
        $("#id_article").val(result.article).trigger('change');
        $("#id_delivery_customer").val(result.customer).trigger('change');
        $("#id_supplier").val(result.supplier).trigger('change');
        $("#id_first_weight").val(result.first_weight);
        $("#id_second_weight").val(result.second_weight);
        $("#id_net_weight").val(result.net_weight);
        $("#id_firstw_alibi_nr").val(result.firstw_alibi_nr);
         $("#id_comment").val(result.comment);
        $("#id_firstw_date_time").val(result.firstw_date_time);
        $("#id_secondw_alibi_nr").val(result.secondw_alibi_nr);
        $("#id_secondw_date_time").val(result.secondw_date_time);
        $("#id_vehicle_weight_flag").val(result.vehicle_weight_flag);
        $("#id_vehicle_second_weight_flag").val(result.vehicle_second_weight_flag);
        $("#id_trans_flag").val(result.trans_flag);
        $("#id_article_price").val(result.price_per_item);
        $("#id_contract_number").val(result.contract_number);
        $("#load_images_btn").val(id);
        $("#id_richtung").val(result.status);
        $('#id_status  option[value="'+result.status+'"]').prop("selected", true);
        // $("[name='deduction_weight']").val(result.deduction_weight);

        if (result.vehicle_weight_flag == 2 ){
          $("#id_first_weight").prop('readonly',false)
        } else{
          $("#id_first_weight").prop('readonly',true)
        }
        if (result.vehicle_second_weight_flag == 2 ){
          $("#id_second_weight").prop('readonly',false)
        } else {
          $("#id_second_weight").prop('readonly',true)
        }
    }
  })
}




function loadHoflisteDeliveryNoteDetails(id) {

$('#div_delivery_form').show();
window.location="#div_delivery_form";
// window.scrollTo(0, document.body.scrollHeight || document.documentElement.scrollHeight);
$('.errorlist').hide();
  $.ajax({
    type: "GET",
    url: "../deliverynote_detail/" + id,
    success: function (result) {
        console.log(result, "hello")

        if(result.lfd_nr != null && result.lfd_nr != "") {
          $("#id").val(result.lfd_nr);
        } else{
          $("#id").val(id);
        }

         $("#id_forwarders").val(result.forwarders).trigger("change")

       $("#id_vehicle").val(result.vehicle);
        $("#id_article").val(result.article).trigger('change');
        $("#id_delivery_customer").val(result.customer).trigger('change');
        $("#id_place_of_delivery").val(result.place_of_delivery).trigger('change')
        $("#id_supplier").val(result.supplier).trigger('change');
        $("#id_first_weight").val(result.first_weight);
        $("#id_richtung").val(result.status);
        $("#id_second_weight").val(result.second_weight);
        $("#id_net_weight").val(result.net_weight);
        $("#id_firstw_alibi_nr").val(result.firstw_alibi_nr);
        $("#id_firstw_date_time").val(result.firstw_date_time);
        $("#id_secondw_alibi_nr").val(result.secondw_alibi_nr);
        $("#id_secondw_date_time").val(result.secondw_date_time);
        $("#id_vehicle_weight_flag").val(result.vehicle_weight_flag);
        $("#id_vehicle_second_weight_flag").val(result.vehicle_second_weight_flag);
        $("#id_trans_flag").val(result.trans_flag);
        $("#id_article_price").val(result.price_per_item);
        $("#id_contract_number").val(result.contract_number);
        $("#load_images_btn").val(id);
        // $("[name='deduction_weight']").val(result.deduction_weight);

        if (result.vehicle_weight_flag == 2 ){
          $("#id_first_weight").prop('readonly',false)
        } else{
          $("#id_first_weight").prop('readonly',true)
        }

    }
  })
}


$( "#load_images_btn" ).click(function() {

    $("#MyPopupImg").modal();

    $.ajax({
      type: "GET",
      url: "../view_images_base64/"+ this.value,
      success: function (result) {
          let image_ele = ''
        if(result.status==false){
          image_ele = '<h4>'+ result.msg + '</h4>'
        }
        else{
            console.log(result)
            if(result.image1 !== ""){
                let image1 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image1" class="trans_img" src="/media/${result.image1}" width="200px">`
                image_ele += image1
            }
            if(result.image2!==""){
                let image2 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image3" class="trans_img" src="/media/${result.image2}" width="200px">`
                image_ele += image2
            }
            if(result.image3 !== ""){
                let image3 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image3" class="trans_img" src="/media/${result.image3}" width="200px">`
                image_ele += image3
            }

             if(result.image4 !== ""){
                let image4 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image4" class="trans_img" src="/media/${result.image4}" width="200px">`
                image_ele += image4
            }

              if(result.image5 !== ""){
                let image5 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image5" class="trans_img" src="/media/${result.image5}" width="200px">`
                image_ele += image5
            }

               if(result.image6 !== ""){
                let image6 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image6" class="trans_img" src="/media/${result.image6}" width="200px">`
                image_ele += image6
            }
                 if(result.image7 !== ""){
                let image7 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image7" class="trans_img" src="/media/${result.image7}" width="200px">`
                image_ele += image7
            }

             if(result.image8 !== ""){
                let image8 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image8" class="trans_img" src="/media/${result.image8}" width="200px">`
                image_ele += image8
            }

               if(result.image9 !== ""){
                let image9 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image9" class="trans_img" src="/media/${result.image9}" width="200px">`
                image_ele += image9
            }
                 if(result.image10 !== ""){
                let image10 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image10" class="trans_img" src="/media/${result.image10}" width="200px">`
                image_ele += image10
            }
                   if(result.image11 !== ""){
                let image11 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image11" class="trans_img" src="/media/${result.image11}" width="200px">`
                image_ele += image11
            }
                     if(result.image12 !== ""){
                let image12 = `<img style="max-width: 100%;padding: 2px;border-radius: 9px;" id="image12" class="trans_img" src="/media/${result.image12}" width="200px">`
                image_ele += image12
            }
        }
        $("#MyPopupImg .modal-body center").html(image_ele);
        $('.trans_img').on('click', function () {
                $('#MyPopupImg').modal('hide');
                var image = $(this).attr('src');
                 $('#MyModal').on('show.bs.modal', function () {
                    $(".img-responsive").attr("src", image);
                });
                $('#MyModal').modal('show');
            });
      }
    });
});

$("#btnClosePopup").click(function () {
  $("#MyPopupImg").modal("hide");
});

$(document).ready(function () {
  // $('#load_images_btn').hide()
  $('#div_delivery_form').hide()
   $('#save_delivery_note').click(function (event) {
   var form = $('#form_delivery_note');
   form.attr('target', '')
   form.submit();
  });


  $('#export-pdf').click(async function (event) {
  var form = $('#form_date_selection');
  form.attr('target', '_blank')
    return
 });
  $('#btn_date_selection').click(async function (event) {
  var form = $('#form_date_selection');
  form.attr('target', '_self')
    return
 });
  $('#id_export_data_btn').click(async function (event) {
  var form = $('#form_date_selection');
  form.attr('target', '_self')
    return
 });
  $('#id_mail_data_btn').click(async function (event) {
  var form = $('#form_date_selection');
  form.attr('target', '_self')
    return
 });

    const send_email = async (trans_id) =>{
        const url = "/stats/send_deliverynotes/"+trans_id
        let response = await $.ajax(url,{
            type:"GET"
        });
        
        return response
    }

    $("#send_email_btn").click(async function (event) {
      event.preventDefault();
      var trans_id = $("#id").val();
      console.log(trans_id);
      try {
        let response = await send_email(trans_id);
        console.log(response);
        alert(response.status);
      } catch (e) {
        console.log(e);
        if (e.status == 404) {
          alert(e.responseJSON.status);
        } else {
          alert(e.toString());
        }
      }
    });


   $('#cancel_delivery_note').click(function (event) {
    event.preventDefault()
    $('#div_delivery_form').hide()
   var form = $('#form_delivery_note')[0];
   form.reset();
  });


});

$("#id_first_weight").change(function(){
  $("#id_vehicle_weight_flag").val(2);
  currentdate = new Date();
  date_time = currentdate.getFullYear()+"-"+(currentdate.getMonth()+1)+"-"+currentdate.getDate()+" "+currentdate.getHours()+":"+currentdate.getMinutes()+":"+currentdate.getSeconds()+".000"
  $('#id_firstw_date_time').val(date_time);
})

$("#id_second_weight").change(function(){
  $("#id_vehicle_second_weight_flag").val(2);
  currentdate = new Date();
  date_time = currentdate.getFullYear()+"-"+(currentdate.getMonth()+1)+"-"+currentdate.getDate()+" "+currentdate.getHours()+":"+currentdate.getMinutes()+":"+currentdate.getSeconds()+".000"
  $('#id_secondw_date_time').val(date_time);
})

$("#sitelistdelete").click(function(e){
  if (!confirm("Do you want to delete")){
    return false;
  }
});


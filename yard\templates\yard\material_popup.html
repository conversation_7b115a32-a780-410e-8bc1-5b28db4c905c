{% load i18n %}
{% block content %}
<style>
body{border:0}
/*.dataTables_scroll{position:relative}*/
/*.dataTables_scrollHead{margin-bottom:40px;}*/
/*.dataTables_scrollFoot{position:absolute; top:38px}*/
table td{
  width:15px;
}
tfoot input {
        width: 100%;
        padding: 3px;
        box-sizing: border-box;
    }
</style>

<div class="row" style="overflow: auto;">
   <!--  <table class="table table-sm table-striped table-bordered aclist" style="">
      <thead class="thead-dark"> -->
    <table id="example" class="display" style="width:100%">
      <thead class="thead-dark" style="width:100%">
        <tr>
          <th>{% trans 'Action' %}</th>
          <th>{% trans 'Name' %}</th>
          <th>{% trans 'Short Name' %}</th>
          <th>{% trans 'Supplier' %}</th>
          <th>{% trans 'Preis1' %}</th>
          <th>{% trans 'Artikelnummer' %}</th>
          <th>{% trans 'Vat' %}</th>
          <th>{% trans 'Revenue Account' %}</th>
          <th>{% trans 'EAN' %}</th>
          <th>{% trans 'Ware House' %}</th>
        </tr>
      </thead>
      <tbody>
      {% for data in dataset %}
        <tr>
          <td><a href="javascript:loadMaterial('{{ data.id }}')">{% trans 'Select' %}</a></td>
          <td>{{ data.name }}</td>
          <td>{{ data.short_name }}</td>
          <td>{{ data.supplier }}</td>
          <td>{{ data.price1 }}</td>
          <td>{{ data.article_number }}</td>
          <td>{{ data.vat }}</td>
          <td>{{ data.revenue_account }}</td>
          <td>{{ data.ean }}</td>
          <td>{{ data.ware_house }}</td>
        </tr>
      {% endfor %}
      </tbody>
      <tfoot class="thead-dark" style="width:100%">
        <tr>
          <th>{% trans 'Action' %}</th>
          <th>{% trans 'Name' %}</th>
          <th>{% trans 'Short Name' %}</th>
          <th>{% trans 'Supplier' %}</th>
          <th>{% trans 'Preis1' %}</th>
          <th>{% trans 'Artikelnummer' %}</th>
          <th>{% trans 'Vat' %}</th>
          <th>{% trans 'Revenue Account' %}</th>
          <th>{% trans 'EAN' %}</th>
          <th>{% trans 'Ware House' %}</th>
        </tr>
      </tfoot>
    </table>
</div>
<div class="row">
  <br/>
  <br/>
</div>
<script>
  $(document).ready(function() {
    // Setup - add a text input to each footer cell
    $('#example tfoot th').each( function () {
        var title = $(this).text();
        $(this).html( '<input type="text"  />' );
    } );
 
    // DataTable
    var table = $('#example').DataTable({
        initComplete: function () {
            // Apply the search
            this.api().columns().every( function () {
                var that = this;
 
                $( 'input', this.footer() ).on( 'keyup change clear', function () {
                    if ( that.search() !== this.value ) {
                        that
                            .search( this.value )
                            .draw();
                    }
                } );
            } );
        }
    });
 
} );
    function loadMaterial(id){
    $('#id_article').val(id).trigger('change');
    $("#MyPopup").modal("hide");
  }
</script>
{% endblock %}

# Generated by Django 3.1.1 on 2021-07-02 06:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0041_auto_20210630_1220'),
    ]

    operations = [
        migrations.CreateModel(
            name='ForeignWeigh',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer', models.CharField(blank=True, max_length=100, null=True)),
                ('vehicle', models.CharField(blank=True, max_length=100, null=True)),
                ('supplier', models.CharField(blank=True, max_length=100, null=True)),
                ('article', models.CharField(blank=True, max_length=100, null=True)),
                ('first_weight', models.CharField(blank=True, max_length=100, null=True)),
                ('second_weight', models.CharField(blank=True, max_length=100, null=True)),
                ('net_weight', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('total_price', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('status', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('secondw_alibi_nr', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
    ]

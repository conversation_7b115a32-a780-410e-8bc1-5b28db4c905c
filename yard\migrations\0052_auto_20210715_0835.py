# Generated by Django 3.1.1 on 2021-07-15 08:35

from django.conf import settings
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0051_auto_20210714_1319'),
    ]

    operations = [
        migrations.AlterField(
            model_name='article',
            name='balance_weight',
            field=models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.AlterField(
            model_name='article',
            name='entry_weight',
            field=models.DecimalField(blank=True, decimal_places=4, default=0.0, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.AlterField(
            model_name='article',
            name='group',
            field=models.PositiveIntegerField(blank=True, choices=[(1, 'Type 1'), (2, 'Type 2'), (3, 'Type 3'), (4, 'Type 4'), (5, 'Type 5'), (6, 'Other Type')], default=6, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='outgoing_weight',
            field=models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.AlterField(
            model_name='article',
            name='ss_role_access',
            field=models.ManyToManyField(blank=True, null=True, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='customer',
            name='delivery_terms',
            field=models.CharField(blank=True, choices=[('free', 'Free'), ('paid', 'Paid'), ('other', 'Other')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='price_group',
            field=models.CharField(blank=True, choices=[('price1', 'Price 1'), ('price2', 'Price 2'), ('price3', 'Price 3'), ('price4', 'Price 4'), ('price5', 'Price 5')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='salutation',
            field=models.CharField(blank=True, choices=[('Mr', 'Mr'), ('Mrs', 'Mrs'), ('Company', 'Company')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='ss_role_access',
            field=models.ManyToManyField(blank=True, null=True, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='salutation',
            field=models.CharField(blank=True, choices=[('Mr', 'Mr'), ('Mrs', 'Mrs'), ('Company', 'Company')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='supplier',
            name='ss_role_access',
            field=models.ManyToManyField(blank=True, null=True, to=settings.AUTH_USER_MODEL),
        ),
    ]

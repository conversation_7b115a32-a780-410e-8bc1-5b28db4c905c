<!DOCTYPE html>
<html lang="en">
	<head>
		<title>Scale View</title>
		{%block head%}
			{%load static%}
		{%endblock%}
		<meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        {% comment %} <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
        <link rel="stylesheet" href="{% static 'scale_app/css/custom.css'%}">
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script> {% endcomment %}

		<link rel="stylesheet" href="{% static 'scale_app/css/bootstrap.min.css'%}">
        <link rel="stylesheet" href="{% static 'scale_app/css/custom.css'%}">

		<script src="{% static 'scale_app/js/jquery.min.js'%}"></script>
		<script src="{% static 'scale_app/js/bootstrap.min.js'%}"></script>
	</head>
	<style>
		.container-custom{
			background-color: #ece9e9;
		}
		body{
			background-color: #ece9e9;
		}
		.row1{
			background-color: #000;
			color:white;
		}
		.f1{
			font-size: 20pt;
		}
		.f2{
			font-size: 15pt;
		}
		.row2{
			background-color: #f1a456eb;
		}
		.bdr-top{
			border-top: 1px solid black;
		}
		.f5{
			font-size: 100pt;
		}
		.btn-row{
			padding:10px; 
		}
		.btn-sqr{
			width: 50px !important;
			height: 50px !important;
			font-size: 10px;
			margin: 5px;
		}
		.f-black{
			color:black;
		}
		.btn-warning{
			background-color: 	#aa4224;
		}
	</style>
	<body>
		<div id="container" class="container container-custom">
			<div class="row">&nbsp;</div>
			<div class="panel panel-default">
					<div class="panel-heading ">Transcations <a href="/devices/"><button class="pull-right" >Back</button></a></div>
        		<div class="row">&nbsp;</div>
        		<div style="max-height:200px;overflow:scroll;">
        		<table class="table table-sm table-striped table-bordered" style="height:200px">
        			<thead class="thead-dark">
				        <tr>
				          <th>ID</th>
				          <th>Device</th>
				          <th>Tara</th>
				          <th>Net Weight</th>
				        </tr>
				    </thead>
				    <tbody>
				      {% for data in dataset %}
				      <tr>
					      <td>{{data.trans_id}}</td>
					      <td>{{data.device}}</td>
					      <td>{{data.tara}}</td>
					      <td>{{data.net_weight}}</td>
			    	  </tr>
				      {%endfor%}
				  	</tbody>
        		</table>
        		</div>
			</div>
		</div>
	</body>
	<script type="text/javascript">
		$(document).ready(function() {

	});

	</script>
</html>
# Generated by Django 3.1.1 on 2021-06-30 12:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0040_transaction_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalWeigh',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=False)),
            ],
        ),
        migrations.AlterModelOptions(
            name='article',
            options={'ordering': ('name',)},
        ),
        migrations.AlterModelOptions(
            name='customer',
            options={'ordering': ('name1',)},
        ),
        migrations.AlterModelOptions(
            name='supplier',
            options={'ordering': ('supplier_name',)},
        ),
        migrations.AlterModelOptions(
            name='vehicle',
            options={'ordering': ('license_plate',)},
        ),
        migrations.AlterField(
            model_name='settings',
            name='language',
            field=models.CharField(choices=[('de', 'Deutsch'), ('fr', 'French'), ('ru', 'Russian')], default='en-us', max_length=40, null=True),
        ),
    ]

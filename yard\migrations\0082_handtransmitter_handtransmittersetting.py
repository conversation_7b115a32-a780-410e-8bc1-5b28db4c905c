# Generated by Django 3.1.1 on 2021-10-04 14:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0081_auto_20210913_1511'),
    ]

    operations = [
        migrations.CreateModel(
            name='HandTransmitterSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='HandTransmitter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=40, null=True)),
                ('devise_id', models.CharField(blank=True, max_length=50, null=True)),
                ('combination', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.combination')),
            ],
        ),
    ]

# Generated by Django 3.1.1 on 2021-08-24 07:35

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Container',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('container_type', models.CharField(blank=True, choices=[(1, 'Type 1'), (2, 'Type 2'), (3, 'Type 3'), (4, 'Type 4'), (5, 'Type 5'), (6, 'Other Type')], max_length=10, null=True)),
                ('container', models.CharField(blank=True, max_length=100, null=True)),
                ('customer', models.CharField(blank=True, max_length=100, null=True)),
                ('construction_site', models.CharField(blank=True, max_length=100, null=True)),
                ('disposer', models.CharField(blank=True, max_length=100, null=True)),
                ('bring_date_from', models.DateTimeField(auto_now_add=True)),
                ('bring_date_till', models.DateTimeField(auto_now_add=True)),
                ('pickup_date_from', models.DateTimeField(auto_now_add=True)),
                ('pickup_date_till', models.DateTimeField(auto_now_add=True)),
                ('company_number', models.CharField(blank=True, max_length=100, null=True)),
                ('designation', models.CharField(blank=True, max_length=100, null=True)),
                ('container_number', models.CharField(blank=True, max_length=100, null=True)),
                ('quantity', models.CharField(blank=True, max_length=100, null=True)),
                ('unit_of_measurement', models.CharField(blank=True, max_length=100, null=True)),
                ('collectionpoint_number', models.CharField(blank=True, max_length=100, null=True)),
                ('point_of_occurence', models.CharField(blank=True, max_length=100, null=True)),
                ('size', models.CharField(blank=True, max_length=100, null=True)),
                ('accounting_unit', models.CharField(blank=True, max_length=100, null=True)),
                ('maintenance_cycle', models.CharField(blank=True, max_length=100, null=True)),
                ('empty_weight', models.CharField(blank=True, max_length=100, null=True)),
                ('maximum_weight_allowed', models.CharField(blank=True, max_length=100, null=True)),
                ('cost_perday', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('cost_perhour', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('workingdays_permonth', models.CharField(blank=True, max_length=100, null=True)),
                ('workinghours_perday', models.CharField(blank=True, max_length=100, null=True)),
                ('cost_center', models.CharField(blank=True, max_length=100, null=True)),
                ('setprice_perpiece', models.CharField(blank=True, choices=[(1, 'Set Price 1'), (2, 'Set Price 2'), (3, 'Set Price 3')], max_length=10, null=True)),
                ('standprice_perday', models.CharField(blank=True, choices=[(1, 'Stand Price 1'), (2, 'Stand Price 2'), (3, 'Stand Price 3')], max_length=10, null=True)),
            ],
            options={
                'verbose_name': '',
                'verbose_name_plural': 's',
            },
        ),
    ]

{% extends 'base2.html' %}
{% load i18n %}
{% block head %}
    {% load static %}
{% endblock %}
{% block content %}

    <style>
        .hidden {
            display: none;
        }

        label {
            font-size: 12px;
        }

        input {
            font-size: 12px !important;
            height: 25px !important;
            border-radius: 0px !important;
        }

        input:focus {
            box-shadow: none !important;
        }

        .form-group {
            padding: 2px;
            margin: 1px;
        }

        .card {
            padding: 4px;
        }

        .card-body {
            box-shadow: 0px 0px 1px #ccc !important;
        }

        .ton {
            color: #222;
            font-size: 12px;
        }

        textarea {
            font-size: 12px !important;
            border-radius: 0px !important;
        }

        textarea:focus {
            box-shadow: none !important;
            border-color: indigo;
        }

        #met_btn {
            /* background-color: #ccc; */
            font-size: 16px;
        }

        #met_veh {
            /* background-color: #ccc; */
            font-size: 16px;
        }

        #met_btn:focus {
            box-shadow: none;
        }

        #met_veh:focus {
            box-shadow: none;
        }

        fieldset {
            padding: 0 18px 16px 18px !important;
            margin: 0 0 20px 0 !important;
            background: rgba(105, 105, 105, .3);
            box-shadow: 1px 1px 2px #ccc;
        }

        legend {
            width: unset !important;
            font-size: 13px;
            padding: 1px 5px;
            margin: 0;
            background: #f5f5f5;
            box-shadow: 0px 0px 3px 0px #ccc;
            color: #b23915;
            font-weight: 900;
        }

        .icon_met {
            position: relative;
            /* top: 4px; */
        }

        .modal-body {
            background-color: #f9fbfd;
        }

        @media only screen and (max-width: 600px) {
            .street {
                width: 100% !important;
            }
        }

        .street {
            width: 48.2%;
        }

        .icon_time {
            position: absolute;
            right: 12px;
            cursor: pointer;
            /* top: 14px; */
        }

        .sep {
            background: #dadada;
            padding: 9px 0 9px 0;
            margin: 10px -18px 10px -18px;
            border: 1px solid #e2e2e2;
        }

        @media only screen and (max-width: 400px) {
            .righ {
                right: 0 !important;
            }
        }

        #sup_id {
            margin-bottom: 5px;
            margin-top: 5px;
        }

        /* .select2-container--open{
            position: relative !important;
        } */
    </style>



    <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
            <i class="fas fa-align-justify"></i>
        </button>
        <div class="row border border-top-0 border-left-0 border-right-0 mb-3">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                <div class="content_text">
                    <p class="mb-0">{% translate 'OVERVIEW' %}</p>
                </div>
                <div class="heding">
                    <p>{% translate 'Contracts' %}</p>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                <div style="float: right" class="content_text">
                    <button data-toggle="modal" data-target="#contract_model" type="button"
                            class="btn btn-primary ml-auto">
                        <i>{% translate 'New Entry' %}</i>
                    </button>
                </div>
            </div>
        </div>


        <div class="row p-2">
            <table class="table table-striped table-hover table-bordered mt-3 " width="100%" id="deliveryNoteTable">
                <thead>
                <tr>
                    <th>{% translate "Name" %}</th>
                    <th>{% translate "Contract No" %}</th>
                    <th>{% translate "Customer" %}</th>
                    {#                    <th>{% translate "Material" %}</th>#}
                    {% if request.user.is_superuser %}
                        <th>{% translate "Action" %}</th>
                    {% endif %}
                </tr>
                </thead>
                <tbody class="mt-4">
                {% for contract in contracts %}
                    <tr>
                        <td data-toggle="collapse" id="{{ forloop.counter }}" class="body_td{{ forloop.counter }}"
                            data-target="#collapseExample{{ forloop.counter }}" aria-expanded="false"
                            aria-controls="collapseExample{{ forloop.counter }}" style="cursor: pointer;"><input
                                type="hidden" value=""/>
                            <i class="fas fa-plus plus{{ forloop.counter }}"></i>
                            <i class="fas fa-minus minus{{ forloop.counter }} hidden"></i>
                            <span>{{ contract.name }}</span>
                        </td>
                        <td data-toggle="collapse" id="{{ forloop.counter }}" class="body_td{{ forloop.counter }}"
                            data-target="#collapseExample{{ forloop.counter }}" aria-expanded="false"
                            aria-controls="collapseExample{{ forloop.counter }}" style="cursor: pointer;"><input
                                type="hidden" value=""/>
                            <span>{{ contract.contract_number }}</span>
                        </td>
                        <td data-toggle="collapse" id="{{ forloop.counter }}" class="body_td{{ forloop.counter }}"
                            data-target="#collapseExample{{ forloop.counter }}" aria-expanded="false"
                            aria-controls="collapseExample{{ forloop.counter }}"
                            style="cursor: pointer;">{{ contract.customer.name1 }}</td>
                        {#                        <td>{{ contract.article.name }}</td>#}
                        {% if request.user.is_superuser %}
                            <td>
                                <div class="row">
                                    <div class="col-1" id="edit"><a class="hover pointer-cursor"
                                                                    onclick="((e) => open_edit_dialog(e,'{{ contract.contract_number }}'))()"><i
                                            class="fas fa-pencil-alt text-primary"></i></a></div>
                                    <div class="col-1 delte"><a class="hover pointer-cursor"
                                                                href="/contract_delete/{{ contract.contract_number }}"><i
                                            class="fas fa-trash-alt text-danger"></i></a></div>
                                    <div class="col-1"><a class="hover pointer-cursor"
                                                          href='/contract_pdf/{{ contract.contract_number }}'
                                                          target='_blank'><i class="fas fa-file-pdf"></i></a></div>
                                </div>

                            </td>
                        {% endif %}
                    </tr>
                    <tr class="collapse body_td{{ forloop.counter }}{{ forloop.counter }}"
                        id="collapseExample{{ forloop.counter }}">
                    <tr class="collapse" id="collapseExample{{ forloop.counter }}">
                        <th style="font-weight: 600;">Artikel</th>
                        <th style="font-weight: 600;">{% translate 'Agreed Value' %}</th>
                        <th style="font-weight: 600;">{{ "Verbleibend" }}</th>
                    </tr>
                    {% for material_obj in contract.materials %}
                        <tr class="collapse" id="collapseExample{{ forloop.parentloop.counter }}">
                            <td>{{ material_obj.material }}</td>
                            <td>{{ material_obj.agreed_value }}</td>
                            <td>

                                <div class="d-flex align-items-center">

                                    <div class="col-sm-1 text-left mr-2">
                                        {{ material_obj.remaining }}
                                    </div>
                                    <div class="progress w-100 ml-4">
                                        {% if material_obj.percentage < 5 %}
                                            <div class="progress-bar progress-bar-striped bg-danger progress-bar-animated"
                                                 role="progressbar" style="width: {{ material_obj.percentage }}%"
                                                 aria-valuenow="{{ material_obj.percentage }}" aria-valuemin="0"
                                                 aria-valuemax="100">{{ material_obj.percentage }} %
                                            </div>
                                        {% else %}
                                            <div class="progress-bar progress-bar-striped bg-success progress-bar-animated"
                                                 role="progressbar" style="width: {{ material_obj.percentage }}%"
                                                 aria-valuenow="{{ material_obj.percentage }}" aria-valuemin="0"
                                                 aria-valuemax="100">{{ material_obj.percentage }} %
                                            </div>
                                        {% endif %}
                                    </div>

                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                {% endfor %}
                </tbody>
            </table>
        </div>

    {% if total_pages > 1 %}
        <div class="float-right">
            <nav aria-label="Page navigation example">
                <ul class="pagination">

                    {% if contracts.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ contacts.previous_page_number }}">
                                <span aria-hidden="true">&laquo;</span>
                                <span class="sr-only">Previous</span></a>
                        </li>
                    {% endif %}


                    {% for num in contracts.paginator.page_range %}
                        <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                    {% endfor %}

                    {% if contracts.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ contracts.next_page_number }}">
                                <span aria-hidden="true">&raquo;</span>
                                <span class="sr-only">Next</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    {% endif %}

       

        {#    Create/Update Modal #}
        <div id="contract_model" class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog"
             aria-labelledby="myLargeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">{{ "Auftrag" }}</h5>
                        <button type="button" class="close cls" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>

                    <div class="modal-body ">

                        <div id="modal_div" class="container">
                            <form method="POST" enctype="multipart/formdata" id="contract_formx">
                                {% csrf_token %}
                                <fieldset>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class=" m-0">{% translate 'Contract No' %}</label>
                                                                <input type="text" id="id_contract_number"
                                                                       name="contract_number" class="form-control"
                                                                       placeholder="{% translate 'Contract Number' %}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class=" m-0">{% translate 'Name' %}</label>
                                                                <input type="text" id="id_name" name="name"
                                                                       class="form-control"
                                                                       placeholder="{% translate 'Name' %}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class=" m-0">{{ "Erstelltdatum" }}</label>
                                                        <input type="text" class="form-control" name="reserved_date"
                                                               autocomplete="off" id="reserved_date"
                                                               placeholder={{ "Erstelltdatum" }}>
                                                        <i class="fas fa-calendar-alt"
                                                           style="position: absolute;right: 30px;bottom: 12px;pointer-events: none;"></i>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="m-0">{% translate 'Project Number' %}</label>
                                                        <input type="text" class="form-control" id="project_number"
                                                               placeholder="{% translate 'Project Number' %}">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class=" m-0">Status</label>
                                                        <select class="form-control" name="contract_status">
                                                            <option value="Active">Aktiv</option>
                                                            <option value="Inactive">Inaktiv</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <fieldset>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group" style="position: relative;">
                                                <label class="m-0">{% translate 'Startdatum' %}</label>
                                                <input type="input" class="form-control" id="inputstart"
                                                       autocomplete="off" name="start_date"
                                                       placeholder={% translate 'Startdatum' %}>
                                                <i class="fas fa-calendar-alt"
                                                   style="position: absolute;right: 16px;bottom: 12px;pointer-events: none;"></i>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group" style="position: relative;">
                                                <label class=" m-0">{% translate 'Enddatum' %}</label>
                                                <input type="text" class="form-control" id="inputend" autocomplete="off"
                                                       name="end_date" placeholder={% translate 'Enddatum' %}>
                                                <i class="fas fa-calendar-alt"
                                                   style="position: absolute;right: 16px;bottom: 12px;pointer-events: none;"></i>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <fieldset style="position:relative;">

                                    {#            <div style="position: absolute;right: 20px;z-index: 1;"><button class="btn btn-sm btn-primary" id="new_custo" style="font-size: 12px;"><i class="fas fa-plus"></i></button></div>#}
                                    <div id="custt">
                                        <div class="row mt-3" id="customer">
                                            <div class="col-md-6">
                                                <div id="dd" class="form-group">
                                                    <label class="m-0"
                                                           style="font-weight: 600;"> {% translate 'Customer' %}</label>
                                                    <select id="customers_select" name="customer"
                                                            class="form-control select2">
                                                        <option value="0">Kunde auswählen</option>
                                                        {% for customer in customers %}
                                                            <option value="{{ customer.id }}">{{ customer.name1 }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="m-0"
                                                       style="font-weight: 600;"> {% translate 'Status' %}</label>
                                                <select name="customer" class="form-control" id="id_status">
                                                    <option value="0">Eingang</option>
                                                    <option value="1">Ausgang</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- {% translate 'Add New' %} -->
                                <fieldset style="position:relative;">

                                    <div style="position: absolute;right: 20px;z-index: 1;">
                                        <button class="btn btn-sm btn-primary" id="met_btn"><i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <div>
                                        <div class="row mt-3 mat-row">
                                            <div class="col-md-6">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div id="meterial">
                                                            <div class="form-group ">
                                                                <label class=" m-0"
                                                                       style="font-weight: 600;"> {% translate 'Material' %}</label>
                                                                <select id="materials_select" name="material"
                                                                        class="form-control">
                                                                    <option value="0">Arikel auswählen</option>
                                                                    {% for material in materials %}
                                                                        <option value="{{ material.id }}">{{ material.name }}</option>
                                                                    {% endfor %}
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class=" m-0"
                                                               style="font-weight: 600;"> {% translate 'Preisgruppe' %}</label>
                                                        <select id="id_price_group" name="price_group"
                                                                class="form-control">
                                                            <option>------</option>
                                                            <option value="price1">Preis 1</option>
                                                            <option value="price2">Preis 2</option>
                                                            <option value="price3">Preis 3</option>
                                                            <option value="price4">Preis 4</option>
                                                            <option value="price5">Preis 5</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">

                                                <div class="form-group">
                                                    <label class=" m-0">{% translate 'Agreed Value' %}</label>
                                                    <div class="d-flex align-items-center">
                                                        <input type="text" id="id_agreed_value"
                                                               class="form-control w-100" name="agreed_value"
                                                               placeholder="{% translate 'Agreed Value' %}">
                                                        <span class="ton pl-1">kg</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <fieldset style="position: relative;">

                                    {% comment %} <div style="position: absolute;right: 20px;z-index: 1;"><button class="btn btn-sm btn-primary" id="new_sup" style="font-size: 12px;">{% translate 'Add New' %}</button></div> {% endcomment %}
                                    <div style="position: absolute;right: 20px;z-index: 1;">
                                        <button class="btn btn-sm btn-primary" id="new_sup" style="font-size: 12px;"><i
                                                class="fa fa-plus"></i></button>
                                    </div>

                                    <div id="supplier">
                                        <div class="row mt-3" id="sup">
                                            <div class="col-md-4">
                                                <div class="form-group ">
                                                    <label class=" m-0" style="font-weight: 600;"><a
                                                            href="javascript:void(0)" id=" "> <i
                                                            class="fas fa-plus"></i></a> {{ "Baustelle" }}</label>
                                                    <div id="sup_id">
                                                        <select id="id_supplier" name="supplier" class="form-control">
                                                            <option value="0">Baustelle auswählen</option>
                                                            {% for supplier in suppliers %}
                                                                <option value="{{ supplier.id }}">{{ supplier.supplier_name }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="">{% translate 'Target Value' %}</label>
                                                <input type="text" id="id_target_value" name="target_value"
                                                       class="form-control"
                                                       placeholder="{% translate 'Target Value' %}">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="">{% translate 'Unit' %}</label>
                                                <select class="form-control" name="unit" id="id_unit">
                                                    <option value="0">{% translate 'kg' %}</option>
                                                    <option value="1">{% translate 'tonne' %}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <fieldset style="position: relative;">

                                    {% comment %} <div style="position: absolute;right: 20px;z-index: 1;"><button class="btn btn-sm btn-primary" id="new_sup" style="font-size: 12px;">{% translate 'Add New' %}</button></div> {% endcomment %}
                                    <div style="position: absolute;right: 20px;z-index: 1;">
                                        <button class="btn btn-sm btn-primary" id="new_sup" style="font-size: 12px;"><i
                                                class="fa fa-plus"></i></button>
                                    </div>

                                    <div id="forwarders">
                                        <div class="row mt-3" id="forwarders">
                                            <div class="col-md-10">
                                                <div class="form-group ">
                                                    <label class=" m-0" style="font-weight: 600;"><a
                                                            href="javascript:void(0)" id="plus_sup"> <i
                                                            class="fas fa-plus"></i></a> {{ "Spediteur" }}</label>
                                                    <div id="forwarders_id">
                                                        <select id="id_forwarders" name="forwarders"
                                                                class="form-control">
                                                            <option value="0">Spediteur auswählen</option>
                                                            {% for forwarder in forwarders %}
                                                                <option value="{{ forwarder.id }}">{{ forwarder.name }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <!-- {% translate 'Add New'%} -->
                                <fieldset style="position:relative;">

                                    <div style="position: absolute;right: 20px;z-index: 1;">
                                        <button class="btn btn-sm btn-primary" id="met_veh"><i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <div id="vehichle">
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="m-0"
                                                           style="font-weight: 600;"> {% translate 'Fahrzeug auswählen' %}</label>
                                                    <select id="vehicles_select" name="vehicles"
                                                            class="form-control select2 vehicles_select">
                                                        <option value="0">fahzeug auswählen</option>
                                                        {% for vehicle in vehicles %}
                                                            <option value="{{ vehicle.id }}">{{ vehicle.license_plate }}</option>
                                                        {% endfor %}
                                                    </select>
                                                    {#                            <input type="text"  class="form-control" placeholder="Vehichle">#}
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group mt-0 mt-md-3">
                                                    <span class="ton m-0">{% translate 'Speichertara erlaubt' %}</span>
                                                    <input name="tara_allowed[]" type="checkbox"
                                                           style="position: relative;top:9px;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>

                                <fieldset>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label class=" m-0"
                                                       style="font-weight: 600;">{% translate 'Comment' %}</label>
                                                <textarea name="comment" id="" cols="30" rows="4" class="form-control"
                                                          placeholder="{% translate 'Comment' %}"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </form>
                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="submit" form="contract_formx" class="btn btn-primary" id="save_contract_btn"
                                style="color: #000;">{% translate 'Save2' %}</button>
                        <button type="button" class="btn btn-secondary cls"
                                data-dismiss="modal">{% translate 'Close' %}</button>
                    </div>

                </div>
            </div>
        </div>


    </div>
{% endblock %}
{% block scripts %}

    <script>
        $("#plus_sup").click(function (e) {
            e.preventDefault();
            $("#sup_id").append('<div id="sup_id" style="position:relative;"><select id="id_supplier" name="supplier" class="form-control">
                {% for supplier in suppliers %}<option value="{{ supplier.id }}">{{ supplier.supplier_name }}
                    </option>{% endfor %}</s
            elect > < a

            class

            = "icon_met_singel"
            href = "javascript:void(0)"
            style = "position:absolute;right:-15px;top:7px;" > < i

            class

            = "fas fa-times" > < /i></
            a > < /div>'
        )
            ;
        });
        $(document).on("click", ".icon_met_singel", function () {
            $(this).parent().remove();
        });
    </script>
    <script>
        $("#met_btn").click(function (e) {
            e.preventDefault();
            let material_dropdown = $("#materials_select").find("option");
            let material_html = ``;
            for (let material of material_dropdown) {
                material_html += material.outerHTML;
            }
            $("#meterial").append(`<div class="row mat-row"><div class="col-6"><div class="form-group p-0">
                                    <select name="material" id="" class="form-control select2">
                                    {% for i in materials%}
                                    <option value="{{ i.id }}">{{ i.name }}</option>
                                    {% endfor %}
                                    </select>
                                    </div></div><div class="col-6" style="position:relative;"><div class="form-group"><div class="d-flex align-items-center"><input name="agreed_value" type="text" class="form-control w-100" placeholder="{% translate 'Agreed Value'%}"><span class="ton pl-1">tonne</span></div></div><a href="javascript:void(0)" class="icon_met" style="color: #000;text-decoration: none;top:11px;position:absolute;right:2px;"><i class="fas fa-times"></i></a></div></div>`
            );
        });

        $("#met_veh").click(function (e) {
            e.preventDefault();

            $("#vehichle").append(`<div class="row">
                                <div class="col-5 col-md-6">
                                    <div class="form-group">
                                        <select id="vehicles_select" name="vehicles" class="form-control select2 vehicles_select">
                                            {% for i in vehicles %}
                                            <option value="{{ i.id }}">{{ i.license_plate }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                        </div>`
            );
        });

        $("#new_sup").click(function (e) {
            e.preventDefault();

            $("#supplier").append(`<div class="row">
                                <div class="col-md-10">
                                    <div class="form-group ">
                                        <div id="sup_id">
                                            <select id="id_supplier" name="supplier" class="form-control select2">
                                                {% for supplier in suppliers %}
                                                    <option value="{{ supplier.id }}">{{ supplier.supplier_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <a href="" class="icon_met righ" style="color: #000;text-decoration: none;top:15px;position:absolute;right:0px;">
                                   <i class="fas fa-times"></i>
                                </a>
                                </div>
                            </div>`
            );
        });

        $("#new_custo").click(function (e) {
            e.preventDefault();

            $("#custt").append(
                `<div class="row">
                                <div class="col-md-10">
                                    <div id="dd" class="form-group">
                                        <select id="customers_select" name="customer" class="form-control select2">
                                            {% for customer in customers %}
                                                <option value="{{ customer.id }}">{{ customer.name1 }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <a href="" class="icon_met righ" style="color: #000;text-decoration: none;top:15px;position:absolute;right:0px;">
                                        <i class="fas fa-times">
                                        </i>
                                    </a>
                                </div>
                            </div>`
            );
        });

        $(document).on("click", ".icon_met", function (e) {
            e.preventDefault();
            $(this).parent().parent().remove();
        });

        $("#customers_select").select2({
            tags: true,
            dropdownParent: $("#modal_div")
        })
        $("#materials_select").select2({
            tags: true,
            dropdownParent: $("#modal_div")

        })
        $("#id_supplier").select2({
            tags: true,
            dropdownParent: $("#modal_div")

        })

        $("#id_forwarders").select2({
            tags: true,
            dropdownParent: $("#modal_div")

        })

        $(".vehicles_select").select2({
            tags: true,
            dropdownParent: $("#modal_div"),

        }).on('select2:open', function () {

            $('.select2-dropdown--above').attr('id', 'fix');
            $('#fix').removeClass('select2-dropdown--above');
            $('#fix').addClass('select2-dropdown--below');

        });
    </script>


    <script>

        $(document).ready(function () {

            $('#contract_model').on('hidden.bs.modal', function () {
                $("#contract_formx").get(0).reset()
            })

            // val = parseInt($('.progress-bar-striped').attr('aria-valuenow'))
            // if(val >= 75 ){
            //     $('.progress-bar-striped').removeClass('bg-success').addClass('bg-info')
            // } else if(val >= 50) {
            //     $('.progress-bar-striped').removeClass('bg-success').addClass('bg-warning')
            // } else if(val < 50 ){
            //     $('.progress-bar-striped').removeClass('bg-success').addClass('bg-danger')
            // }


            $(".delte").click(function () {
                if (confirm("Möchten Sie diesen Datensatz wirklich löschen?")) {
                    return true
                } else {
                    return false
                }


            })


            $('#deliveryNoteTable tr').click(function () {
                trclass = $(this).attr('class'); // table row class
                trid = $(this).attr('id'); // table row ID

                $(".plus" + trid).toggle();
                $("." + trclass.trid).toggle();
                $(".minus" + trid).toggle();
            });


        });


    </script>
    <script>
        $(function () {
            $("#inputstart").datepicker({
                format: 'dd.mm.yyyy'
            });
        });
        $(function () {
            //$("#inputend").datepicker();
            $("#inputend").datepicker({
                format: "dd.mm.yyyy"
            });
        });
        $(function () {
            //$("#reserved_date").datepicker();
            $("#reserved_date").datepicker({
                format: "dd.mm.yyyy"
            });
        });

        $('#contract_formx').submit(function () {
            if ($("#materials_select").val() == 0) {
                alert("Bitte wählen Sie ein Furit aus")
            }
        });

    </script>

    <script>

        function format_date(date) {
            if (!date) {
                return ""
            }

            let splitted_date = date.toString().split("-")
            return `${splitted_date[2]}.${splitted_date[1]}.${splitted_date[0]}`
        }

        $.ajaxSetup({
            headers: {"X-CSRFToken": '{{csrf_token}}'}
        });
        let contract_list = []

        var data = $(this).serializeArray();


        let addLoaderButton = () => {
            let location_btn = $("#save_contract_btn");
            location_btn.empty()
            let inner_html = $(`<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>`);
            let loading_html = $("<i>Loading...</i>");
            location_btn.attr("disabled");
            location_btn.append(inner_html);
            location_btn.append(loading_html);

        }

        let get_contract = async (id) => {
            let response = await $.ajax(
                {
                    url: `/contract/${id}`,
                    type: "GET"
                }
            );
            return response;
        }

        let removeLoader = () => {
            let location_btn = $("#save_contract_btn");
            location_btn.empty()
            let save_html = $("<i>Save</i>")
            location_btn.append(save_html);
        }

        retrieve = 1
        let open_edit_dialog = async (event, id) => {
            let response = await get_contract(id);
            console.log(response)
            retrieve = 0
            $("#id").attr("value", response.contract_number);

            $("#reserved_date").val(format_date(response.reserved_date));
            $("#inputstart").val(format_date(response.start_date));
            $("#inputend").val(format_date(response.end_date));
            $("#project_number").val(response.project_number);
            $("#id_name").val(response.name)
            $("#id_status").val(response.status)
            $("#id_price_group").val(response.price_group)
            $("#id_forwarders").val(response.forwarders).trigger("change")


            $("#customers_select").val(response.customer.id).trigger("change");
            $("#id_contract_number").attr("value", response.contract_number);
            let mat = response.required_materials;
            for (i = 0; i < mat.length; i++) {
                let id = mat[i].material.id
                let name = mat[i].material.name
                let agreed_value = mat[i].agreed_value
                if (i > 0) {
                    $("#meterial").html(`<div class="row mat-row">
                                <div class="col-md-12">
                                    <div class="form-group ">
                                      <label class=" m-0" style="font-weight: 600;"> {% translate 'Material'%}</label>
                                        <select id="materials_select" name="material" class="form-control select2">
                                                <option value="${id}">${name}</option>
                                            {% for material in materials %}
                                                <option value="{{ material.id }}">{{ material.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                            </div>`)
                } else {
                    $("#meterial").html(`<div class="row mt-3 mat-row">
                                <div class="col-md-12">
                                    <div class="form-group ">
                                        <label class=" m-0" style="font-weight: 600;"> {% translate 'Material'%}</label>
                                        <select id="materials_select" name="material" class="form-control select2 materials_select">
                                                <option value="${id}">${name}</option>
                                            {% for material in materials %}
                                                <option value="{{ material.id }}">{{ material.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                            </div>`)
                }


                $(".materials_select").select2({
                    tags: true,
                    dropdownParent: $("#modal_div")

                })

                //$("#materials_select").val(response.required_materials[i].material.id)
                //                      .trigger("change");
            }
            $("#id_agreed_value").attr("value", response.agreed_value)
            for (let i = 0; i < response.vehicles.length; i++) {
                id = response.vehicles[i].id
                licen = response.vehicles[i].license_plate
                self_tara = response.vehicles[i].self_tara
                if (i > 0) {
                    $("#vehichle").append(`<div class="row">
                                <div class="col-5 col-md-6">
                                    <div class="form-group">
                                        <select id="vehicles_select" name="vehicles" class="form-control select2 vehicles_select">
                                            
                                            <option value="${id}">${licen}</option>
                                            
                                        </select>
                                    </div>
                                </div>
                            <div class="col-md-2 col-6">
                                <div class="form-group">
                                    <span class="ton m-0">{% translate 'Self Tara Allowed'%}</span>
                                        <input name="tara_allowed[]" type="checkbox" style="position: checked=${self_tara} relative;top:9px;left:4px;">
                                </div>
                                <a href="javascript:void(0)" class="icon_met righ" style="color: #000;text-decoration: none;top:15px;position:absolute;right:0px;">
                                <i class="fas fa-times">
                                </i>
                                </a>
                            </div>
                        </div>`)
                } else {
                    $("#vehichle").html(`<div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="m-0" style="font-weight: 600;"> {% translate 'Vehicle' %}</label>
                            <select id="vehicles_select"  name="vehicles" class="form-control select2 vehicles_select">
                                        <option value="${id}">${licen}</option>
                                    {% for vehicle in vehicles %}
                                        <option value="{{ vehicle.id }}">{{ vehicle.license_plate }}</option>
                                    {% endfor %}
                            </select>
                        </div>
                    </div>
    
                    <div class="col-md-6">
                        <div class="form-group mt-0 mt-md-3">
                            <span class="ton m-0">{% translate 'Self Tara Allowed'%}</span>
                            <input name="tara_allowed[]" type="checkbox" checked=${self_tara} style="position: relative;top:9px;">
                        </div>
                    </div>
                </div>`)
                }


                //$("#vehicles_select").val(response.vehicles[i].id)
                //                     .trigger("change");
            }
            let sup = response.supplier;
            for (let i = 0; i < sup.length; i++) {
                id = sup[i].id;
                name = sup[i].name;
                if (i > 0) {
                    $("#supplier").append(`<div class="row">
                                <div class="col-md-10">
                                    <div class="form-group ">
                                        <div id="sup_id">
                                            <select id="id_supplier" name="supplier" class="form-control select2">
                                                <option value="${id}">${name}</option>
                                                {% for supplier in suppliers %}
                                                    <option value="{{ supplier.id }}">{{ supplier.supplier_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <a href="" class="icon_met righ" style="color: #000;text-decoration: none;top:15px;position:absolute;right:0px;">
                                   <i class="fas fa-times"></i>
                                </a>
                                </div>
                            </div>`
                    );
                } else {
                    $("#supplier").html(`<div class="row mt-3" id="sup">
                    <div class="col-md-10">
                        <div class="form-group ">
                            <label class=" m-0" style="font-weight: 600;"><a href="javascript:void(0)" id="plus_sup"> <i class="fas fa-plus"></i></a> {{ "Baustelle" }}</label>

                            <div id="sup_id">
                                <select id="id_supplier" name="supplier" class="form-control select2">
                                    {% for supplier in suppliers %}
                                        <option value="${id}">${name}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>`);
                }
            }
            $("#construction_sites_select").val(response.construction_site)
                .trigger("change");
            $("#contract_model").modal("show")

        }

        let clear_contract_form = () => {
            $("#id").attr("value", "");
            $("#customers_select").attr("value", null).trigger("change");
            $("#id_contract_number").attr("value", "");
            $("#materials_select").val(null)
                .trigger("change");
            $("#id_agreed_value").attr("value", 0)
            $("#vehicles_select").val(null)
                .trigger("change");
            $("#construction_sites_select").val(null)
                .trigger("change");
        }

        $(document).ready(() => {

            $('#contract_model').on('hidden.bs.modal', function () {
                console.log("Close Modal Triggered");
                clear_contract_form()
            });

            $(".cls").click(function () {
                retrieve = 1
            })

            $('#contract_model').on('show.bs.modal', function () {
                let random_num = Math.floor(Math.random() * 90000) + 10000;
                console.log("Show Modal Triggered");
                if (retrieve == 1) {
                    $("#id_contract_number").attr("value", `${random_num}`);
                }
            });


            console.log($("#id_status").val())

            let serialize_data = () => {
                let s_d_s = $("#inputstart").val();
                {#s_d_s = s_d_s.split('/')#}
                let e_d_s = $("#inputend").val();
                let status = $("#id_status").val();
                {#e_d_s = e_d_s.split('/')#}
                let r_d_s = $("#reserved_date").val();
                {#r_d_s = r_d_s.split('/')#}
                let start_date_str = s_d_s
                let end_date_str = e_d_s
                let reserved_date_str = r_d_s
                let contract_status = $('select[name="contract_status"]')[0].value;
                let vehicles_field = $('select[name="vehicles"]');
                let supplier_field = $('select[name="supplier"]');

                let vehicles = []
                for (let vehicle of vehicles_field) {
                    vehicles = [...vehicles, vehicle.value]
                }
                let suppliers = []
                for (let supplier of supplier_field) {
                    suppliers = [...suppliers, supplier.value]
                }
                let required_materials = []
                let material_rows = $(".mat-row")
                for (let mat_row of material_rows) {
                    let material = {}
                    material["material"] = $(mat_row).find('select').val();
                    material["agreed_value"] = parseInt($(mat_row).find('input').val());
                    required_materials = [...required_materials, material]
                }
                return {
                    csrf_token: $('input[name=csrfmiddlewaretoken]').val(),
                    id: $("#id").val(),
                    customer: $("#customers_select").val(),
                    contract_number: $("#id_contract_number").val(),
                    status: status,
                    required_materials: required_materials,
                    agreed_value: $("#id_agreed_value").val(),
                    forwarders: $("#id_forwarders").val(),
                    vehicles: vehicles,
                    supplier: suppliers,
                    start_date: start_date_str,
                    end_date: end_date_str,
                    project_number: $("#project_number").val(),
                    target_value: $("#id_target_value").val(),
                    price_group: $("#id_price_group").val(),
                    unit: $("#id_unit").val(),
                    reserved_date: reserved_date_str,
                    contract_status: contract_status,
                    name: $("#id_name").val(),

                }
            }

            let submit_contract_form = async function (e) {
                e.preventDefault();

                addLoaderButton();
                let url = "/contracts/";
                let form_data = serialize_data()
                let method = "POST";
                //
                console.log("form_data")
                console.log(form_data)
                try {
                    let response = await $.ajax(
                        {
                            url: url,
                            type: method,
                            data: JSON.stringify(form_data),
                        }
                    );
                    window.location.reload();

                } catch (e) {
                    console.log(e)
                    let error_text = ""
                    let error_data = e.responseJSON;
                    for (let key of Object.keys(error_data)) {
                        error_text += `${key}: ${error_data[key][0]} \n`;
                    }
                    alert(error_text);
                } finally {
                    removeLoader()
                }
                console.log(e);
                return false;
            }

            $('#save_contract_btn').click(function (e) {

                if ($("#materials_select").val() == 0) {
                    alert("Bitte wählen Sie ein Furit aus")
                    e.preventDefault();
                } else {
                    $("#contract_formx").submit(async (e) => await submit_contract_form(e));
                }
            });


        });

        $(document).on('click', '[data-dismiss="modal"]', function () {
            $(document).on('hide.bs.modal', '#contract_model', function () {
                window.location.reload()

            });
        })

    </script>

{% endblock %}

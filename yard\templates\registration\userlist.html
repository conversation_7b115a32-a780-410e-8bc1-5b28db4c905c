{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}
<div class="container">
   <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
   <i class="fas fa-align-justify"></i>
   </button>
   <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
         <div class="content_text">
            <p class="mb-0">{% translate "OVERVIEW" %}</p>
         </div>
         <div class="heding">
            <p>{% translate "Users" %}</p>
         </div>
      </div>
   </div>
   <!--table strat-->
   <div class="row">
      <div class="table-responsive pl-3 pr-3">
         <table class="table table-striped table-hover table-bordered mt-3" width="100%">
            <thead>
               <tr>
               <th width="5%">{{form.name.label}}</th>
               <th width="7%">{{form.email.label}}</th>
               <th width="7%">{{form.yard.label}}</th>
               <th width="7%">{{form.role.label}}</th>
               <th width="7%">{% translate "Superuser" %}</th>
               <th width="5%">{% translate "Action" %}</th>
               </tr>
            </thead>
            {% for data in dataset %}
            <tbody class="mt-4">
               <tr>
               <td>{{ data.name }}</td>
               <td>{{ data.email }}</td>
               <td>{{ data.yard }}</td>
               <td>{{ data.role }}</td>
               <td>{{ data.is_superuser }}</td>
               <td><a href="javascript:loadUsersDetails('{{ data.id }}')"><i class="fas fa-pencil-alt text-primary  ml-4"></i></a>
                  {% if data.id == request.user.id %} {% else %} <a class="confirmdelete" href="{% url 'user_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a> {% endif %}
               </td>
               </tr>
            </tbody>
            {% endfor %}
         </table>
      </div>
   </div>
   <!--table end-->
</div>

<div class="container">
  <div class="row mb-5">
    <div class="col-sm-12 col-12">
       <!-- Material form login -->
       <div class="card">
          <div class="row">
             <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                <h5 class="card-header info-color white-text py-3">
                   <div class="panel-heading">
                      <h4 class="panel-title pt-2">
                           <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18">
                            <p class="mb-0 pt-2 mr-3 text-color text_color float-left">{% translate "User" %}</p>
                            <button type="button" id="new_entry1" class="btn btn-dark" style="float:right;">Neue Eingabe</button>
                           </a>
                      </h4>
                   </div>
                </h5>
             </div>
          </div>
          <div id="collapse_18" class="collapse show" >
             <div class="panel-body">
                <div class="card-body text-left">
                   <form class="form-group" method="POST" enctype="multipart/form-data">
                      {% csrf_token %}
                      <input type="hidden" name="id" id="id">
                      <input type="hidden" id="user_id" value={{request.user.id}}>
                      <input type="hidden" name="address" id="id_address">
                      <input type="hidden" name="telephone" id="id_telephone">
                      <div class="row">
                        <div class="col-sm-6 col-12 md-form mb-3">
                           <label class="label">{{form.name.label}}</label>
                           {{form.name}} {{form.name.errors}}
                        </div>
                        <div class="col-sm-6 col-12 md-form mb-3">
                           <label class="label">{{form.email.label}}</label>
                           {{form.email}} {{form.email.errors}}
                        </div>
                        <div class="col-sm-6 col-12 md-form mb-3">
                           <label class="label">{{form.password1.label}}</label>
                           {{form.password1}} {{form.password1.errors}}
                        </div>
                        <div class="col-sm-6 col-12 md-form mb-3">
                           <label class="label">{{form.yard.label}}</label>
                           {{form.yard}} {{form.yard.errors}}
                        </div>
                        <div class="col-sm-6 col-12 md-form mb-3">
                           <label id="label_role" class="label">{{form.role.label}}</label>
                           {{form.role}} {{form.role.errors}}
                        </div>
                        <div class="col-sm-12 col-12 md-form pt-2">
                           <button type="submit" id="submit_user" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate "Save" %}</button>
                        </div>
                      </div>
                     </form>
                </div>
             </div>
          </div>
       </div>
    </div>
  </div>
</div>
{% endblock %}

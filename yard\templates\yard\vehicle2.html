{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% load static %}
{% block content %}
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              {% if master_container.status == False %}
                <p>{% translate 'Vehicles' %}</p>
                {% else %}
                <p>Container</p>
              {% endif %}
            </div>
          </div>
        </div>
        <!--table strat-->
        <div class="row">
          <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
              <label>{% translate "Show entries" %}:</label>
             <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
             </select>
           </div>
           
           <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
               <label>{% translate "Search" %}:</label>
               <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
           </div>
           
           {%comment %}<div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12">
             <form action="">
            <label>{% translate "Fahrzeugtypen" %}:</label>
            <select class="form-control w-30" id="showentries" name="vehicle_type">
              <option value="all">Alle</option>
              <option value="0">Firmenwaage</option>
              <option value="1">Fremdwaage</option>
              entries
           </select>
          </div>
           <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12 pt-md-4">
            <button type="submit" class="btn btn-primary" style="margin-top:13px;" id="btn_date_selection" name="date_selection">Filter</button>
          </form>
          </div>
          {% endcomment %}
        </div>
        <div class="slider_2">
        <div class="row" style="margin-left: 0px;">
        <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="vehicleTable">
          <thead>
            <tr> {% if request.user.is_superuser %}
              <th width="3%">{% translate "Action" %}</th> {% endif%}
              {% if master_container.status == False %}
                <th width="5%">{% trans "Kenn. Zugmaschine" %}</th>
                <th width="5%">{% trans "Kenn. Trailer" %}</th>
                {% else %}
                <th width="5%">Containernummer</th>
              {% endif %}
              <th width="5%">{{form.forwarder.label}}</th>
              <th width="3%">{{form.owner.label}}</th>
              <th width="3%">{{form.driver_name.label}}</th>
{#              <th width="3%">{{form.vehicle_weight_id.label}}</th>#}
              <th width="5%">{{ "Tara Zugmaschine" }}</th>
              <th width="5%">{{ "Tara Trailer" }}</th>
              {% comment %}<th width="3%">{{form.trailor_weight.label}}</th>{% endcomment %}
              <!-- <th width="3%">{{form.vehicle_type.label}}</th> -->
              {% comment %}<th width="3%">{{form.cost_center.label}}</th>{% endcomment %}
              {% comment %}<th width="5%">{{form.group.label}}</th>{% endcomment %}
              <th width="5%">{{form.country.label}}</th>
              <!-- <th width="5%">{{form.email.label}}</th> -->
              <th width="5%">{{form.vehicle_weight_date.label}}</th>
              <th width="5%">{{form.vehicle_weight_time.label}}</th>
              <th width="10%">{% translate "Updated on" %}</th>
<!--                   <th width="7%">Forwarder</th>
              <th width="5%">Group</th>
              <th width="5%">Country</th>
              <th width="5%">Telephone</th>
              <th width="5%">Vehicle Weight</th>
              <th width="3%">Vehicle Weight Id</th>
              <th width="10%">Updated on</th>
              <th width="5%">Action</th> -->
            </tr>
          </thead>

          <tbody class="mt-4">
            {% for data in dataset %}
            <tr ondblclick="javascript:loadVehicleDetails('{{ data.id }}')" class="loadVD">{% if request.user.is_superuser %}
              <td><a class="loadV" href="javascript:loadVehicleDetails('{{ data.id }}')" ><i class="fas fa-pencil-alt text-primary  ml-4"></i></a><a class="confirmdelete" href="{% url 'vehicle_delete' identifier=data.id  %}"> <i class="fas fa-trash-alt ml-2 text-danger"></i></a></td>
              {% endif %}
              <td>{{ data.license_plate|default_if_none:"-" }}</td>
              {% if master_container.status == False %}
              <td>{{ data.license_plate2|default_if_none:"-" }}</td>
              {% endif %}
              <td>{{ data.forwarder|default_if_none:"-" }}</td>
              <td>{{ data.owner|default_if_none:"-" }}</td>
              <td>{{ data.driver_name|default_if_none:"-" }}</td>
{#              <td>{{ data.vehicle_weight_id|default_if_none:"-" }}</td>#}
              <td>{{ data.vehicle_weight|default_if_none:"-" }}</td>
              <td>{{ data.vehicle_weight2|default_if_none:"-" }}</td>
              {% comment %}<td>{{ data.trailor_weight }}</td>{% endcomment %}
              <!-- <td>{{ data.vehicle_type|default_if_none:"-" }}</td> -->
              {% comment %}<td>{{ data.cost_center }}</td>{% endcomment %}
              {% comment %}<td>{{ data.group }}</td>{% endcomment %}
              <td>{{ data.country|default_if_none:"-" }}</td>
              <!-- <td>{{ data.email|default_if_none:"-" }}</td> -->
              <td>{{ data.vehicle_weight_date|default_if_none:"-" }}</td>
              <td>{{ data.vehicle_weight_time|default_if_none:"-" }}</td>
              <td>{{ data.updated_date_time|default_if_none:"-" }}</td>
            </tr>
            {% endfor %}
<!--            <tfoot hidden>-->
<!--                <tr>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text" class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text"class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text" class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text" class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text"class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text" class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text" class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text"class="form-control">-->
<!--                  </th>-->
<!--                  <th rowspan="1" colspan="1">-->
<!--                     <input type="text" class="form-control">-->
<!--                  </th>-->
<!--               </tr>-->
<!--            </tfoot>-->
          </tbody>
          </tbody>

        </table>
      </div>
    </div>
    </div>
    <!--table end-->
    <div class="container">
      <div class="row mb-5">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
          <!-- Material form login -->
          <div class="card" id="vehicle-form-section">
                <h5 class="card-header info-color white-text py-3 mt-3">
                  <div class="panel-heading">
                    <h4 class="panel-title">
                      {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18"> {% endcomment %}

                     {% if request.user.is_superuser %}
                        <div class="row">
                          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 ">
                            {% if master_container.status == False %}
                              <p class="mb-0 pt-2 text-color text_color float-left">{% translate 'Vehicle' %}</p>
                              {% else %}
                              <p class="mb-0 pt-2 text-color text_color float-left">Container</p>
                            {% endif %}
                            <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill" style="float:right;">Neue Eingabe</button>
                          </div>
                          </div>
                    {% endif %}
    
                      {% comment %} </a> {% endcomment %}
                    </h4>
                  </div>
                </h5>

            
            <div id="collapse_18" class="collapse " >
              <div class="panel-body">  
                 <div class="contanier">
                <form class="form-group" method="POST" enctype="multipart/form-data">
                  {% csrf_token %}
                <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  <div class="md-form mb-3">
                    <input type="hidden" name="id" id="id">
                        {% if master_container.status == False %}
                        <label>{% translate "Kennzeichen Zugmaschine" %}</label>
                    {% else %}
                    <label>Containernummer</label>
                  {% endif %}
                    
                    {{ form.license_plate}}{{ form.license_plate.errors}}
<!--                    <input type="License Plate" class="form-control" name="license_plate" id="id_license_plate" required>-->
                  </div>
                  <div class="md-form mb-3">
                    <label>{{ form.forwarder.label}}</label>
                    {{ form.forwarder}}{{ form.forwarder.errors}}
<!--                    <input type="Forwarder" class="form-control" name="forwarder" id="id_forwarder" >-->
                  </div>

                  <div class="md-form mb-3">
                    <label>{{ form.street.label}}</label>
                    {{ form.street}}{{ form.street.errors}}
<!--                    <input type="number" min="0" class="form-control" name="group" id="id_group" >-->
                  </div>
                  <div class="md-form mb-3">
                    <label>{{ form.pin.label}}</label>
                    {{ form.pin}}{{ form.pin.errors}}
<!--                    <input type="number" min="0" class="form-control" name="group" id="id_group" >-->
                  </div>
                  <div class="md-form mb-3">
                    <label>{{ form.place.label}}</label>
                    {{ form.place}}{{ form.place.errors}}
<!--                    <input type="number" min="0" class="form-control" name="group" id="id_group" >-->
                  </div>
                     <div class="md-form mb-3">
                    <label>{{ form.country.label}}</label>
                    {{ form.country}}{{ form.country.errors}}
                  </div>

                  <div class="md-form mb-3">
                    <label>{{ form.owner.label}}</label>
                    {{ form.owner}}{{ form.owner.errors}}
                  </div>
                     
                  <div class="md-form mb-3">
                    <label>{{ form.self_tara.label}}</label>
                    {{ form.self_tara}}{{ form.self_tara.errors}}
                  </div>
                </div>
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                  {% if master_container.status == False %}
                  <div class="md-form mb-3">
                    <label>{% trans 'Kennzeichen Trailer' %}</label>
                    {{ form.license_plate2}}{{ form.license_plate2.errors}}
<!--                    <input type="Telephone" class="form-control" name="telephone" id="id_telephone">-->
                  </div>
                  {% endif %}
                  <!-- <div class="md-form mb-3">
                    <label>{{ form.email.label}}</label>
                    {{ form.email}}{{ form.email.errors}}
                  </div> -->
                  <div class="md-form mb-3">
                    <label>{{ form.vehicle_weight.label}} 1</label>
                    {{ form.vehicle_weight}}{{ form.vehicle_weight.errors}}
                  </div>
                     <div class="md-form mb-3">
                    <label>{{ form.vehicle_weight_date.label}}</label>
                    {{ form.vehicle_weight_date}}{{ form.vehicle_weight_date.errors}}
<!--                    <input type="number" step="any" class="form-control" name="vehicle_weight_id" id="id_vehicle_weight_id">-->
                  </div>
                     <div class="md-form mb-3">
                    <label>{{ form.vehicle_weight_time.label}}</label>
                    {{ form.vehicle_weight_time}}{{ form.vehicle_weight_time.errors}}
<!--                    <input type="number" step="any" class="form-control" name="vehicle_weight_id" id="id_vehicle_weight_id">-->
                  </div>
                  <div class="md-form mb-3">
                   <label>{{ form.vehicle_weight2.label}}</label>
                    {{ form.vehicle_weight2}}{{ form.vehicle_weight2.errors}}
                  </div>
                      <div class="md-form mb-3">
                    <label>{{ form.vehicle_weight_date2.label}} 2</label>
                    {{ form.vehicle_weight_date2}}{{ form.vehicle_weight_date2.errors}}
<!--                    <input type="number" step="any" class="form-control" name="vehicle_weight_id" id="id_vehicle_weight_id">-->
                  </div>

                  <div class="md-form mb-3">
                    <label>{{ form.vehicle_weight_time2.label}} 2</label>
                    {{ form.vehicle_weight_time2}}{{ form.vehicle_weight_time2.errors}}
<!--                    <input type="number" step="any" class="form-control" name="vehicle_weight_id" id="id_vehicle_weight_id">-->
                  </div>
                  <div class="md-form mb-3">
                    <label>{{ form.driver_name.label}}</label>
                    {{ form.driver_name}}{{ form.driver_name.errors}}
                  </div>
{#                  <div class="md-form mb-3">#}
{#                    <label>{% translate "Fahrzeuggewicht Zugmaschine Id" %}</label>#}
{#                    {{ form.vehicle_weight_id}}{{ form.vehicle_weight_id.errors}}#}
{#<!--                    <input type="number" step="any" class="form-control" name="vehicle_weight_id" id="id_vehicle_weight_id">-->#}
{#                  </div>#}
{#                  <div class="md-form mb-3">#}
{#                    <label>{% translate "Fahrzeuggewicht Trailer Id" %} 2</label>#}
{#                    {{ form.vehicle_weight_id2}}{{ form.vehicle_weight_id2.errors}}#}
{#<!--                    <input type="number" step="any" class="form-control" name="vehicle_weight_id" id="id_vehicle_weight_id">-->#}
{#                  </div>#}

                
                  
                  <input type="hidden" name="taken" id="id_taken" value=0>
                  <button id="submit" onclick="validateVeh()" type="submit" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate "Save2" %}</button>
                  </div>
                  
                </div>

                </form>

              </div></div>
            </div>

          </div>

        </div>
      </div>
    </div>

{% endblock %}

{% block scripts %}

<script>
function check_checkbox(){
  if($('#id_self_tara').is(":checked")){
    $('#id_vehicle_weight').attr('required','required');
    $('#id_vehicle_weight_date').attr('required','required');
    $('#id_vehicle_weight_time').attr('required','required');
    return true;
  }else{
      $('#id_vehicle_weight').removeAttr('required');
    $('#id_vehicle_weight_date').removeAttr('required');
    $('#id_vehicle_weight_time').removeAttr('required');
    return false;
  }
}



function validateVeh(){
  var result = check_checkbox();
  $('#button').attr('type','submit').click();
}
    // DataTable
    $(document).ready(function() {
      var table = $('#vehicleTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
//                    console.log(that)
// <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
// <!--                       if ( that.search() !== this.value ) {-->
// <!--                           that.search( this.value ).draw();-->
// <!--                       }-->
// <!--                   } );-->
               } );
           }
          });

      $("#vehicleTable_filter").hide()

      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      } );

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      } );

 });

{% if request.user.is_superuser %}
    $(".loadV").click(function () {
        window.location = "#vehicle-form-section";
        $("#collapse_18").addClass('show')
    });

    $(".loadVD").dblclick(function () {
        window.location = "#vehicle-form-section";
        $("#collapse_18").addClass('show')
    });
{% endif %}

  
   $("#new_entry").click(function(e){
     $("#collapse_18").addClass('show')
    //e.preventDefault();
    $('input#id_trailor_weight').val('0');
    $('#id_forwarder').prop('selectedIndex',0);
    //$('select').val('');
    return false;
  })
</script>
{% endblock %}

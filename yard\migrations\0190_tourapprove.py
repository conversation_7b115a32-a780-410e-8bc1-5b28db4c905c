# Generated by Django 3.1.1 on 2023-09-27 12:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0189_notification_weight'),
    ]

    operations = [
        migrations.CreateModel(
            name='TourApprove',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vehicle', models.CharField(blank=True, max_length=250, null=True)),
                ('tour_number', models.CharField(blank=True, max_length=250, null=True)),
                ('status', models.BooleanField(blank=True, default=None, null=True)),
                ('time', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]

{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% load static %}
{% block content %}
      <div class="container">
        <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
          <i class="fas fa-align-justify"></i>
        </button>
        <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
          <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
            <div class="content_text">
              <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
              <p>{{ "Auswertung" }}</p>
            </div>
          </div>
        </div>
        <!--table strat-->
       <!--table end-->
      {%if error%}
        <div class="alert alert-danger" role="alert">
          {{error}}
        </div>
      {%endif%}

    <div class="container">
        <div claas="card mt-3">
          <div class="panel-body">
              <form class="form-group" method="POST" id="sp_form" enctype="multipart/form-data" name="form1" >
                {% csrf_token %} 
                <div class="row">               
                  <div class="col-12">
                    <div class="mt-4 borde border-top border-right border-bottom border-left">
                    <div class="row m-0">
                      <div class="col-sm-6 col-12 pt-2 pb-2">
                          <label class="label">{% if request.session.customer%} {{request.session.customer}} {%else%} {% translate 'Customer' %} {%endif%} </label>
                          <select id="sp_cust" class="form-control select2" name="customer" aria-label="default select">
                            <option value='0'>{%if request.session.customer %}{{request.session.customer}} &nbsp;{% trans 'Select2' %} {% else %}{% trans 'Select Customer' %}{%endif%} </option>
                            <option value="-1">Alle</option>
                            {% for i in customer %}
                            <option value='{{i.pk}}'>{{i.name1}}</option>
                            {% endfor %}
                          </select>
                      </div>
                      <div class="col-sm-6 col-12 pt-2 pb-2">
                          <label class="label">{% if request.session.vehicle%} {{request.session.vehicle}} {%else%} {% translate 'Vehicle' %} {%endif%} </label>
                          <select id="sp_veh" class="form-control select2" name="vehicle" aria-label="default select">
                            <option value='0'>{%if request.session.vehicle %}{{request.session.vehicle}} &nbsp;{% trans 'Select2' %} {% else %}{% trans 'Select Vehicle' %}{%endif%} </option>
                            {% for i in vehicle %}
                            <option value='{{i.pk}}'>{{i.license_plate}}</option>
                            {% endfor %}
                          </select>
                      </div>
                      <div class="col-sm-6 col-12 pt-3 pb-2">
                          <label class="label">{% if request.session.supplier%} {{request.session.supplier}} {%else%} {% translate 'Supplier' %} {%endif%} </label>
                          <select id="sp_sup" class="form-control select2" name="supplier" aria-label="default select">
                            <option value='0'>{%if request.session.supplier %}{{request.session.supplier}} &nbsp;{% trans 'Select2' %} {% else %}{% trans 'Select Supplier' %}{%endif%} </option>
                          <option value="-1">Alle</option>
                            {% for i in supplier %}
                            <option value='{{i.pk}}'>{{i.supplier_name}}</option>
                            {% endfor %}
                          </select>
                      </div>
                      <div class="col-sm-6 col-12 pt-1 pb-2">
                          <label class="col-sm-2">{% if request.session.article%} {{request.session.article}} {%else%} {% translate 'Material' %} {%endif%} </label>
                          <select id="sp_art" class="form-control select2 " name="article" >
                            <option value='0'>{%if request.session.article %}{{request.session.article}} &nbsp;{% trans 'Select2' %} {% else %}{% trans 'Select Article' %}{%endif%} </option>
                          <option value="-1">Alles</option>
                            {% for i in article %}
                            <option value='{{i.pk}}'>{{i.name}}</option>
                            {% endfor %}
                            </select>
                      </div>
                     <div class="col-sm-6 col-12 pb-2">
                          <label class="col-sm-2" style="margin-left: -10px;"> {% translate 'Auftrag' %} </label>
                          <select id="sp_contract" class="form-control select2 " name="contract" >
                            <option value='0'>{% trans 'Auftrag auswählen' %} </option>
                          <option value="-1">Alle</option>
                            {% for i in contract %}
                            <option value='{{i.pk}}'>{{i.name}}</option>
                            {% endfor %}
                            </select>
                      </div>
                       <di class="col-sm-6 col-12 pb-2">
                          <label class="col-sm-2" style="margin-left: -10px;"> {% translate 'Forwarders' %} </label>
                          <select id="sp_forwarders" class="form-control select2 " name="forwarders" >
                            <option value='0'>{% trans 'Spedition auswählen' %} </option>
                          <option value="-1">Alle</option>
                            {% for i in forwarders %}
                            <option value='{{i.pk}}'>{{i.name}}</option>
                            {% endfor %}
                            </select>
                      </di>
                    </div>
                  <hr>
                  <div class="row m-0">
                    <div class="col-sm-6 col-12">
                      <div class="row">
                      <div class="col-sm-12 col-12">
                        <p class="pt-0 pb-0">
                          {% translate 'Date' %}
                        </p>                    
                      </div>
                      <div class="col-sm-12 col-12">
                        <div class="row">
                          <div class="col-12">
                            <input type="date" class="form-control"  id='from-date' name="fromdate">   
                          </div>
                          <div class="col-12 pt-2 pb-2 text-center">
                            {% translate 'To' %}
                          </div>
                          <div class="col-12">
                            <input type="date" class="form-control" id="to-date" name="todate">   
                          </div>
                        </div> 
                      </div>
                      <div class="col-sm-12 col-12 my-3">
                        <button type="submit" class="btn btn-primary mb-2" name="excel" id="excel"><i class="fas fa-th-list"></i> {% translate 'Generate Excel' %}</button>
                        <button type="submit" class="btn btn-primary mb-2 ml-1" name="pdf" id="pdf"><i class="fas fa-print"></i> {% translate 'Generate Report' %}</button>
                        <button type="button" class="btn btn-primary mb-2 ml-1" id="download_devaluation" onclick="zip_delivery_notes()"><span id="delivery-notes-loader"></span> <i class="fas fa-file-archive"></i> {% translate 'Als pdf exportieren' %}</button>                        
                      </div>
                    </div>
                    </div>
                  </div>
                </div>
            
              </div>
              {% comment %} </div> {% endcomment %}
              </form>
            </div>
          </div>
        </div>
          </div>
      </div>
    </div>
        
        <script  src="{% static 'yard/js/jquery.min.js'%}"></script>
<script src="{% static 'yard/js/index.js'%}"></script>
<script type="text/javascript" src="{% static 'stats/js/jszip.js'%}"></script>

<script>
  $("#pdf").click(function(){
    $("#sp_form").prop('target','_blank')
  })

  $('#sp_form').on('submit',function(){
    c = $("#sp_cust").val();
    v = $("#sp_veh").val();
    s = $("#sp_sup").val();
    a = $("#sp_art").val();
    sped = $("#sp_forwarders").val()
    contract_val = $("#sp_contract").val();
    
    if ( c == '0' && v == '0' && s == '0' && a == '0' && sped == '0' && contract_val == '0'){
      alert('Bitte wählen Sie eine aus der Liste aus')
      return false
    }
  })
  
function zip_delivery_notes(){
    c = $("#sp_cust").val();
    v = $("#sp_veh").val();
    s = $("#sp_sup").val();
    a = $("#sp_art").val();
    sped = $("#sp_forwarders").val()
    contract_val = $("#sp_contract").val();

    
    csrf = $("input[name='csrfmiddlewaretoken']").val();
    from_date = $("#from-date").val();
    to_date = $("#to-date").val();
    {% comment %} if ( c == '0' && v == '0' && s == '0' && a == '0' && sped == '0'){
      alert('Bitte wählen Sie eine aus der Liste aus')
    return false
  }else{ {% endcomment %}
      $("#delivery-notes-loader").html("<span style='height:15px;width:15px;' class='delivery-loader spinner-border'></span> ");
      $("#download_devaluation").attr('disabled',true);
      $.ajax({
        url:"/stats/get_delivery_notes/",
        type:'get',
        data:{csrfmiddlewaretoken:csrf,fromdate:from_date, todate:to_date,customer:c, contract: contract_val, forwarders: sped,
        vehicle:v, supplier:s,article:a,fromdate:from_date,todate:to_date,export_special_ev:""},
        success:function(data){
          console.log(data);
          var zip = new JSZip();
          if(data.status==0){
            alert("keine Daten gefunden!");
            return
          }
          if(data.length>0){
            for(i in data){
              var length=data[i].pdf.length;
              pdf=data[i].pdf.substring(1, length-2);
              const linkSource = `data:application/pdf;base64,${pdf}`;
              const downloadLink = document.createElement("a");
              const fileName = data[i].id+".pdf";
                zip.file(fileName, data[i].pdf, {base64: true})
            }
              zip.generateAsync({
                    type: "base64"
                }).then(function(content) {
                    window.location.href = "data:application/zip;base64," + content;
                }); 
          }else{
            alert("keine Daten gefunden!");
          }
            
          $(".delivery-loader").removeClass("spinner-border");  
          $("#download_devaluation").attr('disabled',false);
        }
      });
      {% comment %} } {% endcomment %}
  }

</script>
  {% endblock %}

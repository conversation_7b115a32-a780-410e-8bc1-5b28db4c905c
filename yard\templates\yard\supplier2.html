{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}
<div class="container">
  <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
  <i class="fas fa-align-justify"></i>
  </button>
  <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
      <div class="content_text">
        <p class="mb-0">{% translate 'OVERVIEW' %}</p>
      </div>
      <div class="heding">
        {% if master_container.status == False %}
          <p>{% if request.session.supplier%} {{ request.session.supplier}} {% else %} {% translate 'Supplier' %} {% endif%}</p>
          {% else %}
          <p>Gebäude</p>
        {% endif %}
      </div>
    </div>
  </div>
  <!--table strat-->
  <div class="row">
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
      <label>{% translate "Show entries" %}:</label>
      <select class="form-control w-30" id="showentries">
        <option>10</option>
        <option>25</option>
        <option>50</option>
        <option>100</option>
        entries
      </select>
    </div>
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
      <label>{% translate "Search" %}:</label>
      <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="SupplierSearch">
    </div>
  </div>
  <div class="slider_2">
  <div class="row">
    <table class="table table-striped table-hover table-bordered ml-4 mt-4 building_site" width="100%" id="supplierTable">
      <thead>
        <tr> {% if request.user.is_superuser %}
           <th width="5%">{% translate "Action" %}</th> {% endif %}
          <th width="7%">{{ "Bezeichnung" }}</th>
        <th width="5%">{% translate 'Projektnummer' %}</th>
          {% comment %}<th width="7%">{{form.first_name.label}}</th>{% endcomment %}
          <th width="7%">{{form.name.label}}</th>
          <th width="5%">{{form.street.label}}</th>
          <th width="5%">{{form.pin.label}}</th>
          {% comment %} <th width="5%">{{form.post_office_box.label}}</th> {% endcomment %}
          <th width="5%">{{form.place.label}}</th>
          <th width="5%">{{form.country.label}}</th>
          {% comment %}<th width="5%">{{form.fax.label}}</th>{% endcomment %}
          <th width="5%">{{ "Telefon" }}</th>
          {% comment %}<th width="5%">{{form.contact_person2_phone.label}}</th>{% endcomment %}
          {% comment %}<th width="5%">{{form.contact_person3_phone.label}}</th>{% endcomment %}
          <th width="5%">{{ "E-Mail" }}</th>
          {% comment %}<th width="5%">{{form.contact_person2_email.label}}</th>{% endcomment %}
          {% comment %}<th width="5%">{{form.contact_person3_email.label}}</th>{% endcomment %}
          {% comment %}<th width="5%">{{form.website.label}}</th>{% endcomment %}
          {% comment %}<th width="5%">{{form.warehouse.label}}</th>{% endcomment %}
          {% comment %}<th width="5%">{{form.cost_centre.label}}</th>{% endcomment %}
          {% comment %}<th width="5%">{{form.creditor_number.label}}</th>{% endcomment %}
          <th width="5%">{{form.addition1.label}}</th>
          <th width="5%">{{form.addition2.label}}</th>
          <th width="5%">{{form.addition3.label}}</th>

        </tr>
      </thead>
      <tbody class="mt-4">
        {% for data in dataset %}
        <tr class="loadD" ondblclick="javascript:loadSupplierDetails('{{ data.id }}');"> {% if request.user.is_superuser %}
          <td><a class="load" href="javascript:loadSupplierDetails('{{ data.id }}')" ><i class="fas fa-pencil-alt text-primary  ml-4"></i></a><a class="confirmdelete"  href="{% url 'supplier_delete' identifier=data.id  %}" ><i class="fas fa-trash-alt ml-2 text-danger"></i></a></td>
          {% endif %}
          <td>{{ data.supplier_name|default_if_none:"-" }}</td>
         <td>{{ data.project_number|default_if_none:"-" }}</td>
          {% comment %}<td>{{ data.first_name }}</td>{% endcomment %}
          <td>{{ data.name|default_if_none:"-" }}</td>
          <td>{{ data.street|default_if_none:"-" }}</td>
          <td>{{ data.pin|default_if_none:"-" }}</td>
          {% comment %} <td>{{ data.post_office_box }}</td>           {% endcomment %}
          <td>{{ data.place|default_if_none:"-" }}</td>
          <td>{{ data.country|default_if_none:"-" }}</td>
          {% comment %}<td>{{ data.fax }}</td>{% endcomment %}
          <td>{{ data.contact_person1_phone|default_if_none:"-" }}</td>
          {% comment %}<td>{{ data.contact_person2_phone }}</td>{% endcomment %}
          {% comment %}<td>{{ data.contact_person3_phone }}</td>{% endcomment %}
          <td>{{ data.contact_person1_email|default_if_none:"-" }}</td>
          {% comment %}<td>{{ data.contact_person2_email }}</td>{% endcomment %}
          {% comment %}<td>{{ data.contact_person3_email }}</td>{% endcomment %}
          {% comment %}<td>{{ data.website }}</td>{% endcomment %}
          {% comment %}<td>{{ data.warehouse }}</td>{% endcomment %}
          {% comment %}<td>{{ data.cost_centre }}</td>{% endcomment %}
          {% comment %}<td>{{ data.creditor_number }}</td>{% endcomment %}
          <td>{{ data.addition1|default_if_none:"-" }}</td>
          <td>{{ data.addition2|default_if_none:"-" }}</td>
          <td>{{ data.addition3|default_if_none:"-" }}</td>
          </tr>
        {% endfor %}
<!--      <tfoot hidden>-->
<!--        <tr>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text"class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text"class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text"class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--        </tr>-->
<!--      </tfoot>-->
      </tbody>
    </table>
  </div>
</div>
<!--table end-->
<div class="container">
  <div class="row mb-5">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <!-- Material form login -->
      <div class="card" id="supplier-form-section">
       {% if request.user.is_superuser %}
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3 mt-4">
              <div class="panel-heading">
                <h4 class="panel-title">
                  {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18"> {% endcomment %}
                    <div class="row">
                      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                        {% if master_container.status == False %}
                          <p class="mb-0 pt-2 text-color text_color float-left">{% if request.session.supplier%} {{ request.session.supplier}} {% else %} {% translate 'Supplier' %} {% endif%}</p>
                          {% else %}
                          <p class="mb-0 pt-2 text-color text_color float-left">Gebäude</p>
                        {% endif %}
                        <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill" style="float:right;">Neue Eingabe</button>
                      </div>
                    </div>
                  {% comment %} </a> {% endcomment %}
                </h4>
              </div>
            </h5>
          </div>
        </div>
      {% endif %}
        <div id="collapse_18" class="collapse" >
          <div class="panel-body">
            <div class="card-body text-left">
                
              <form class="form-group" method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" name="id" id="id">
                <div class="row">
                  <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                    <div >
                      <label>{{ "Bezeichnung" }}</label>
                      {{ form.supplier_name}}{{ form.supplier_name.errors}}
                    </div>
                    {% comment %}
                    <div >
                      <label>{{form.salutation.label}}</label>
                      {{ form.salutation}}{{ form.salutation.errors}}
                    </div>

                    <div >
                      <label>{{form.first_name.label}}</label>
                      {{ form.first_name}}{{ form.first_name.errors}}
                    </div>
                    {% endcomment %}
                    <div>
                      <label>{{form.name.label}}</label>
                      {{ form.name}}{{ form.name.errors}}
                    </div>
                    <div>
                      <label>{{form.street.label}}</label>
                      {{ form.street }}{{ form.street.errors }}
                    </div>
                    <div>
                      <label>{{form.pin.label}}</label>
                      {{ form.pin }}{{ form.pin.errors }}
                    </div>
                    <div>
                      <label>{{form.place.label}}</label>
                      {{ form.place }}{{ form.place.errors }}
                    </div>
                    <div >
                      <label>{{form.country.label}}</label>
                      {{ form.country }}{{ form.country.errors }}
                    </div>
                     <div >
                      <label>{{ "Telefon" }}</label>
                      {{ form.contact_person1_phone }}{{ form.contact_person1_phone.errors }}
                    </div>
                    {% comment %}
                    <div >
                      <label>{{form.contact_person2_phone.label}}</label>
                      {{ form.contact_person2_phone }}{{ form.contact_person2_phone.errors }}
                    </div>
                    <div >
                      <label>{{form.contact_person3_phone.label}}</label>
                      {{ form.contact_person3_phone }}{{ form.contact_person3_phone.errors }}
                    </div>
                    <div >
                      <label>{{form.creditor_number.label}}</label>
                      {{ form.creditor_number }}{{ form.creditor_number.errors }}
                    </div>
                    {% endcomment %}
                  </div>
                  <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
                    {% comment %}
                    <div >
                      <label>{{form.fax.label}}</label>
                      {{ form.fax}}{{ form.fax.errors }}
                    </div>
                    {% endcomment %}
                    <div >
                      <label>{{form.addition1.label}}</label>
                      {{ form.addition1}}{{ form.addition1.errors }}
                    </div>
                    <div >
                      <label>{{form.addition2.label}}</label>
                      {{ form.addition2 }}{{ form.addition2.errors }}
                    </div>
                    <div >
                      <label>{{form.addition3.label}}</label>
                      {{ form.addition3 }}{{ form.addition3.errors }}
                    </div>
                    {% comment %}
                    <div >
                      <label>{{form.post_office_box.label}}</label>
                      {{ form.post_office_box}}{{ form.post_office_box.errors }}
                    </div>
                    <div >
                      <label>{{form.website.label}}</label>
                      {{ form.website }}{{ form.website.errors }}
                    </div>
                    <div >
                      <label>{{form.cost_centre.label}}</label>
                      {{ form.cost_centre }}{{ form.cost_centre.errors }}
                    </div>
                    <div >
                      <label>{{form.warehouse.label}}</label>
                      {{ form.warehouse }}{{ form.warehouse.errors }}
                    </div>
                    {% endcomment %}
                    <div>
                      <label>{{ "E-Mail" }}</label>
                      {{ form.contact_person1_email }}{{ form.contact_person1_email.errors }}
                    </div>
                    <div>
                      <label>{% translate 'Projektnummer' %}</label>
                      {{ form.project_number }}{{ form.project_number.errors }}
                    </div>
                    {% comment %}
                    <div >
                      <label>{{form.contact_person2_email.label}}</label>
                      {{ form.contact_person2_email }}{{ form.contact_person2_email.errors }}
                    </div>
                    <div >
                      <label>{{form.contact_person3_email.label}}</label>
                      {{ form.contact_person3_email }}{{ form.contact_person3_email.errors }}
                    </div>
                    {% endcomment %}
                  </div>
                  <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12"><br>
                    <button type="submit" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate "Save2" %}</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
{% block scripts %}
<script>
  // DataTable
  $(document).ready(function() {
    var table = $('#supplierTable').DataTable(
       {
          "bLengthChange": false,
         initComplete: function () {
             // Apply the search
                   this.api().columns().every( function () {
                   var that = this;
//                    console.log(that)
// <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
// <!--                       if ( that.search() !== this.value ) {-->
// <!--                           that.search( this.value ).draw();-->
// <!--                       }-->
// <!--                   } );-->
               } );
         }
        });
  
    $("#supplierTable_filter").hide()
  
    // custom search filter
    $('#SupplierSearch').on( 'keyup', function () {
        table.search( this.value ).draw();
    } );
  
    //  custom show entries
    $('#showentries').change(function() {
        table.page.len(this.value).draw();
    } );
  
  });

  {% if request.user.is_superuser %}
      $(".load").click(function () {
          $("#collapse_18").addClass('show');
      })
      $(".loadD").dblclick(function () {
          $("#collapse_18").addClass('show');
      })
  {% endif %}

   $("#new_entry").click(function(e){
     $("#collapse_18").addClass('show')
    //e.preventDefault();
    //$('input').val('');
    //$('select').val('');
    return false;
  })
  
  
</script>
{% endblock %}

<!DOCTYPE html>
<html lang="en">
<head>
    {% block head %}
        {% load static %}
    {% endblock %}
    {% load i18n %}
    {% load l10n %}
    {% load custom_template_filters %}
    <meta charset="utf-8">
    <title>Delivery Note</title>
    <style>
        @page  {
            margin: 20;
            size: 220mm 550mm; /*or width x height 150mm 50mm*/
            }
        body {
            font-size: 40px;
        }

        .Row {
            display: table;
            width: 100%; /*Optional*/
            table-layout: fixed; /*Optional*/
            border-spacing: 40px; /*Optional*/
        }

        .Column {
            display: table-cell;
            border-left: 10px;
        }



    </style>
</head>
<body>
<div class="a4_sheet">
    {% if logo.logo.url is not None %}
    <div style="display: flex; flex-direction: row;">
        <div style="margin-top: 100px; text-align: center;">
            <img src="{{ logo.logo.url }}" style="width:200px; height: 150px;"/>
        </div>
    </div>
    {% endif %}
    <br>
    <div style="margin-top: 5px; text-align: center; font-size: 45px;">
        {% if heading is not None %}
        {% for head in heading %}
            {{ head }} <br/>
        {% endfor %}
        {% endif %}
    </div>
    <br>
    <div>
        <table style="width:100%">
            
            <tr style="height: 80px;">
                <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                    Datum:
                </td>
                <td colspan="2" style="padding-left: 50px;">
                    {{ dataset.updated_date_time|date:"d.m.y" }}
                </td>
            </tr>
            <tr style="height: 80px;">
                <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                    Uhrzeit:
                </td>
                <td colspan="2" style="padding-left: 50px;">
                     {{ dataset.updated_date_time|time:"H:i" }}
                </td>
            </tr>

            <tr style="height: 80px;">
                <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                    lfd. Nr:
                </td>
                <td colspan="2" style="padding-left: 50px;">
                    {% if dataset.lfd_nr is not None and dataset.lfd_nr != "" %}
                        {{ dataset.lfd_nr }}
                    {% else %}
                        {{ dataset.id }}
                    {% endif %}
                </td>
            </tr>

            <tr style="height: 80px;">
                <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                    Fahrzeug:
                </td>
                <td colspan="2" style="padding-left: 50px;">
                    {{ dataset.vehicle.license_plate }}
                </td>
            </tr>

            <tr style="height: 80px;">
                <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                    Projektnummer:
                </td>
                <td colspan="2" style="padding-left: 50px;">
                    {{ dataset.contract_number.project_number|default_if_none:"-" }}
                </td>
            </tr>

            <tr style="height: 80px;">
                <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                    Artikel:
                </td>
                <td colspan="2" style="padding-left: 50px;">
                    {% if dataset.article.name is not None %}
                        {{ dataset.article.name }}
                    {% else %}
                        {{ "-" }}
                    {% endif %}
                </td>
            </tr>
            
            <tr style="height: 80px;">
            <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                {{ customer }}:
            </td>
            <td colspan="2" style="padding-left: 50px;">
                {% if dataset.customer.name1 is not None %}
                    {{ dataset.customer.name1 }}

                {% else %}
                    {{ "-" }}
                {% endif %}
            </td>
        </tr>
        {% if dataset.customer.name1 is not None %}
        <tr style="height: 80px;">
            <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%"></td>
            <td colspan="2" style="padding-left: 50px;">
                {% if dataset.customer.place is not None %}
                    {{ dataset.customer.place|space }}
                {% endif %}
            </td>
        </tr>
        {% endif %}

        <tr style="height: 80px;">
            <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%">
                {% if dataset.vehicle_weight_flag == 1 or dataset.vehicle_second_weight_flag == 1 %} {% if dataset.vehicle_weight_flag == 1%} Tarawiegung {% else %} Bruttowiegung {% endif %} {% else %} Erstwiegung {% endif %}:
            </td>
            <td colspan="2" style="font-size: 40px; padding-left: 50px;">{% if dataset.vehicle_weight_flag == 1 %} PT{%endif%} {{ dataset.first_weight }} kg</td>
        </tr>

        <tr style="height: 80px;">
            <td colspan="2" style="width:30%; text-align:left;padding-left:5px; height:30%">
                {% if dataset.vehicle_weight_flag == 1 or dataset.vehicle_second_weight_flag == 1 %} {% if dataset.vehicle_second_weight_flag == 1 %} Tarawiegung {% else %} Bruttowiegung {% endif %} {% else %} Zweitwiegung {% endif %}:
            </td>
            <td colspan="2" style="font-size: 40px; padding-left: 50px;">{% if dataset.vehicle_second_weight_flag == 1 %} PT {%endif%}  {{ dataset.second_weight }} kg</td>
        </tr>

        <tr style="height: 80px;">
            <td colspan="2" style="width:30%;text-align:left;padding-left:5px;height:30%; font-weight: 600;">Netto:</td>
            <td colspan="2" style="font-size: 35px; padding-left: 50px; font-weight: 600;">E {{ dataset.net_weight }} kg</td>
        </tr>
        </table>
    </div>


   <br>
    <strong style="margin-top: 5px; font-size: 35px;">
        Messwerte aus frei programmierbarer Zusatzeinrichung. Die geeichten Messwerte können eingesehen werden
    </strong>


    <div style="margin-left: 200px;">
        <img src="{{driver_sign.image.url}}" style="width: 600px; height: 390px;"/><br/>
       <strong style="font-size: 15pt; font-size: 28px; margin-left: 400px;">UNTERSCHRIFT</strong>
    </div>

</div>
</body>
</html>

from django.conf import settings
from django.shortcuts import render
from django.shortcuts import (get_object_or_404, render, HttpResponseRedirect) 
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth import login
from django.contrib.auth.decorators import login_required
from yard.render import Render

from yard.models import *
from yard.forms import *
from django.http import JsonResponse
from django.forms.models import model_to_dict
from django.core.serializers import serialize
import json
from datetime import datetime,date, timedelta
from django.db.models import Sum

@login_required(redirect_field_name=None)
def std_evaluation(request):
	context={}
	absolute_url = request.build_absolute_uri('?')
	context["absolute_url"] =  "http://"+request.get_host()

	if request.POST:
		note_type = request.POST.getlist('note_type')
		grouping = request.POST.get('grouping')
		fromdate = request.POST.get('fromdate')

		if fromdate:
			context['from'] = datetime.strptime(fromdate,"%Y-%m-%d")
			fromdate = fromdate+" 00:00:00"
		else:
			fromdate = str(date.today())+" 00:00:00"

		todate = request.POST.get('todate')
		if todate:
			context['to'] =datetime.strptime(todate,"%Y-%m-%d") 
			todate = todate +" 23:59:59"
		else:
			todate=str(date.today())+" 23:59:59"

		article_from = request.POST.get('article_from')
		article_to = request.POST.get('article_to')
		print ("Grouping:",grouping)
		obj = Transaction.objects.filter(created_date_time__range=(fromdate,todate))
		# obj = Transkation.objects.raw('SELECT *,SUM(net_weight) as ttl FROM yard_transkation WHERE created_date_time BETWEEN %s and %s GROUP BY(article_id)',[fromdate,todate])
		sum_kg = 0
		if obj:
			if grouping=='article':
				# context["data"] = get_article_groupset(obj)
				context['date'] = datetime.now()
				context["data"] = obj
				context["art_list"] = obj.values_list("article","article__description").distinct()
				art_sum = []
				context["art_list"] = [list(i) for i in context["art_list"]]
				for i in context["art_list"]:
					i.append(sum(j.net_weight for j in obj if j.article.pk==i[0]))
				context["art_sum"]= art_sum
				return Render.render("stats/pdf/article_report.html", context)
			elif grouping=='art-cus':
				print ("article customer")
				context["data"] = obj
				context['date'] = datetime.now()
				context["art_list"] = obj.values_list("article","article__description").distinct()
				context["cus_list"] = obj.values_list("customer","customer__name").distinct()
				art_sum = []
				context["art_list"] = [list(i) for i in context["art_list"]]
				context["cus_list"] = [list(i) for i in context["cus_list"]]
				context["summ"] = []
				for i in context["art_list"]:
					for k in context["cus_list"]:
						temp = {}
						for j in obj:
							summ=0
							temp["article"] = i[0]
							temp["customer"] = k[0]
							# if j.article.pk==i[0] and j.customer.pk==k[0]:
								# summ=summ+j.net_weight
							temp["sum"] = sum(j.net_weight for j in obj if j.article.pk==i[0] and j.customer.pk==k[0])
						context["summ"].append(temp)
				return Render.render("stats/pdf/art_cus_report.html", context)
			elif grouping=='cus-art':
				print ("customer article")
				context["data"] = obj
				context['date'] = datetime.now()
				context["art_list"] = obj.values_list("article","article__description").distinct()
				context["cus_list"] = obj.values_list("customer","customer__name").distinct()
				context["art_list"] = [list(i) for i in context["art_list"]]
				context["cus_list"] = [list(i) for i in context["cus_list"]]
				context["summ"] = []
				for i in context["cus_list"]:
					for k in context["art_list"]:
						temp = {}
						for j in obj:
							summ=0
							temp["article"] = k[0]
							temp["customer"] = i[0]
							# if j.article.pk==i[0] and j.customer.pk==k[0]:
								# summ=summ+j.net_weight
							temp["sum"] = sum(j.net_weight for j in obj if j.article.pk==k[0] and j.customer.pk==i[0])
						context["summ"].append(temp)
						# context["sum"].append(sum(j.net_weight for j in obj if j.article.pk==i[0] and j.customer.pk==k[0]))
				return Render.render("stats/pdf/cus_art_report.html", context)
				context["data"] = obj
		else:
			context['error'] = "No Transkations found!"
			return render(request, "stats/std_evaluation2.html", context)
	return render(request, "stats/std_evaluation2.html", context)

@login_required(redirect_field_name=None)
def daily_delivery_list(request):
	context = {}
	form = TransactionForm(request.POST or None)

	if request.POST:
		if 'print_button' in request.POST:
			context_transaction = transaction_update(request)
			return Render.render("yard/pdf_template.html", context_transaction)
		elif 'save_button' in request.POST:
			id = request.POST.get('id')
			obj = Transaction.objects.get(id=id)
			form = TransactionForm(request.POST, instance=obj)
			if form.is_valid():
				form.save()
			else:
				print("error", form.errors)
	fromdate = str(date.today()) + " 00:00:00"
	todate = str(date.today()) + " 23:59:59"
	context["form"] = form
	context["dataset"] = Transaction.objects.filter(updated_date_time__range=(fromdate,todate),trans_flag=1, yard = request.user.yard)
	return render(request, "stats/deliverynotes2.html", context)

@login_required(redirect_field_name=None)
def deliverynotes(request):
	context = {}
	form = TransactionForm(request.POST or None)
	if request.POST:
		if 'print_button' in request.POST:
			context_transaction = transaction_update(request)
			return Render.render("yard/pdf_template.html", context_transaction)
		elif 'date_selection' in request.POST:
			fromdate = request.POST.get('fromdate')
			if fromdate:
				context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
				fromdate = fromdate + " 00:00:00"
			else:
				fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

			todate = request.POST.get('todate')
			if todate:
				context['to'] = datetime.strptime(todate, "%Y-%m-%d")
				todate = todate + " 23:59:59"
			else:
				todate = str(date.today()) + " 23:59:59"

			context["form"] = form
			context["dataset"] = Transaction.objects.filter(updated_date_time__range=(fromdate, todate),trans_flag=2, yard = request.user.yard)
			return render(request, "stats/deliverynotes2.html", context)

		else:
			id = request.POST.get('id')
			if id:
				obj = Transaction.objects.get(id=id)
				form = TransactionForm(request.POST, instance=obj)
				if form.is_valid():
					form.save()
				else:
					print("error", form.errors)

	# else:
	# 	context["form"] = form
	# 	context["dataset"] = Transkation.objects.all()
	# 	return render(request, "stats/deliverynotes.html", context)

	fromdate = request.POST.get('fromdate')
	if fromdate:
		context['from'] = datetime.strptime(fromdate, "%Y-%m-%d")
		fromdate = fromdate + " 00:00:00"
	else:
		fromdate = str(date.today() - timedelta(days=7)) + " 00:00:00"

	todate = request.POST.get('todate')
	if todate:
		context['to'] = datetime.strptime(todate, "%Y-%m-%d")
		todate = todate + " 23:59:59"
	else:
		todate = str(date.today()) + " 23:59:59"

	context["form"] = form
	context["dataset"] = Transaction.objects.filter(updated_date_time__range=(fromdate,todate), trans_flag=2, yard = request.user.yard)
	return render(request, "stats/deliverynotes2.html", context)

#API for loading details from ajax to editform
@login_required(redirect_field_name=None)
def deliverynote_detail(request, identifier):
	try:
		obj = Transaction.objects.get(id=identifier)
	except:
		obj=None
	if obj:
		data = model_to_dict(obj)
	else:
		data={}
	return JsonResponse(data)

#@login_required(redirect_field_name=None)
def view_images_base64(request,identifier):
	try:
		obj = images_base64.objects.get(transaction_id=identifier)
	except:
		obj=None
	if obj:
		serialized_obj = serialize('json', [ obj, ])
		data = json.loads(serialized_obj)[0]['fields']
		return JsonResponse(data)
	else:
		return JsonResponse({'status':False,'msg':'No Images'})


@login_required(redirect_field_name=None)
def transaction_update(request):
	context = {}
	absolute_url = request.build_absolute_uri('?')
	context["absolute_url"] = absolute_url
	# form = TranskationForm(request.POST or None)

	if request.POST:
		id = request.POST.get('id')
		try:
			obj = Transaction.objects.get(id=id)
			form = TransactionForm(request.POST, instance=obj)
			if form.is_valid():
				form.save()
				context["dataset"] = obj
			else:
				print("error", form.errors)
		except:
			obj = None
	return context

@login_required(redirect_field_name=None)
def special_evaluation(request):
	context={}
	absolute_url = request.build_absolute_uri('?')
	context["absolute_url"] =  "http://"+request.get_host()

	if request.POST:
		note_type = request.POST.getlist('note_type')
		stat_type = request.POST.get('stat_type')
		fromdate = request.POST.get('fromdate')
		todate = request.POST.get('todate')

		if fromdate:
			context['from'] = datetime.strptime(fromdate,"%Y-%m-%d")
			fromdate = fromdate+" 00:00:00"
		else:
			fromdate = str(date.today())+" 00:00:00"

		if todate:
			context['to'] =datetime.strptime(todate,"%Y-%m-%d") 
			todate = todate +" 23:59:59"
		else:
			todate=str(date.today())+" 23:59:59"
			
		obj = Transaction.objects.filter(created_date_time__range=(fromdate,todate))
		# obj = Transkation.objects.raw('SELECT *,SUM(net_weight) as ttl FROM yard_transkation WHERE created_date_time BETWEEN %s and %s GROUP BY(article_id)',[fromdate,todate])
		sum_kg = 0
		if obj:
			if stat_type=='material' or 'vehicle' or 'supplier' or 'customer':
				# context["data"] = get_article_groupset(obj)
				context['stat_type'] = stat_type
				context['date'] = datetime.now()
				context["data"] = obj
				context["summ"]= sum(j.net_weight for j in obj)
				return Render.render("stats/pdf/material_stat.html", context)
		else:
			context['error'] = "No Transkations found!"
	# 	article_from = request.POST.get('article_from')
	# 	article_to = request.POST.get('article_to')
	# 	obj = Transaction.objects.filter(created_date_time__range=(fromdate,todate))
	return render(request, "stats/special_evaluation2.html", context)

@login_required(redirect_field_name=None)
def daily_closing(request):
	context={}
	absolute_url = request.build_absolute_uri('?')
	context["absolute_url"] =  "http://"+request.get_host()
	fromdate = str(date.today())+" 00:00:00"
	todate = str(date.today()) +" 23:59:59"
	trans = Transaction.objects.filter(trans_flag=1, yard = request.user.yard)
	# trans = Transaction.objects.filter(created_date_time__range=(fromdate,todate),trans_flag=1)
	if len(trans)>0:
		trans.update(trans_flag=2)
		message = str(len(trans))+" Transcations Updated"
	else:
		message = "No transcations found!"
	return JsonResponse({'status':True,'msg':message})


@login_required(redirect_field_name=None)
def site_list(request):
	context = {}
	form = TransactionForm(request.POST or None)
	context["form"] = form
	context["dataset"] = Transaction.objects.filter(trans_flag=0, yard = request.user.yard)
	return render(request, "stats/site_list.html", context)

@login_required
# @user_passes_test(lambda u: u.is_superuser,redirect_field_name=None)
def site_list_delete(request, identifier):
    context ={}
    obj = get_object_or_404(Transaction, id = identifier)
    obj.delete()
    return HttpResponseRedirect("/stats/site_list")


@login_required(redirect_field_name=None)
def deliverynotes1(request):
	if 'print_button' in request.POST:
		return pdf_view(request)
	context = {}
	form = TransactionForm(request.POST or None)
	context["form"] = form
	context["dataset"] = Transaction.objects.all()
	return render(request,'stats/deliverynotes2.html',context)



afrom django.http import FileResponse, Http404
import os
import sqlite3
import datetime
from datetime import datetime

LOGO_DIR="/var/www/html/pdfdemo"
PDF_DIR= "/var/www/html/pdfdemo"
PDF_BASE_NAME="delivery_note"
PDF_LATEX="pdflatex"

def pdf_view(request):
	def R(str):
		if str != None:
			str = str.replace('\\', '\\')
			str = str.replace('$', '\$')
			str = str.replace("`", '``')
			str = str.replace("´", "''")
			str = str.replace('"', "''")
			str = str.replace('<', '$<$')
			str = str.replace('>', '$>$')
			str = str.replace('_', '\\_')
			str = str.replace('#', '\\#')
			str = str.replace('{', '\\{')
			str = str.replace('}', '\\}')
			str = str.replace('^', '\\textasciicircum{}')
			str = str.replace("°", '$^{\\circ}$')
			str = str.replace('€', '\\euro{}')
			str = str.replace('&', '\\&')
			str = str.replace('%', '\\%')
			str = str.replace('Ω', '$\\Omega$')
			str = str.replace('½', '\\sfrac{1}{2}')
			str = str.replace('¾', '\\sfrac{3}{4}')
			str = str.replace('¼', '\\sfrac{1}{4}')
		return str
	
	def create_sheet(kunde_name, lieferanten_name, artikel_name, kennzeichen, zufahrt_art, weight_info, with_time_var):
		global LOGO_DIR, PDF_LATEX
		global PDF_DIR, PDF_BASE_NAME
		kunden_adresse = R(kunde_name) + ","
		fahrzeugnummer = R(kennzeichen)
		op_ok = True
		op_name = R("my_name")
		op_strasse = R("my_stgreet")
		op_bezeichnung = R("my_description")
		op_plz = R("my_zip")
		op_ort = R("my_town")
		lfd_nr = 1
		the_id = weight_info[0]
		the_weight = int(weight_info[1])
		the_date = weight_info[2]
		if with_time_var:
			the_time = weight_info[3]
		else:
			the_time = ""
		lieferant = "Ab-/Beladestelle\nLieferant:"
		tex_text  = "\\documentclass[12pt,oneside,a4paper]{article}\n"
		tex_text += "\\usepackage[utf8]{inputenc}\n"
		tex_text += "\\usepackage[german]{babel}\n"
		tex_text += "\\usepackage{color}\n"
		tex_text += "\\usepackage{eurosym}\n"
		#tex_text += "\\usepackage{xfrac}\n"
		tex_text += "\\pagestyle{empty}\n"
		tex_text += "\\usepackage{graphicx}\n"
		tex_text += "\\usepackage{hyperref}\n"
		tex_text += "\\usepackage[a4paper,left=2cm,right=16mm,top=1cm,bottom=1cm]{geometry}\n"
		tex_text += "\\setlength{\\parindent}{0pt}\n"
		tex_text += "\\begin{document}\n"
		tex_text += "\\begin{sf}\n"
		tex_text += "\\vspace*{4cm}\n"
		tex_text += "\\begin{tabular}{|c|c|c|c|c|c|}\\hline\n"
		tex_text += "\\multicolumn{3}{|p{9cm}|}{\n"
		tex_text += "\\parbox[t]{7cm}{\n"
		tex_text += "\\textbf{\\tiny{" + op_name + "}} \n"
		tex_text += "\\tiny{" + op_bezeichnung + "} "
		tex_text += "\\tiny{" + op_strasse + "}\\\\\n"
		tex_text += "\\tiny{" + op_plz + "} "
		tex_text += "\\tiny{" + op_ort + "}}\n"
		tex_text += "\\parbox[t]{5cm}{\n"
		tex_text += "\\vspace*{5mm}\n"
		tex_text += "\\textbf{\\large %s:}\\\\\n"%("Kunde")
		if len(kunde_name) > 0:
		#    conn = sqlite3.connect(OPERATOR_DATABASE)
		#    c = conn.cursor()
		#    sql_command = "SELECT vorname, bezeichnung, strasse, PLZ, ort FROM kunden WHERE name = '%s'"%(kunde_name)
		#    c.execute(sql_command)
		#    result = c.fetchall() 
		#    conn.close()
			result = [["Hans", "firma1", "xstreet", "98299", "Boston"]]
			if len(result) > 0: 
				tex_text += R(result[0][0]) + " " + R(kunde_name) + "\\\\\n"
				tex_text += R(result[0][1]) + "\\\\\n"; kunden_adresse+=R(result[0][1]) + ","
				tex_text += R(result[0][3]) + "\\\\\n"; kunden_adresse+=R(result[0][3]) + ","
				tex_text += R(result[0][4]) + "\n"; kunden_adresse+=R(result[0][4]) + "."
			else:
				tex_text += R(kunde_name) + "\n"
		else:
			tex_text += "\n"
		tex_text += "\\vspace*{5mm}\n"
		tex_text += "}}\n"
		tex_text += "&\\multicolumn{3}{|p{7cm}|}{\n"
		tex_text += "\\vspace*{2mm}\\raisebox{-90pt}{\\includegraphics[width=170pt]{" + LOGO_DIR + "/operator_logo.png}}\n"
		tex_text += "}\\\\ \\hline \n"
		tex_text += "&lfd. Nr.&Datum&Uhrzeit&Werk&Zufuhrart\\\\ \n"
		tex_text += "\parbox{4cm}{"
		tex_text += "\Large Wiegeschein\\\\Lieferschein\\\n\\vspace{2mm}}&%d&%s&%s&&%s\\\\ \\hline\n"%(lfd_nr, the_date, the_time, zufahrt_art)
		tex_text += "\\multicolumn{3}{|l|}{%s}&\multicolumn{3}{|l|}{Fahrzeug-Nr.}\\\\ \n"%(lieferant)
		tex_text += "\\multicolumn{3}{|l|}{\n"
		tex_text += "\\parbox{5cm}{\n"
		if len(lieferanten_name) > 0:
	#        conn = sqlite3.connect(OPERATOR_DATABASE)
	#        c = conn.cursor()
	#        sql_command = "SELECT strasse, plz, ort FROM lieferanten WHERE lieferanten_name = '%s'"%(lieferanten_name)
	#        c.execute(sql_command)
	#        result = c.fetchall() 
	#        conn.close()
			result = [["ystreet", "98932", "berlin"]]
			if len(result) > 0: 
				tex_text += "\\vspace{1cm}\n\n"
				tex_text += R(lieferanten_name) + "\\\\\n"
				tex_text += R(result[0][0]) + "\\\\\n"
				tex_text += R(result[0][1]) + " "
				tex_text += R(result[0][2]) +"\\\\\n\n"
			else:
				tex_text += R(lieferanten_name) + "\n"
		tex_text += "}}&\\multicolumn{3}{|l|}{\n"
		tex_text += "\\parbox{5cm}{\n"
		tara = 0
		the_tara_id = "?"
		the_tara_date = "??"
		the_tara_time = "??"
		wstate = ""
		if len(kennzeichen) > 0:
			tex_text += R(kennzeichen)+ "\\\\\n\n"
			tara_in_court = False
	#        conn = sqlite3.connect(OPERATOR_DATABASE)
	#        c = conn.cursor()
	#        sql_command = "SELECT tara, tara_date, tara_time, tara_id FROM yardlist WHERE kennung = '%s'"%(kennzeichen)
	#        c.execute(sql_command)
	#        result = c.fetchall() 
	#        conn.close()
			result = [[1234, "11.02.2021", "11:00", "90"]]
			if len(result) > 0: 
				tara_in_court = True
				tara = result[0][0]
				the_tara_date = result[0][1]
				the_tara_time = result[0][2]
				the_tara_id = result[0][3]
				if the_tara_date == None:
					the_tara_date = '???'
					tara_in_court = False
				if the_tara_time == None:
					the_tara_time = '???'
					tara_in_court = False
				if the_tara_id == None:
					the_tara_id = '?'
					tara_in_court = False
	#        conn = sqlite3.connect(OPERATOR_DATABASE)
	#        c = conn.cursor()
	#        sql_command = "DELETE FROM yardlist WHERE kennung = '%s'"%(kennzeichen)
	#        c.execute(sql_command)
	#        conn.commit()
	#        conn.close()
	#        if not tara_in_court:
	#            conn = sqlite3.connect(OPERATOR_DATABASE)
	#            c = conn.cursor()
	#            sql_command = "SELECT tara , tara_date, tara_time, tara_id FROM fahrzeuge WHERE kennung = '%s'"%(kennzeichen)
	#            c.execute(sql_command)
	#            result = c.fetchall() 
	#            conn.close()
	#            if len(result) > 0: 
	#                tara = result[0][0]
	#                the_tara_date = result[0][1]
	#                the_tara_time = result[0][2]
	#                the_tara_id = result[0][3]
	#                if the_tara_date == None:
	#                    the_tara_date = '???'
	#                if the_tara_time == None:
	#                    the_tara_time = '???'
	#                if the_tara_id == None:
	#                    the_tara_id = '?'
	#               wstate = "PT"
	#    else:
	#        tkMessageBox.showinfo('Err','Bitte geben Sie einen ein KFZ-Kennzeichen an!')
	#        return False
		tex_text += "\\vspace*{5mm}\n"
		tex_text += "}}\\\\ \\hline\n"
		tex_text += "\\end{tabular}\n\n"
		tex_text += "\\vspace*{2cm}\n"
		diff = the_weight - tara
		tara_s = str(tara)
		kosten = 0.0
		weight_s = ("%d"%(the_weight)).replace('.', ',')
		if diff >= 0:
			diff_s = ("%d"%(diff)).replace('.', ',')
		else:
	#        conn = sqlite3.connect(OPERATOR_DATABASE)
	#        c = conn.cursor()
	#        sql_command = "UPDATE  fahrzeuge SET tara = '%s' WHERE kennung = '%s'"%(weight_s, kennzeichen)
	#        c.execute(sql_command)
	#        conn.commit()
	#        conn.close()
			diff_s = ("%d"%(-diff)).replace('.', ',')
	#    if with_time_var:
	#        the_tara_date = ""
	#        the_tara_time = ""
		tex_text += "\\begin{tabular}{|p{24mm}p{25mm}p{15mm}p{3mm}rp{1cm}p{40mm}p{14mm}|} \\hline \n"
		tex_text += "&Datum&Uhrzeit&&Gewicht&&%s&Alibi-Nr.\\\\ \\hline \n"%("Material")
		if diff >= 0:
			tex_text += "Erstwiegung&%s&%s&%s&%s&kg&&%s\\\\ \n"%(the_tara_date, the_tara_time, wstate, tara_s,the_tara_id)
			tex_text += "Zweitwiegung&%s&%s&&%s&kg&&%s\\\\ \n"%(the_date, the_time, weight_s,the_id)
		else:
			tex_text += "Zweitwiegung&%s&%s&&%s&kg&&%s\\\\ \n"%(the_date, the_time, weight_s,the_id)
			tex_text += "Erstwiegung&%s&%s&%s&%s&kg&&%s\\\\ \n"%(the_tara_date, the_tara_time, wstate, tara_s,the_tara_id)
		tex_text += "Nettogewicht&&&E&%s&kg&%s&\\\\ \\hline \n"%(diff_s, R(artikel_name))
		tex_text += "\\end{tabular}"
		tex_text += "\\vfill\n"
		tex_text += "\\begin{tabular}{|p{53mm}|p{53mm}|p{53mm}|} \\hline\n"
		tex_text += "Unterschrift des Wägers&Unterschrift des Fahrers&Unterschrift des Empfängers\\\\ \hline\n"
		tex_text += "\\rule{0pt}{15mm}&\\rule{0pt}{15mm}&\\rule{0pt}{15mm}\\\\ \\hline\n"
		tex_text += "\\end{tabular}\n\n"
		tex_text += "\\footnotesize{E: errechnet, PT: Preset Tara (voreingegebens Tara)}\\\\\n"
		tex_text += "\\footnotesize{Messwerte aus frei programmierbarer Zusatzeinrichung. Die geeichten Messwerte können eingesehen werden.}\\\\\n"
		tex_text += "\\footnotesize{Für Überladungen haftet der Fahrzeuglenker.}\n"
		tex_text += "\\end{sf}\n"

		tex_text += "\\end{document}\n"
		tex_path = PDF_DIR + "/" + PDF_BASE_NAME + ".tex"
		tfile = open(tex_path, "wb")
		tfile.write(tex_text.encode('utf-8'))
		tfile.close()
		os.system(PDF_LATEX + " -output-directory=" + PDF_DIR + " " + tex_path)


	create_sheet("mycustomer", "my_supplier", "potetos", "manal-1235", 1, ["10", "11234", "10.02.21", "10:23"], True)

	return FileResponse(open('/var/www/html/pdfdemo/delivery_note.pdf', 'rb'), content_type='application/pdf')




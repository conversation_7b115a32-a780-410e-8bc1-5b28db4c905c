# Generated by Django 3.1.1 on 2021-06-04 08:03

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0014_transaction_vehicle_second_weight_flag'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='article',
            name='balance_weight',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.AlterField(
            model_name='article',
            name='entry_weight',
            field=models.DecimalField(decimal_places=4, default=0.0, max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.AlterField(
            model_name='article',
            name='outgoing_weight',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)]),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='selectcamera',
            name='yes',
            field=models.BooleanField(default=True),
        ),
    ]

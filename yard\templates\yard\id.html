{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{% load i18n %}
{% block head %}
    {% load static %}
{% endblock %}
{% block content %}
    <!-- /#sidebar-wrapper -->
    <div class="container">
    <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
        <i class="fas fa-align-justify"></i>
    </button>
    <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <div class="content_text">
                <p class="mb-0">{% translate 'OVERVIEW' %}</p>
            </div>
            <div class="heding">
                <p>{% translate 'ID List' %}</p>
            </div>
        </div>
    </div>

    <!--table strat-->
    <div class="row">
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <label>{% translate "Show entries" %}:</label>
            <select class="form-control w-30" id="showentries">
                <option>10</option>
                <option>25</option>
                <option>50</option>
                <option>100</option>
                entries
            </select>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
            <label>{% translate "Search" %}:</label>
            <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search"
                   id="mysearch">
        </div>
    </div>
    <div class="row">
        <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%"
               id="deliveryNoteTable">
            <thead>
            <tr>
            {% if request.user.is_superuser %}
                <th>{% translate 'Action' %}</th>
            {% endif %}
                <th>{{ "Ident" }}</th>
                <th>{% translate 'Customer' %}</th>
                <th>{{ "Kennzeichen" }}</th>
            <th>{% if request.session.supplier%} {{ request.session.supplier}} {% else %} {% translate 'Supplier' %} {% endif%}</th>
{#                {% comment %}<th>{% translate 'Baustelle' %}</th>{% endcomment %}#}
              <th>{{ "Lieferant" }}</th>
                <th>{% translate 'Forwarders' %}</th>
                <th>{% translate 'Material' %}</th>
                <th>{% translate 'Created on' %}</th>
                <th>{% translate 'Updated on' %}</th>
            </tr>
            </thead>
            <tbody class="mt-4">
            {% for data in data %}
                <tr class="loadCSD" ondblclick="javascript:loadCombinationDetails('{{ data.id }}')">
                {% if request.user.is_superuser %}
                    <td>
                        <a class="loadCS" href="javascript:loadCombinationDetails('{{ data.id }}')"><i
                                class="fas fa-pencil-alt text-primary  ml-4"></i></a>
                        <a class="confirmdelete" href="{% url 'comb_delete' identifier=data.id %}"><i
                                class="fas fa-trash-alt ml-2 text-danger"></i></a> &nbsp;
                        <a class="hover-pointer-cursor" target="_blank" href="{% url 'id_pdf' id=data.id %}"><i
                                class="fas fa-file-pdf"></i></a>
                    </td>
                {% endif %}
                    <td>{{ data.ident|default_if_none:"-" }}</td>
                    <td>{{ data.customer|default_if_none:"-" }}</td>
                    <td>{{ data.vehicle|default_if_none:"-" }}</td>
                    {% comment %}<td>{{ data.building_site|default_if_none:"-" }}</td>{% endcomment %}
                    <td>{{ data.supplier|default_if_none:"-" }}</td>
                     <td>{{ data.place_of_delivery.name1|default_if_none:"-" }}</td>
                    <td>{{ data.forwarders.name|default_if_none:"-" }}</td>
                    <td>{{ data.article|default_if_none:"-" }}</td>
                    <td>{{ data.created_date_time|default_if_none:"-" }}</td>
                    <td>{{ data.updated_date_time|default_if_none:"-" }}</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="container">
        <div class="row mb-5">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

                <!-- Material form login -->
                <div class="card mt-4" id="entry">
                {% if request.user.is_superuser %}
                    <div class="row">
                        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                            <h5 class="card-header info-color white-text py-3">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse2031"> {% endcomment %}
                                        <div class="row">
                                            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                                                <p class="mb-0 pt-2 mr-4 text-color text_color float-left">
                                                    {% translate "ID" %}
                                                </p>
{#                                                <button type='button' class='btn btn-dark' id="text_btn"#}
{#                                                        style='float:right;margin-left: 4px;'>{% translate 'Click' %}</button>#}
                                                <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill"
                                                        style="float:right;">Neue Eingabe
                                                </button>

                                            </div>
                                        </div>
                                        {% comment %} </a> {% endcomment %}
                                    </h4>
                                </div>
                            </h5>
                        </div>
                    </div>
                {% endif %}


                    <!--first forms satrat-->
                    <div id="collapse2031" class="collapse">
                        <div class="panel-body">
                            <div class="contanier">
                                <form class="form-group" method="POST" enctype="multipart/form-data">
                                    {% csrf_token %}
                                    <input type="hidden" name="id" id="id" value="">
                                    <div class="row">
                                        <div class="col-xl-4 col-lg-4 text-left mt-4">
                                            <div>
                                                <label>{{ form.ident.label }}</label>
                                                {{ form.ident }} {{ form.ident.errors }}
                                            </div>
                                            <div>
                                                <label>{{ "Fahrer" }}</label>
                                                {{ form.short_name }} {{ form.short_name.errors }}
                                            </div>

                                            <div>
                                                <label>{% if request.session.supplier%} {{ request.session.supplier}} {% else %} {% translate 'Supplier' %} {% endif%}</label>
                                                {{ form.supplier }} {{ form.supplier.errors }}
                                            </div>

                                            <div>
                                                <label>{{ form.contracts.label }}</label>
                                             {{ form.contracts }} {{ form.contracts.errors }}
                                            </div>
                                        </div>
                                        <div class="col-xl-4 col-lg-4 text-left mt-4">
                                            <div>
                                                <label>{{ form.vehicle.label }}</label>
                                                {{ form.vehicle }} {{ form.vehicle.errors }}
                                            </div>
                                            <div>
                                                <label>{{ form.customer.label }}</label>
                                                {{ form.customer }} {{ form.customer.errors }}
                                            </div>

                                            <div>
                                                <label>{{ form.status.label }}</label>
                                                <select name="status" class="form-control"
                                                        title="Biite Richtung auswählen"
                                                        id="id_status">
                                                    <option value="0">Eingang</option>
                                                    <option value="1">Ausgang</option>

                                                </select> {{ form.status.errors }}
                                            </div>

                                             <div>
                                                <label>{{ "Lieferant" }}</label>
                                                {{ form.place_of_delivery }} {{ form.place_of_delivery.errors }}
                                            </div>
                                        </div>
                                        <div class="col-xl-4 col-lg-4 text-left mt-4">
                                            <div>
                                                <label>{{ form.article.label }}</label>
                                                {{ form.article }} {{ form.article.errors }}
                                            </div>
{#                                            <div>#}
{#                                                <label>{{ form.container.label }}</label>#}
{#                                                {{ form.container }} {{ form.container.errors }}#}
{#                                            </div>#}
                                            <div>
                                                <label>{{ form.forwarders.label }}</label>
                                                {{ form.forwarders }} {{ form.forwarders.errors }}
                                            </div>

                                            <div class="mt-4">
                                                <label>{{ form.tara_with_mobile.label }}</label>
                                                {{ form.tara_with_mobile }} {{ form.tara_with_mobile.errors }}
                                            </div>

                                        </div>
                                        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                                            <button id="submit" type="submit" class="btn btn-primary mr-3 mt-3"><i
                                                    class="fas fa-save ml-2"></i>{% translate "Save2" %}</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="MyPopup" class="modal fade modal-custom bd-example-modal-lg" role="dialog">
        <div class="modal-dialog modal-dd">
            <!-- Modal content-->
            <div class="modal-content p-4" style="width: 1048px;height: 548px;">
                <div class="modal-header modal-header-custom">
                    <h4 class="modal-title">
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                </div>
                <div class="modal-footer">
                    <input type="button" id="btnClosePopup" value="Schließen" class="btn btn-secondary"/>
                </div>
            </div>
        </div>
    </div>

{% endblock %}
{% block scripts %}
    <script src="{% static 'stats/js/stats_custom.js' %}"></script>

    <script>
        // DataTable
        $(document).ready(function () {

            $("#id_vehicle").change(function (){

                $.ajax({
          url: '/vehicle_detail/' + $(this).val(),
          method: 'GET',
          success: function (result){
              $("#id_forwarders").val(result.forwarder).trigger("change")
          }
         });

            })


            var table = $('#deliveryNoteTable').DataTable(
                {
                    "bLengthChange": false,
                    initComplete: function () {
                        // Apply the search
                        this.api().columns().every(function () {
                            var that = this;
                            console.log(that)
                            // <!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
                            // <!--                       if ( that.search() !== this.value ) {-->
                            // <!--                           that.search( this.value ).draw();-->
                            // <!--                       }-->
                            // <!--                   } );-->
                        });
                    }
                });

            $("#deliveryNoteTable_filter").hide()
            $("#deliveryNoteTable_previous").html("Zurück");
            // custom search filter
            $('#mysearch').on('keyup', function () {
                table.search(this.value).draw();
            });

            $('#id_contracts').select2();

            //  custom show entries
            $('#showentries').change(function () {
                table.page.len(this.value).draw();
            });

        });

        $('#id_tara_with_mobile').click(function () {
            if ($('#id_tara_with_mobile').is(':checked')) {
                $('#id_tara_with_mobile').val(true)
            } else {
                $('#id_tara_with_mobile').val("false")

            }
        });

        {% if request.user.is_superuser %}
            $(".loadCS").click(function () {
                window.location = "#entry";
                $("#collapse2031").addClass('show');
            });

            $(".loadCSD").dblclick(function () {
                window.location = "#entry";
                $("#collapse2031").addClass('show');
            });
        {% endif %}

        $("#new_entry").click(function (e) {
            $("#collapse2031").addClass('show');
            $('input#id_update_quantity').val('0');
            $('input#id_minimum_amount').val('0');

            $("#id_article").prop('selectedIndex',0);
            $("#id_driver").prop('selectedIndex',0);
            $("#id_status").prop('selectedIndex',0);
            $("#id_delivery_customer").prop('selectedIndex',0);
            $("#id_container").prop('selectedIndex',0);
            $("#id_forwarders").prop('selectedIndex',0);
            $("#id_supplier").prop('selectedIndex',0);
            $("#id_vehicle").prop('selectedIndex',0);

            return false;
        })

        $("#id_ident").keyup(function () {
            val = this.value;
            $.ajax({
                url: "{% url 'comb_match' %}",
                type: 'GET',
                data: {'val': val},
                success: function (resp) {
                    if (resp.data == 1) {
                        $("#id_ident").css('background-color', '#fd0000')
                        $("#submit").prop('disabled', true)
                    } else {
                        $("#id_ident").css('background-color', '#ffff')
                        $("#submit").prop('disabled', false)
                    }
                }
            })
        })
        $("#text_btn").click(function () {
            var title = "Text für id";
            // var body = "Material list";
            $("#MyPopup .modal-title").html(title);

            $("#btnClosePopup").click(function () {
                $("#MyPopup").modal("hide");
            });
            $("#MyPopup .modal-body").load("/id_text");
            $("#MyPopup").modal();
        });
    </script>
{% endblock %}
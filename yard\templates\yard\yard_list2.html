{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}
<div class="container">
   <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
   <i class="fas fa-align-justify"></i>
   </button>
   <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
      <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
         <div class="content_text">
            <p class="mb-0">{% translate "OVERVIEW" %}</p>
         </div>
         <div class="heding">
            <p>{% translate "Yard List" %}</p>
         </div>
      </div>
   </div>
   <!--table strat-->
   <div class="row">
       <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
          <label>{% translate "Show entries" %}:</label>
         <select class="form-control w-30" id="showentries">
            <option>10</option>
            <option>25</option>
            <option>50</option>
            <option>100</option>
            entries
         </select>
       </div>
       <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
           <label>{% translate "Search" %}:</label>
           <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
       </div>
   </div>
   <div class="">
      <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="yardListTable">
         <thead>
            <tr>
          <th>{% translate "Action" %}</th>
          <th>{{form.name.label}}</th>
          <th>{{form.place.label}}</th>
          <th>{{form.country.label}}</th>
            </tr>
         </thead>

        <tbody>
          {% for data in dataset %}
          <tr>
              <td><a href="javascript:loadYardListDetails('{{ data.id }}')"><i class="fas fa-pencil-alt text-primary"></i></a>
<!--                <a href="{% url 'yard_list_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a>-->
            </td>
            <td>{{ data.name }}</td>
            <td>{{ data.place }}</td>
            <td>{{ data.country }}</td>

          </tr>
          {% endfor %}
<!--          <tfoot hidden>-->
<!--            <tr>-->
<!--              <th rowspan="1" colspan="1">-->
<!--                 <input type="text" class="form-control">-->
<!--              </th>-->
<!--              <th rowspan="1" colspan="1">-->
<!--                 <input type="text"class="form-control">-->
<!--              </th>-->
<!--              <th rowspan="1" colspan="1">-->
<!--                 <input type="text" class="form-control">-->
<!--              </th>-->
<!--              <th rowspan="1" colspan="1">-->
<!--                 <input type="text" class="form-control">-->
<!--              </th>-->
<!--            </tr>-->
<!--          </tfoot>-->
        </tbody>

      </table>
   </div>
</div>
<!--table end-->
<div class="container">
  <div class="row mb-5">
      <div class="col-sm-12 col-12">
         <!-- Material form login -->
         <div class="card mt-3">
            <div class="row">
               <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                  <h5 class="card-header info-color white-text py-3">
                     <div class="panel-heading">
                        <h4 class="panel-title">
                           <a data-toggle="collapse" data-parent="#accordion" href="#collapse_20">
                              <p class="mb-0 pt-2 text-color text_color">{% translate "Yard" %}</p>     
                           </a>
                        </h4>
                     </div>
                  </h5>
               </div>
            </div>
            <div id="collapse_18" class="collapse show" >
               <div class="panel-body">
                  <div class="card-body text-left">
                     <form class="form-group" method="POST" enctype="multipart/form-data">
                        {% csrf_token %} 
                        <input type="hidden" name="id" id="id">

                        <div class="row">
                           <div class="col-md-4 col-4">
                              <div class="md-form mb-3">
                                 <label>{{form.name.label}}</label>
                                 {{form.name}} {{form.name.errors}}
                                 <!-- <input type="License plate" class="form-control" name="License plate" placeholder="License plate"> -->
                              </div>
                           </div>
                           <div class="col-md-4 col-4">
                              <div class="md-form mb-3">
                                 <label>{{form.place.label}}</label>
                                 {{form.place}} {{form.place.errors}}
                                 <!-- <input type="Vehicle Weight" class="form-control" name="Vehicle Weight" placeholder="Vehicle Weight"> -->
                              </div>
                           </div>
                           <div class="col-md-4 col-4">
                              <div class="md-form mb-3">
                                 <label>{{form.country.label}}</label>
                                 {{form.country}} {{form.country.errors}}
                                 <!-- <input type="Vehicle weight ide" class="form-control" name="Vehicle weight id" placeholder="Vehicle weight id"> -->
                              </div>
                           </div>
                           <div class="col-md-4 col-4">
                              <button type="submit" id="submit" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate "Save" %}</button>
                           </div>
                        </div>
                     </form>
                  </div>
               </div>
            </div>
         </div>
      </div>
  </div>
</div>
{% endblock %}

{% block scripts %}

<script>
    // DataTable
    $(document).ready(function() {
      var table = $('#yardListTable').DataTable(
         {
            "bLengthChange": false,
           initComplete: function () {
               // Apply the search
               this.api().columns().every( function () {
                   var that = this;
                   console.log(that)
<!--                   $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
<!--                       if ( that.search() !== this.value ) {-->
<!--                           that.search( this.value ).draw();-->
<!--                       }-->
<!--                   } );-->
               } );
           }
          });

      $("#yardListTable_filter").hide()

      // custom search filter
      $('#mysearch').on( 'keyup', function () {
          table.search( this.value ).draw();
      } );

      //  custom show entries
      $('#showentries').change(function() {
          table.page.len(this.value).draw();
      } );

 });

  $("#new_entry").click(function(e){
    e.preventDefault();
    $('input').val('');
    $('select').val('');
    return false;
  })
</script>
{% endblock %}
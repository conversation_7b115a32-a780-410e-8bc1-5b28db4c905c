# Generated by Django 3.1.1 on 2022-06-10 15:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0130_merge_20220601_1733'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='article',
            options={'ordering': ('name',), 'verbose_name_plural': 'Artikel'},
        ),
        migrations.AlterModelOptions(
            name='buildingsite',
            options={'verbose_name_plural': 'Baustellen'},
        ),
        migrations.AlterModelOptions(
            name='combination',
            options={'verbose_name_plural': 'Kombinationen'},
        ),
        migrations.AlterModelOptions(
            name='contract',
            options={'verbose_name_plural': 'Verträge'},
        ),
        migrations.AlterModelOptions(
            name='customer',
            options={'ordering': ('name1',), 'verbose_name_plural': 'Kunden'},
        ),
        migrations.AlterModelOptions(
            name='delivery_note',
            options={'verbose_name_plural': 'Lieferscheine'},
        ),
        migrations.AlterModelOptions(
            name='foreignflag',
            options={'verbose_name_plural': 'Ausländische Flaggen'},
        ),
        migrations.AlterModelOptions(
            name='forwarders',
            options={'verbose_name_plural': 'Spediteure'},
        ),
        migrations.AlterModelOptions(
            name='placeofdelivery',
            options={'ordering': ('name1',)},
        ),
        migrations.AlterModelOptions(
            name='settings',
            options={'verbose_name_plural': 'Einstellungen'},
        ),
        migrations.AlterModelOptions(
            name='supplier',
            options={'ordering': ('supplier_name',), 'verbose_name_plural': 'Lieferanten'},
        ),
        migrations.AlterModelOptions(
            name='transaction',
            options={'verbose_name_plural': 'Transaktion'},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'verbose_name': 'user', 'verbose_name_plural': 'Benutzer'},
        ),
        migrations.AlterModelOptions(
            name='vehicle',
            options={'ordering': ('license_plate',), 'verbose_name_plural': 'Fahrzeuge'},
        ),
        migrations.AlterModelOptions(
            name='yard_list',
            options={'verbose_name_plural': 'Hofliste'},
        ),
        migrations.RemoveField(
            model_name='placeofdelivery',
            name='name',
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='area',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='company',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='company_size',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='contact_person2_email',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='contact_person2_phone',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='contact_person3_email',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='contact_person3_phone',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='cost_centre',
            field=models.PositiveIntegerField(default=1, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='customer_type',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='delivery_terms',
            field=models.CharField(blank=True, choices=[('free', 'Free'), ('paid', 'Paid'), ('other', 'Other')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='description',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='diff_invoice_recipient',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='document_lock',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='dunning',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='fax',
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='kreditoren_number',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='name1',
            field=models.CharField(max_length=40, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='name2',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='payment_block',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='perm_country',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='perm_pin',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='perm_place',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='perm_street',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='phone_number',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='post_office_box',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='private_person',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='salutation',
            field=models.CharField(blank=True, choices=[('Mr', 'Mr'), ('Mrs', 'Mrs'), ('Company', 'Company')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='sector',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='special_discount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='term_of_payment',
            field=models.CharField(blank=True, choices=[('Sofort', 'Sofort'), ('7 Tage', '7 Tage'), ('14 Tage', '14 Tage'), ('30 Tage', '30 Tage')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='placeofdelivery',
            name='website',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='unit',
            field=models.CharField(blank=True, choices=[('0', 'kg'), ('1', 't')], max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='article',
            name='vat',
            field=models.DecimalField(blank=True, choices=[(0.0, '0%'), (7.0, '7%'), (19.0, '19%')], decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='contract',
            name='status',
            field=models.CharField(blank=True, choices=[('', '----------'), ('0', 'Eingang'), ('1', 'Ausgang'), ('2', 'Fremdweigung')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='placeofdelivery',
            name='addition1',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='placeofdelivery',
            name='addition2',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='placeofdelivery',
            name='addition3',
            field=models.CharField(blank=True, max_length=40, null=True),
        ),
        migrations.AlterField(
            model_name='placeofdelivery',
            name='street',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
    ]

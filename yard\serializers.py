from rest_framework import serializers
from .models import HandTransmitter, LoadingBox, MaxVehicle, Notification, Vehicle, Customer, Supplier, Article, \
    BuildingSite, Warehouse, \
    Transaction, \
    Combination, Forwarders, RouteImage, \
    images_base64, User, \
    DriverSignature, Contract, DriverID, Barriers, OfficeTiming, OfficeName, VehicleType, FireAlarm, TourApprove
from drf_extra_fields.fields import Base64I<PERSON>Field
from rest_framework.utils import model_meta


# from drf_base64.fields import Base64ImageField


class VehicleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Vehicle
        fields = '__all__'


class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = '__all__'


class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = '__all__'


class ArticleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Article
        fields = '__all__'


class BuildingSiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = BuildingSite
        fields = '__all__'


class WarehouseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Warehouse
        fields = '__all__'


class TransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Transaction
        fields = '__all__'


class TransactSerializer(serializers.ModelSerializer):
    vehicle = serializers.StringRelatedField()
    article = serializers.StringRelatedField()
    customer = serializers.StringRelatedField()
    supplier = serializers.StringRelatedField()

    class Meta:
        model = Transaction
        fields = '__all__'


class CombinationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Combination
        fields = '__all__'

class FireAlarmSerializer(serializers.ModelSerializer):
    class Meta:
        model = FireAlarm
        fields = '__all__'


class TourApproveSerializer(serializers.ModelSerializer):
    class Meta:
        model = TourApprove
        fields = '__all__'

class VehicleTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VehicleType
        fields = '__all__'

class OfficeNameSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfficeName
        fields = '__all__'

class CombinationReadSerializer(serializers.ModelSerializer):
    vehicle = serializers.StringRelatedField()
    article = serializers.StringRelatedField()
    customer = serializers.StringRelatedField()
    supplier = serializers.StringRelatedField()

    class Meta:
        model = Combination
        fields = '__all__'


class GetDetailCombinationSerializer(serializers.ModelSerializer):
    vehicle = VehicleSerializer()
    article = ArticleSerializer
    customer = CustomerSerializer()
    supplier = SupplierSerializer()

    class Meta:
        model = Combination
        fields = '__all__'


class ForwarderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Forwarders
        fields = '__all__'


# class ImageSerializerField(serializers.Field):
#     def to_representation(self, value):
#         if value:
#             return value # convert it the way you want to see in json
#     def to_internal_value(self, data):
#         if data:
#             im = Image.open(BytesIO(base64.b64decode(data)))
#             return im

# class Base64ImageField(serializers.ImageField):
#     def to_internal_value(self, data):
#         if isinstance(data, str):
#             # base64 encoded image - decode
#             ext = 'jpg' # guess file extension
#             data = ContentFile(base64.b64decode(imgstr))
#         return super(Base64ImageField, self).to_internal_value(data)


class SaveImageSerializer(serializers.ModelSerializer):
    image1 = Base64ImageField(required=False, allow_null=True)
    image2 = Base64ImageField(required=False, allow_null=True)
    image3 = Base64ImageField(required=False, allow_null=True)
    image4 = Base64ImageField(required=False, allow_null=True)
    image5 = Base64ImageField(required=False, allow_null=True)
    image6 = Base64ImageField(required=False, allow_null=True)
    image7 = Base64ImageField(required=False, allow_null=True)
    image8 = Base64ImageField(required=False, allow_null=True)
    image9 = Base64ImageField(required=False, allow_null=True)
    image10 = Base64ImageField(required=False, allow_null=True)
    image11 = Base64ImageField(required=False, allow_null=True)
    image12 = Base64ImageField(required=False, allow_null=True)

    class Meta:
        model = images_base64
        fields = '__all__'


class DriverSignSerializer(serializers.ModelSerializer):
    image = Base64ImageField()

    class Meta:
        model = DriverSignature
        fields = '__all__'


class ContractSerializer(serializers.ModelSerializer):
    class Meta:
        model = Contract
        fields = '__all__'


class HandTransmitterSerializer(serializers.ModelSerializer):
    class Meta:
        model = HandTransmitter
        fields = '__all__'


class DriverIDSerializer(serializers.ModelSerializer):
    class Meta:
        model = DriverID
        fields = '__all__'

class BarriersSerializer(serializers.ModelSerializer):
    class Meta:
        model = Barriers
        fields = '__all__'

class OfficeTimingSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)
    class Meta:
        model = OfficeTiming
        fields = ['id', 'day_name', 'show', 'start_time', 'end_time']

class OfficeNameTimingSerializer(serializers.ModelSerializer):
    officetiming = OfficeTimingSerializer(many=True)
    class Meta:
        model = OfficeName
        fields = ['id', 'ident_code', 'name', 'officetiming']

    def create(self, validated_data):
        officetimings_data = validated_data.pop('officetiming')
        office_name = OfficeName.objects.create(**validated_data)
        for officetiming_data in officetimings_data:
            if "id" in officetiming_data:
                officetiming_data.pop('id')
            refrence_detail = OfficeTiming.objects.create(office_name_id=office_name, **officetiming_data)
        return office_name

    def update(self, instance, validated_data):
        if 'officetiming' in validated_data:
            officetimings_data = validated_data.pop('officetiming')
            for officetiming_data in officetimings_data:
                officetiming_id = officetiming_data.pop('id')
                officetiming_intance = OfficeTiming.objects.get(id=officetiming_id)
                officetiming_info = model_meta.get_field_info(officetiming_intance)
                m2m_fields = []
                for attr, value in officetiming_data.items():
                    if attr in officetiming_info.relations and officetiming_info.relations[attr].to_many:
                        m2m_fields.append((attr, value))
                    else:
                        setattr(officetiming_intance, attr, value)
                officetiming_intance.save()
                for attr, value in m2m_fields:
                    field = getattr(officetiming_intance, attr)
                    field.set(value)
        return super(OfficeNameTimingSerializer, self).update(instance, validated_data)

class RouteImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = RouteImage
        fields = '__all__'

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = '__all__'


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = "__all__"#('name', 'email', 'role', 'password', "is_superuser", 'is_staff')

    def create(self, validated_data):
        user = super(UserSerializer, self).create(validated_data)
        user.set_password(validated_data['password'])
        user.save()
        return user

    def update(self, instance, validated_data):
        instance.is_superuser = validated_data.get('is_superuser', instance.is_superuser)
        instance.name = validated_data.get('name', instance.name)
        instance.role = validated_data.get('role', instance.role)
        instance.email = validated_data.get('email', instance.email)
        instance.is_staff = validated_data.get('is_staff', instance.is_staff)
        instance.is_active = validated_data.get('is_active', instance.is_active)
        instance.address = validated_data.get('address', instance.address)
        instance.telephone = validated_data.get('telephone', instance.telephone)
        if "password" in validated_data:
            instance.set_password(validated_data['password'])
        instance.save()
        return instance
    
class LoadingBoxSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoadingBox
        fields = '__all__'

class MaxVehicleSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaxVehicle
        fields = '__all__'
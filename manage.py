#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import hashlib
import os
import re
import subprocess
import sys

import psycopg2
from django.conf import settings

def get_mac_id():
    proc = subprocess.Popen(f'{os.path.dirname(sys.executable)}\pyarmor hdinfo', shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT)
    pattern = r'Default Mac address: "(?:[0-9a-fA-F]:?){12}"'
    output = proc.stdout.read()
    output = output.decode()
    matched_mac = re.findall(pattern, output)
    matched_mac = matched_mac[0].split()[-1]
    matched_mac = matched_mac[1:]
    matched_mac = matched_mac[:-1]
    return matched_mac


def get_mac_from_db():
    machine_mac = get_mac_id()
    connection = psycopg2.connect(host='www.rw-datanet.com',
                                  port=5434,
                                  user='yardman_admin',
                                  password='yardman_pass',
                                  database='rudiger')
    with connection.cursor() as cursor:
        sql = "SELECT mac_address FROM customer_track WHERE mac_address=%s"
        cursor.execute(sql, (machine_mac,))
        result = cursor.fetchone()
        return result


def get_enc_key(machine_mac):
    salt = settings.SECRET_KEY
    machine_fingerprint = hashlib.pbkdf2_hmac('sha256', machine_mac.encode('utf-8'), salt.encode('utf-8'), 10000)
    return machine_fingerprint


def save_lic(machine_mac):
    machine_fingerprint = get_enc_key(machine_mac)
    file = open("lic.txt", "w+")
    file.write(machine_fingerprint.hex())
    file.close()


def check_encryption():
    try:
        lic_path = os.path.join(settings.BASE_DIR, "lic.txt")
        from_file = open(lic_path, "r")
        from_file = from_file.readline()
        machine_mac = get_mac_id()
        machine_fingerprint = get_enc_key(machine_mac)
        if machine_fingerprint.hex() == from_file:
            print("License Verified")
        else:
            print("Invalid License")
            sys.exit(3)

    except FileNotFoundError as fn:
        print(fn)
        is_mac_valid = get_mac_from_db()
        if is_mac_valid:
            save_lic(is_mac_valid[0])
            check_encryption()
        else:
            print("Licence Not Found")
            sys.exit(3)


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yardman.settings')
    if settings.PROD:
        check_encryption()

    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()

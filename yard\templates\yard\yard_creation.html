{% extends 'base2.html' %}

{% load crispy_forms_tags %}
{%load i18n%}
{% block content %}
<div class="container">
   <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
   <i class="fas fa-align-justify"></i>
   </button>
   <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
      <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
         <div class="content_text">
            <p class="mb-0">{% translate "OVERVIEW" %}</p>
         </div>
         <div class="heding">
            <p>{% translate "Create Yard" %}</p>
         </div>
      </div>
   </div>
   <!--table strat-->
<!--   <div class="row">-->
<!--      <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%">-->
<!--         <thead>-->
<!--            <tr>-->
<!--          <th>{{form.name.label}}</th>-->
<!--          <th>{{form.place.label}}</th>-->
<!--          <th>{{form.country.label}}</th>-->
<!--          <th>{% translate "Action" %}</th>-->
<!--            </tr>-->
<!--         </thead>-->
<!--        {% for data in dataset %}-->
<!--        <tbody>-->
<!--          <tr>-->
<!--            <td>{{ data.name }}</td>-->
<!--            <td>{{ data.place }}</td>-->
<!--            <td>{{ data.country }}</td>-->
<!--            <td><a href="javascript:loadYardListDetails('{{ data.id }}')"><i class="fas fa-pencil-alt text-primary"></i></a>-->
<!--&lt;!&ndash;                <a href="{% url 'yard_list_delete' identifier=data.id  %}"><i class="fas fa-trash-alt ml-2 text-danger"></i></a>&ndash;&gt;-->
<!--            </td>-->
<!--          </tr>-->
<!--        </tbody>-->
<!--        {% endfor %} -->
<!--      </table>-->
<!--   </div>-->
</div>
<!--table end-->
<div class="container">
  <div class="row mb-5">
      <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">
         <!-- Material form login -->
         <div class="card">
            <div class="row">
               <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                  <h5 class="card-header info-color white-text py-3">
                     <div class="panel-heading">
                        <h4 class="panel-title">
                           <a data-toggle="collapse" data-parent="#accordion" href="#collapse_20">
                              <div class="row">
                                 <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12 text-left">
                                    <p class="mb-0 pt-2 text-color text_color">{% translate "Yard Details" %}</p>
                                 </div>
                              </div>
                           </a>
                        </h4>
                     </div>
                  </h5>
               </div>
            </div>
            <div id="collapse_18" class="collapse show" >
               <div class="panel-body">
                  <div class="card-body text-left">
                     <form class="form-group" method="POST" enctype="multipart/form-data">
                        {% csrf_token %} 
<!--                        <input type="hidden" name="id" id="id">-->

                        <div class="md-form mb-3">
                           <label>{{form.name.label}}</label>
                           {{form.name}} {{form.name.errors}}
                           <!-- <input type="License plate" class="form-control" name="License plate" placeholder="License plate"> -->
                        </div>
                        <div class="md-form mb-3">
                           <label>{{form.place.label}}</label>
                           {{form.place}} {{form.place.errors}}
                           <!-- <input type="Vehicle Weight" class="form-control" name="Vehicle Weight" placeholder="Vehicle Weight"> -->
                        </div>
                        <div class="md-form mb-3">
                           <label>{{form.country.label}}</label>
                           {{form.country}} {{form.country.errors}}
                           <!-- <input type="Vehicle weight ide" class="form-control" name="Vehicle weight id" placeholder="Vehicle weight id"> -->
                        </div>
                        <button type="submit" id="submit" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i>{% translate "Save" %}</button>
                     </form>
                  </div>
               </div>
         </div>
      </div>
  </div>
</div>
{% endblock %}
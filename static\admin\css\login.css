/* LOGIN FORM */

.login {
    background: #f8f8f8;
    height: auto;
}

.login #header {
    height: auto;
    padding: 15px 16px;
    justify-content: center;
}

.login #header h1 {
    font-size: 18px;
}

.login #header h1 a {
    color: #fff;
}

.login #content {
    padding: 20px 20px 0;
}

.login #container {
    background: #fff;
    border: 1px solid #eaeaea;
    border-radius: 4px;
    overflow: hidden;
    width: 28em;
    min-width: 300px;
    margin: 100px auto;
    height: auto;
}

.login #content-main {
    width: 100%;
}

.login .form-row {
    padding: 4px 0;
    float: left;
    width: 100%;
    border-bottom: none;
}

.login .form-row label {
    padding-right: 0.5em;
    line-height: 2em;
    font-size: 1em;
    clear: both;
    color: #333;
}

.login .form-row #id_username, .login .form-row #id_password {
    clear: both;
    padding: 8px;
    width: 100%;
    box-sizing: border-box;
}

.login span.help {
    font-size: 10px;
    display: block;
}

.login .submit-row {
    clear: both;
    padding: 1em 0 0 9.4em;
    margin: 0;
    border: none;
    background: none;
    text-align: left;
}

.login .password-reset-link {
    text-align: center;
}

# Generated by Django 3.1.1 on 2021-09-05 19:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0070_driversignenable'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contract',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='contract',
            name='reserved_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='contract',
            name='start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='contract',
            name='supplier',
            field=models.ManyToManyField(blank=True, null=True, to='yard.Supplier'),
        ),
        migrations.AlterField(
            model_name='contract',
            name='vehicles',
            field=models.ManyToManyField(blank=True, null=True, to='yard.Vehicle'),
        ),
    ]

# Generated by Django 3.1.1 on 2022-12-07 19:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0165_auto_20221125_1540'),
    ]

    operations = [
        migrations.CreateModel(
            name='OfficeName',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ident_code', models.CharField(max_length=10, null=True, unique=True)),
                ('name', models.CharField(max_length=50, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='OfficeTiming',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_name', models.CharField(max_length=50)),
                ('show', models.BooleanField(default=False)),
                ('start_time', models.TimeField(blank=True, null=True)),
                ('end_time', models.TimeField(blank=True, null=True)),
                ('office_name_id', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='officetiming', to='yard.officename')),
            ],
        ),
    ]

{% load i18n %}
{%block head%}
  {%load static%}

{%endblock%}
{% block content %}
<style>
#sig-canvas {
  border: 2px dotted #CCCCCC;
  border-radius: 22px;
  cursor: crosshair;
}
</style>
{% comment %} <form class="form-group" id="sign_form" action=""onkeydown="return event.key != 'Enter';" method="POST" enctype="multipart/form-data"> {% csrf_token%} {% endcomment %}
	<div class="container">
		<div class="row">
			<div class="col-md-12">
				<h1>Unterschrift des Fahrers</h1>
				<p>Unterschreiben Sie bitte unten im Feld und Speichern Sie Ihre Unterschrift als Bild.</p>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
		 		<canvas id="sig-canvas" width="850" height="300">
		 			Get a better browser, bro.
		 		</canvas>
		 	</div>
		</div>
		<div class="row">
			<div class="col-md-12">
                <input type="hidden" id="sig-dataUrl" name="signature" class="form-control" />
				<button type="button" class="btn btn-primary" id="sig-submitBtn"> {% translate "Submit Signature" %}</button>
				<button type="button" class="btn btn-default" id="sig-clearBtn"> {% translate "Clear Signature" %}</button>
			</div>
		</div>
		
	</div>
  
<script>
(function() {
  window.requestAnimFrame = (function(callback) {
    return window.requestAnimationFrame ||
      window.webkitRequestAnimationFrame ||
      window.mozRequestAnimationFrame ||
      window.oRequestAnimationFrame ||
      window.msRequestAnimaitonFrame ||
      function(callback) {
        window.setTimeout(callback, 1000 / 60);
      };
  })();

  var canvas = document.getElementById("sig-canvas");
  var ctx = canvas.getContext("2d");
  ctx.strokeStyle = "#222222";
  ctx.lineWidth = 4;

  var drawing = false;
  var mousePos = {
    x: 0,
    y: 0
  };
  var lastPos = mousePos;

  canvas.addEventListener("mousedown", function(e) {
    drawing = true;
    lastPos = getMousePos(canvas, e);
  }, false);

  canvas.addEventListener("mouseup", function(e) {
    drawing = false;
  }, false);

  canvas.addEventListener("mousemove", function(e) {
    mousePos = getMousePos(canvas, e);
  }, false);

  // Add touch event support for mobile
  canvas.addEventListener("touchstart", function(e) {

  }, false);

  canvas.addEventListener("touchmove", function(e) {
    var touch = e.touches[0];
    var me = new MouseEvent("mousemove", {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    canvas.dispatchEvent(me);
  }, false);

  canvas.addEventListener("touchstart", function(e) {
    mousePos = getTouchPos(canvas, e);
    var touch = e.touches[0];
    var me = new MouseEvent("mousedown", {
      clientX: touch.clientX,
      clientY: touch.clientY
    });
    canvas.dispatchEvent(me);
  }, false);

  canvas.addEventListener("touchend", function(e) {
    var me = new MouseEvent("mouseup", {});
    canvas.dispatchEvent(me);
  }, false);

  function getMousePos(canvasDom, mouseEvent) {
    var rect = canvasDom.getBoundingClientRect();
    return {
      x: mouseEvent.clientX - rect.left,
      y: mouseEvent.clientY - rect.top
    }
  }

  function getTouchPos(canvasDom, touchEvent) {
    var rect = canvasDom.getBoundingClientRect();
    return {
      x: touchEvent.touches[0].clientX - rect.left,
      y: touchEvent.touches[0].clientY - rect.top
    }
  }

  function renderCanvas() {
    if (drawing) {
      ctx.moveTo(lastPos.x, lastPos.y);
      ctx.lineTo(mousePos.x, mousePos.y);
      ctx.stroke();
      lastPos = mousePos;
    }
  }

  // Prevent scrolling when touching the canvas
  document.body.addEventListener("touchstart", function(e) {
    if (e.target == canvas) {
      e.preventDefault();
    }
  }, false);
  document.body.addEventListener("touchend", function(e) {
    if (e.target == canvas) {
      e.preventDefault();
    }
  }, false);
  document.body.addEventListener("touchmove", function(e) {
    if (e.target == canvas) {
      e.preventDefault();
    }
  }, false);

  (function drawLoop() {
    requestAnimFrame(drawLoop);
    renderCanvas();
  })();

  function clearCanvas() {
    canvas.width = canvas.width;
  }

  // Set up the UI
  var sigText = document.getElementById("sig-dataUrl");
  
  var clearBtn = document.getElementById("sig-clearBtn");
  
  clearBtn.addEventListener("click", function(e) {
    clearCanvas();
    $("#save_esign").css("display",'none');
    sigText.innerHTML = " ";
  }, false);

  $("#sig-submitBtn").click(function(){
    var dataUrl = canvas.toDataURL();
    $("#sig-dataUrl").val(dataUrl);
    data = $("#sig-dataUrl").val()
    console.log("data Collected")
    console.log(data)
    $.ajax({
        url:"/driver_sign/",
        type:'post',
        data:{'signature':data},
        success: function(resp){
            console.log(resp)
            if(resp.status == 1){
                $("#btnClosePop").click();
                }else{
                    alert(resp.error)
                }
        }
    })
  })

})();
</script>
{% endblock%}
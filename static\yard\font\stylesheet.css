/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on November 24, 2020 */



@font-face {
    font-family: 'cerebri_sans_pro_boldbold';
    src: url('cerebrisanspro-bold.woff2') format('woff2'),
         url('cerebrisanspro-bold.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_proextrabold';
    src: url('cerebrisanspro-extrabold.woff2') format('woff2'),
         url('cerebrisanspro-extrabold.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_proextralight';
    src: url('cerebrisanspro-extralight.woff2') format('woff2'),
         url('cerebrisanspro-extralight.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_proheavy';
    src: url('cerebrisanspro-heavy.woff2') format('woff2'),
         url('cerebrisanspro-heavy.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_prolight';
    src: url('cerebrisanspro-light.woff2') format('woff2'),
         url('cerebrisanspro-light.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_promedium';
    src: url('cerebrisanspro-medium.woff2') format('woff2'),
         url('cerebrisanspro-medium.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_proregular';
    src: url('cerebrisanspro-regular.woff2') format('woff2'),
         url('cerebrisanspro-regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_prosemibold';
    src: url('cerebrisanspro-semibold.woff2') format('woff2'),
         url('cerebrisanspro-semibold.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'cerebri_sans_prothin';
    src: url('cerebrisanspro-thin.woff2') format('woff2'),
         url('cerebrisanspro-thin.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}
<!DOCTYPE html>
<html lang="en">
<head>
    {%block head%}
    {%load static%}
    {%endblock%}
    {% load i18n %}
    {% load l10n %}
    {%load custom_template_filters%}
    <meta charset="utf-8">
    <title>Contract</title>

    <!-- <link rel="stylesheet" href="{% static 'yard/css/bootstrap.min.css'%}">
    <link rel="stylesheet" href="{% static 'yard/css/custom.css'%}">
    <link rel="stylesheet" href="{% static 'yard/css/jquery.dataTables.min.css'%}">
    <link rel="stylesheet" href="{% static 'yard/css/select2.min.css'%}">
    <script src="{% static 'yard/js/jquery.min.js'%}"></script>
    <script src="{% static 'yard/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'yard/js/bootstrap.min.js'%}"></script>
    <script src="{% static 'yard/js/select2.min.js'%}"></script> -->

    <!-- Bootstrap CSS -->
  
  <!-- <link rel="stylesheet" href="{% static 'yard/css/bootstrap4.min.css'%}">
  <link rel="stylesheet" href="{% static 'yard/css/style.css'%}">
  <link rel="stylesheet" href="{% static 'yard/css/jquery-datables-min.css.css'%}">
  {% comment %} <link rel="stylesheet" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.min.css"> {% endcomment %}
  <link rel="stylesheet" href="{% static 'yard/css/jquery.dataTables.min.css'%}">
  {% comment %} <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.8.1/css/all.min.css"> {% endcomment %}
  <link rel="stylesheet" href="{% static 'yard/css/all.min.css'%}">
  {% comment %} <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet" /> {% endcomment %}
  <link rel="stylesheet" href="{% static 'yard/css/select2.min.css'%}">
  <link rel="stylesheet" href="{% static 'yard/css/utils.css'%}"> -->

    <style>
    span {
        font-size:13px;
    }
    .Row {
        display: table;
        width: 100%; /*Optional*/
        table-layout: fixed; /*Optional*/
        border-spacing: 40px; /*Optional*/
    }
    .Column {
        display: table-cell;
        border-left: 10px;
    }
    table { -pdf-keep-with-next: true; }
    p { margin: 0; -pdf-keep-with-next: true; }
    p.separator { -pdf-keep-with-next: false; font-size: 6pt; }

    .header_logo{
        text-align: right;
        padding-bottom: 20px; 
        padding-right: 20px;
        width:100%;
        height:auto;
    }
    .header_logo img{
        max-width: 100%;
        max-height: auto;
    }
    .right-column {
        width: 150px;
        text-align: right;
        padding-top: -150px;
    }
    .title{
        font-size: 16px;
    }
    .main_date{
        padding-top: 30px;
    }
    .year{
        text-align: left;
        padding-top: 40px;
    }
    .month{
        text-align: center;
        padding-top: -46px;
    }
      .day1{
        text-align: left;
        padding-top: -50px;
        padding-left: 460px;
    }
    .day{
        text-align: left;
        padding-top: -50px;
        padding-left: 590px;
    }
    .table-title{
        padding-top: 20px;
    }
    .table-title b{
        font-size: 20px;
    }
    .main_table{
        margin-top: 30px;
    }
    .table{
        width:100%;
    }
    .main_table .table .tr .th{
        border-top: 1px solid #000;
        border-bottom: 1px solid #000;
        padding: 4px;
        text-align: left;
    }
    .main_table .table .tr .td{
        text-align: left;
    }
    .footer{
        padding-top: 60px;
    }
    .footer .footer_first span{
        font-size: 10px;
    }

    </style>
</head>
   <body>
       <!-- <button type="button" class="btn btn-success">cick</button> -->
      <div class="">
         <div class="col-md-12" style="padding-top:20%;">
            <div class="col-md-6" >
                <div class="mt-5" >
                {% for head in logo %}
                        <font color="orange">{{head.heading|linebreaksbr}}</font>
                        {% endfor %}
                </div>

            </div>
            <div class="col-md-6">
                <div class="header_logo">
{#                <img src="/static/yard/images/logo.png"/>#}
                {% for img in logo %}
                   {% if img.logo.url is not None %}
                   <img src="{{ img.logo.url }}" style="object-fit: contain;height: 100px; width: 170px;"/>
                   {% endif %}
                  {% endfor %}
                </div>
            </div>
         </div>
            
            <div class="left-column">
            
                <br>
                <br>
                <strong class="title">{{ contract.customer.name1 }} </strong><br>
                <span>
                {% if contract.customer.street is not None %}
                    {{ contract.customer.street }} <br> {% endif %}
                {% if contract.customer.place is not None %}
                    {{ contract.customer.place }} <br> {% endif %}
                {% if contract.customer.post_office_box is not None %}
                    {{ contract.customer.post_office_box }} {% endif %}
                </span><br> <br>
                <b>Baustelle:</b> <br>
                <span>{{supplier.supplier_name}}</span><br>
            <br>
            </div>

            <div class="right-column">
                <img src="data:image/png;base64, {{ qr_code }}"/>
{#                   {% for img in logo %}#}
{#                   {% if img.logo.url is not None %}#}
{#                   <img src="{{ img.logo.url }}" style="width:200%;height:10%;"/>#}
{#                   {% endif %}#}
{#                  {% endfor %}#}
            </div>



            <div class="main_date">
                <div class="year" style="font-size:15px;">
                    <b>Auftragnummer:</b> <br>
                    <span>{{ contract.contract_number }} </span>
                </div> 
               
                <div class="month" style="font-size:15px;">
                    <b>Startdatum:</b> <br>
                    <span>{{ contract.start_date|date:"d.m.y"|default_if_none:"-" }}</span>
                </div>

                  <div class="day1" style="font-size:15px;">
                    <b>Enddatum:</b> <br>
                    <span>{{ contract.end_date|date:"d.m.y"|default_if_none:"-" }}</span>
                </div>

                <div class="day" style="font-size:15px;">
                    <b>Erstelltdatum:</b> <br>
                    <span>{{ contract.reserved_date|date:"d.m.y"|default_if_none:"-" }}</span>
                </div> 
            </div>
<br><br><br><br>
            <div class="table-title">
                <b>Auftragsbestätigung {{contract.name}}</b> <br> <br>
                <span>Wir bedanken uns für Ihren Auftrag. </span>
            </div>

            <div class="main_table">
                <table class="table">
                    <tr class="tr">
                        <th class="th" style="width: 50px;font-size:13px;">Pos.</th>
                        <th class="th" style="font-size:13px;">Material</th>
                        <th class="th" style="font-size:13px;">Bruttopreis</th>
                         <th class="th" style="font-size:13px;">Aritkelnummer</th>
                        <th class="th" style="font-size:13px;">Menge</th>
                        <th class="th" style="font-size:13px;">Gesammtpreis</th>
                    </tr>
                    {% for material in materials %}
                        <tr style="padding-top: 6px;">
                            <td class="td" style="margin-left: 15px;font-size:13px;">{{ material.material.id }}</td>
                            <td class="td" style="margin-left: 9px;font-size:13px;">{{ material.material.name }}</td>
                             <td class="td" style="margin-left: 9px;font-size:13px;">
                                 {% if contract.price_group == "price1" %}
                                     {{ material.material.price1 }}
                                      {% elif contract.price_group == "price2" %}
                                     {{ material.material.price2 }}
                                      {% elif contract.price_group == "price3" %}
                                     {{ material.material.price3 }}
                                      {% elif contract.price_group == "price4" %}
                                     {{ material.material.price4 }}
                                      {% elif contract.price_group == "price5" %}
                                     {{ material.material.price5 }}
                                 {% endif %}
                             </td>
                            <td class="td" style="margin-left: 9px;font-size:13px;">{{ material.material.article_number }}</td>
                            <td class="td" style="margin-left: 8px;font-size:13px;">{{ material.agreed_value }} kg</td>
                         <td class="td" style="margin-left: 8px;font-size:13px;">
                                       0
                         </td>
                        </tr>
                    {% endfor %}
{#                    <tr>#}
{#                        <td class="td" style="margin-left: 15px;">2</td>#}
{#                        <td class="td" style="margin-left: 9px;">Material 2</td>#}
{#                        <td class="td" style="margin-left: 8px;">10 Ton</td>#}
{#                    </tr>#}
                </table>
            </div>


            <div class="footer">
                <div class="footer_first">
                    <span>Sie haben noch Fragen? Sie erreichen uns täglich von 07:00 bis 17:00 unter {{ tele_phone_number.tele_phone_number }} oder per E-Mail  {% if email.company_email is not None %} {{email.company_email}} {% else %} {%endif %}</span>
                        <br><br>
                    <span>Wir freuen uns auf die Zusammenarbeit </span>
                        <br><br>
                    <span>Mit freundlichen Grüßen</span>
                </div>
            </div>
        </div>
      </div>
   </body>
</html>

# Generated by Django 3.1.1 on 2021-02-19 10:08

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import yard.managers


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.CharField(blank=True, max_length=250, null=True)),
                ('short_name', models.CharField(blank=True, max_length=40, null=True)),
                ('entry_weight', models.DecimalField(decimal_places=3, default=0.0, max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('balance_weight', models.DecimalField(decimal_places=3, default=0.0, max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('outgoing_weight', models.DecimalField(decimal_places=3, default=0.0, max_digits=10, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('price1', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price2', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price3', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price4', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price5', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount', models.CharField(blank=True, max_length=100, null=True)),
                ('group', models.PositiveIntegerField(choices=[(1, 'Type 1'), (2, 'Type 2'), (3, 'Type 3'), (4, 'Type 4'), (5, 'Type 5'), (6, 'Other Type')], default=6)),
                ('vat', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('minimum_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('avv_num', models.CharField(blank=True, max_length=250, null=True)),
                ('account', models.CharField(blank=True, max_length=250, null=True)),
                ('cost_center', models.CharField(blank=True, max_length=250, null=True)),
                ('unit', models.CharField(blank=True, max_length=250, null=True)),
                ('min_quantity', models.PositiveIntegerField(blank=True, null=True)),
                ('revenue_group', models.CharField(blank=True, choices=[('revenue1', 'Revenue 1'), ('revenue2', 'Revenue 2'), ('revenue3', 'Revenue 3'), ('revenue4', 'Revenue 4'), ('revenue5', 'Revenue 5')], max_length=250, null=True)),
                ('revenue_account', models.CharField(blank=True, max_length=250, null=True)),
                ('list_price_net', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('ean', models.CharField(blank=True, max_length=250, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='BuildingSite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('short_name', models.CharField(blank=True, max_length=40, null=True)),
                ('place', models.CharField(blank=True, max_length=100, null=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('pin', models.CharField(blank=True, max_length=100, null=True)),
                ('infotext', models.CharField(max_length=100)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=40, unique=True)),
                ('firstname', models.CharField(blank=True, max_length=40, null=True)),
                ('company', models.CharField(blank=True, max_length=40, null=True)),
                ('salutation', models.CharField(choices=[('Mr', 'Mr'), ('Mrs', 'Mrs'), ('Dr', 'Dr')], max_length=10)),
                ('addition1', models.CharField(blank=True, max_length=40, null=True)),
                ('addition2', models.CharField(blank=True, max_length=40, null=True)),
                ('addition3', models.CharField(blank=True, max_length=40, null=True)),
                ('post_office_box', models.CharField(blank=True, max_length=40, null=True)),
                ('description', models.CharField(blank=True, max_length=250, null=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('pin', models.CharField(blank=True, max_length=10, null=True)),
                ('fax', models.CharField(blank=True, max_length=15, null=True)),
                ('place', models.CharField(blank=True, max_length=100, null=True)),
                ('website', models.CharField(blank=True, max_length=100, null=True)),
                ('cost_centre', models.PositiveIntegerField(default=1, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_person1_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person2_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person3_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person1_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person2_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person3_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('diff_invoice_recipient', models.CharField(blank=True, max_length=40, null=True)),
                ('customer_type', models.CharField(blank=True, max_length=40, null=True)),
                ('price_group', models.CharField(choices=[('price1', 'Price 1'), ('price2', 'Price 2'), ('price3', 'Price 3'), ('price4', 'Price 4'), ('price5', 'Price 5')], max_length=10)),
                ('classification', models.CharField(blank=True, max_length=40, null=True)),
                ('sector', models.CharField(blank=True, max_length=40, null=True)),
                ('company_size', models.CharField(blank=True, max_length=40, null=True)),
                ('area', models.CharField(blank=True, max_length=40, null=True)),
                ('private_person', models.BooleanField(default=False)),
                ('document_lock', models.BooleanField(default=False)),
                ('payment_block', models.BooleanField(default=False)),
                ('delivery_terms', models.CharField(choices=[('free', 'Free'), ('paid', 'Paid'), ('other', 'Other')], max_length=10)),
                ('special_discount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('debitor_number', models.PositiveIntegerField(blank=True, null=True)),
                ('dunning', models.CharField(blank=True, max_length=10, null=True)),
                ('perm_street', models.CharField(blank=True, max_length=100, null=True)),
                ('perm_pin', models.CharField(blank=True, max_length=10, null=True)),
                ('perm_place', models.CharField(blank=True, max_length=100, null=True)),
                ('perm_country', models.CharField(blank=True, max_length=100, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Delivery_note',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lfd_nr', models.CharField(max_length=100)),
                ('file_name', models.CharField(max_length=100)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Forwarders',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('firstname', models.CharField(blank=True, max_length=100, null=True)),
                ('second_name', models.CharField(blank=True, max_length=100, null=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('pin', models.CharField(blank=True, max_length=10, null=True)),
                ('telephone', models.CharField(blank=True, max_length=15, null=True)),
                ('place', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('supplier_name', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(blank=True, max_length=40, null=True)),
                ('first_name', models.CharField(blank=True, max_length=40, null=True)),
                ('street', models.CharField(blank=True, max_length=100, null=True)),
                ('pin', models.CharField(blank=True, max_length=10, null=True)),
                ('fax', models.CharField(blank=True, max_length=15, null=True)),
                ('place', models.CharField(blank=True, max_length=100, null=True)),
                ('infotext', models.CharField(blank=True, max_length=100, null=True)),
                ('salutation', models.CharField(choices=[('Mr', 'Mr'), ('Mrs', 'Mrs'), ('Dr', 'Dr')], max_length=10)),
                ('addition1', models.CharField(blank=True, max_length=40, null=True)),
                ('addition2', models.CharField(blank=True, max_length=40, null=True)),
                ('addition3', models.CharField(blank=True, max_length=40, null=True)),
                ('post_office_box', models.CharField(blank=True, max_length=40, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_person1_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person2_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person3_email', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person1_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person2_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('contact_person3_phone', models.CharField(blank=True, max_length=40, null=True)),
                ('website', models.CharField(blank=True, max_length=100, null=True)),
                ('cost_centre', models.PositiveIntegerField(default=1, null=True)),
                ('creditor_number', models.PositiveIntegerField(blank=True, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('stock_designation', models.CharField(blank=True, max_length=100, null=True)),
                ('stock_number', models.CharField(blank=True, max_length=100, null=True)),
                ('stock_item', models.BooleanField(default=True)),
                ('locked_warehouse', models.BooleanField(default=True)),
                ('ordered', models.BooleanField(default=True)),
                ('production', models.CharField(blank=True, max_length=100, null=True)),
                ('reserved', models.CharField(blank=True, max_length=100, null=True)),
                ('available', models.PositiveIntegerField(blank=True, null=True)),
                ('total_stock', models.PositiveIntegerField(blank=True, null=True)),
                ('store', models.PositiveIntegerField(blank=True, null=True)),
                ('outsource', models.PositiveIntegerField(blank=True, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Yard_list',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('street', models.CharField(blank=True, max_length=40, null=True)),
                ('pin', models.CharField(blank=True, max_length=10, null=True)),
                ('place', models.CharField(blank=True, max_length=40, null=True)),
                ('country', models.CharField(blank=True, max_length=40, null=True)),
                ('telephone', models.CharField(blank=True, max_length=15, null=True)),
                ('email', models.CharField(blank=True, max_length=40, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('license_plate', models.CharField(max_length=100, unique=True)),
                ('license_plate2', models.CharField(blank=True, max_length=100, null=True)),
                ('group', models.PositiveIntegerField(blank=True, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('telephone', models.CharField(blank=True, max_length=20, null=True)),
                ('vehicle_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('vehicle_weight_id', models.CharField(blank=True, max_length=100, null=True)),
                ('taken', models.PositiveIntegerField(blank=True, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('vehicle_type', models.CharField(blank=True, choices=[('type1', 'Type 1'), ('type2', 'Type 2'), ('type3', 'Type 3'), ('type4', 'Type 4'), ('type5', 'Type 5')], max_length=10, null=True)),
                ('cost_center', models.CharField(blank=True, max_length=100, null=True)),
                ('owner', models.CharField(blank=True, max_length=100, null=True)),
                ('driver_name', models.CharField(blank=True, max_length=100, null=True)),
                ('trailor_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('forwarder', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.forwarders')),
            ],
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('second_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('lfd_nr', models.CharField(blank=True, max_length=40, null=True)),
                ('firstw_date_time', models.DateTimeField(blank=True, null=True)),
                ('secondw_date_time', models.DateTimeField(blank=True, null=True)),
                ('firstw_alibi_nr', models.CharField(blank=True, max_length=40, null=True)),
                ('secondw_alibi_nr', models.CharField(blank=True, max_length=40, null=True)),
                ('vehicle_weight_flag', models.BooleanField(default=False)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('trans_flag', models.PositiveIntegerField(choices=[(0, 'Initial_Weighing'), (1, 'Second_Weighing'), (2, 'Closed_Weighing')], null=True)),
                ('price_per_item', models.DecimalField(decimal_places=2, max_digits=10)),
                ('article', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.article')),
                ('customer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.customer')),
                ('supplier', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.supplier')),
                ('vehicle', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.vehicle')),
                ('yard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list')),
            ],
        ),
        migrations.AddField(
            model_name='supplier',
            name='warehouse',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.warehouse'),
        ),
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=40, null=True, unique=True)),
                ('customer', models.CharField(choices=[('Kunde', 'Kunde'), ('Erzeuger', 'Erzeuger'), ('Customer', 'Customer')], max_length=40, null=True)),
                ('supplier', models.CharField(choices=[('Lieferant', 'Lieferant'), ('Baustelle', 'Baustelle'), ('Schlag', 'Schlag'), ('Supplier', 'Supplier')], max_length=40, null=True)),
                ('article', models.CharField(choices=[('Material', 'Material'), ('Produkt', 'Produkt'), ('kultur', 'Kultur'), ('article', 'Article')], max_length=40, null=True)),
                ('show_article', models.BooleanField(default=True)),
                ('show_supplier', models.BooleanField(default=True)),
                ('show_yard', models.BooleanField(default=True)),
                ('show_forwarders', models.BooleanField(default=True)),
                ('show_storage', models.BooleanField(default=True)),
                ('show_building_site', models.BooleanField(default=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('read_number_from_camera', models.BooleanField(default=False)),
                ('language', models.CharField(choices=[('en-us', 'English'), ('de', 'German'), ('fr', 'French'), ('ru', 'Russian')], default='en-us', max_length=40, null=True)),
                ('yard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list')),
            ],
        ),
        migrations.CreateModel(
            name='images_base64',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image1', models.ImageField(null=True, upload_to='')),
                ('image2', models.ImageField(null=True, upload_to='')),
                ('image3', models.ImageField(null=True, upload_to='')),
                ('transaction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.transaction')),
            ],
        ),
        migrations.AddField(
            model_name='customer',
            name='warehouse',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.warehouse'),
        ),
        migrations.CreateModel(
            name='Container',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('container_type', models.CharField(blank=True, max_length=40, null=True)),
                ('group', models.PositiveIntegerField(blank=True, null=True)),
                ('container_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('volume', models.CharField(blank=True, max_length=40, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('last_site', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.buildingsite')),
            ],
        ),
        migrations.CreateModel(
            name='Combination',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ident', models.CharField(max_length=40, unique=True)),
                ('short_name', models.CharField(blank=True, max_length=40, null=True)),
                ('created_date_time', models.DateTimeField(auto_now_add=True)),
                ('updated_date_time', models.DateTimeField(auto_now=True)),
                ('article', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.article')),
                ('building_site', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.buildingsite')),
                ('customer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.customer')),
                ('forwarders', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.forwarders')),
                ('supplier', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.supplier')),
                ('vehicle', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.vehicle')),
                ('yard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list')),
            ],
        ),
        migrations.AddField(
            model_name='article',
            name='supplier',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.supplier'),
        ),
        migrations.AddField(
            model_name='article',
            name='ware_house',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.warehouse'),
        ),
        migrations.AddField(
            model_name='article',
            name='yard',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list'),
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('name', models.CharField(blank=True, max_length=30, verbose_name='name')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='email address')),
                ('date_joined', models.DateTimeField(auto_now_add=True, verbose_name='date joined')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('is_staff', models.BooleanField(default=True, verbose_name='staff status')),
                ('role', models.CharField(blank=True, max_length=30, verbose_name='role')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
                ('yard', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list')),
            ],
            options={
                'abstract': False,
            },
            managers=[
                ('objects', yard.managers.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Article_meta',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('balance_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('outgoing_weight', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('article', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.article')),
                ('yard', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list')),
            ],
            options={
                'unique_together': {('article', 'yard')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='article',
            unique_together={('name', 'yard')},
        ),
    ]

body {
    background-color: #fff;
  
}

.navbar-custom {
    background-image: linear-gradient(to bottom, #902c0f 0%, #d16343 37%, #852a0f 100%);
    background-color: #943c2e;
    z-index:1;
}

.navbar-nav.navbar-center {
    position: absolute;
    left: 50%;
    transform: translatex(-50%);
}
/* change the brand and text color */
.navbar-custom .navbar-brand,
.navbar-custom .navbar-text {
    color: #F827F6;
    padding: 0.8rem 1rem;
}
/* change the link color and add padding for height */
.navbar-custom .navbar-nav .nav-link {
    color: #ffffff;
    padding: 1rem 1rem;
}
/* change the color of active or hovered links */
.navbar-custom .nav-item.active .nav-link,
.navbar-custom .nav-item:hover .nav-link {
    color: #ffffff;
    background-color: #943c2e; /* add background-color to active links */
}



/* for dropdown only - change the color of droodown */
.navbar-custom .dropdown-menu {
    background-color: #943c2e;
}

.navbar-custom .dropdown-item {
    color: #ffffff;
}

    /*a {
    color: #0000ff;
}*/

    .navbar-custom .dropdown-item:hover,
    .navbar-custom .dropdown-item:focus {
        color: #333333;
        background-color: rgba(255,255,255,.5);
    }

.nav > li > a:focus, .nav > li > a:hover {
    text-decoration: none;
    background-color: transparent !important;
    color: yellow;
    
}

.nav .open > a, .nav .open > a:focus, .nav .open > a:hover {
    text-decoration: none;
    background-color: transparent !important;
    color: yellow;
    
}

.dropdown-menu > li > a:focus, .dropdown-menu > li > a:hover {
    text-decoration: none;
    background-color: transparent !important;
    color: yellow;
   
}

.navbar-right {
    margin-right: 0px;
}
.row {
     margin-right: 0px; 
     margin-left: 0px; 
}
.nav-custom {
    background-color: #943c2e;
}

nav a:link {
    color: #ffffff;
}

nav a:visited {
    color: #ffffff;
}

.nav-logo {
    height: 150%;
}

.nav-flag {
    width: 47px;
    height: 28px;
}

.nav-margin {
    margin-right: 50px;
    margin-left: 20px;
}

.cus-button-1 {
    background-color: #cb5f3f;
    color: #ffffff;
    font-size: 13px;
    border: none;
    padding: 5px;
    border-radius: 0.25rem;
    background-image: linear-gradient(to bottom, #e26d4b 0%, #a94a2e 37%, #da6644 100%);
}

    .cus-button-1:hover {
        color: #fff;
        text-decoration: none;
        background: #a94a2e;
    }

.container-custom {
    background-color: #80808033;
    margin-top: 10px;
    padding-top: 10px;
    background-repeat: no-repeat;
    background-image: url(http://www.rw-datanet.com/Weighing/wlogo_trans_matt.png);
    background-position: center;
    min-height: 550Px;
}

.thead-dark {
    background-color: #cb5f3f;
}

.aclist {
    margin-left: 3%;
    padding: 10px;
}

.ipt1 {
    padding-top: 5px;
    padding-bottom: 5px;
    margin-left: 2px;
}

.pan_search {
    color: #000;
    height: 25px;
}

.pan_search_btn {
    height: 20px;
    background-color: #cb5f3f;
    color: #ffffff;
    border: none;
}

    .pan_search_btn:hover {
        color: #fff;
        text-decoration: none;
        background: #a94a2e;
    }

.btn-weig {
    margin-top: 1px;
    height: 23px;
    background-color: #ddd;
    color: #777;
}



.accordion {
    background-color: #faac58;
    background-image: linear-gradient(to bottom, #e26d4b 0%, #f3a857 37%, #da6644 100%);
    color: #000;
    cursor: pointer;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 14px;
    font-weight: bold;
    transition: 0.4s;
    border-radius: 3px 3px 0px 0px;
    padding: 2px 4px;
    border-left: 3px solid #ff0000;
    min-height: 30px;
    /* background-color: #943c2e;
           color: #ffffff;
           cursor: pointer;
           width: 100%;
           border: none;
           text-align: left;
           outline: none;
           font-size: 20px;
           transition: 0.4s;*/
}

    .accordion:after {
        content: '\2304';
        display: inline-block;
        vertical-align: middle;
        background: #cb5f3f;
        -moz-border-radius: 10px;
        -webkit-border-radius: 10px;
        border-radius: 10px;
        color: White;
        text-align: center;
        font-size: 9px;
        /*font-weight: bold;*/
        padding: 2px 5px;
        float: left;
        margin: 2px 5px;
        /* content: '\2304';
        color: #777;
        font-weight: bold;
        float: left;
        margin-left: 5px;*/
    }

.active:after {
    content: "\2303";
}

.panel {
    background-color: white;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
    border-radius: 0px;
    margin-bottom: 10px;
}

.panel-default {
    border-color: #ff0000;
}

.bdr {
    border: 1;
}

.edit-form {
    margin-left: 3%;
}

 .ui-autocomplete {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    float: left;
    display: none;
    min-width: 160px;   
    padding: 4px 0;
    margin: 0 0 10px 25px;
    list-style: none;
    background-color: #ffffff;
    border-color: #ccc;
    border-color: rgba(0, 0, 0, 0.2);
    border-style: solid;
    border-width: 1px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    *border-right-width: 2px;
    *border-bottom-width: 2px;
}

.ui-menu-item > a.ui-corner-all {
    display: block;
    padding: 3px 15px;
    clear: both;
    font-weight: normal;
    line-height: 18px;
    color: #555555;
    white-space: nowrap;
    text-decoration: none;
}

.ui-state-hover, .ui-state-active {
    color: #ffffff;
    text-decoration: none;
    background-color: rgb(250, 172, 88);
    border-radius: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    background-image: none;
}

.loading 
{
    background-image: url("images/loading.gif");
    background-size: 50px 50px;
    background-position:right center;
    background-repeat: no-repeat;
    /*background: red;*/
}


.weigh_scale{
  font-size: 40px;
  color:#12b143;
  border:1px;
  margin-left: 25%;
}
.weigh_scale_label{
  font-size: 20px;
}
.tab-col{
  text-align: center;
}
.weig-table{
  width:100%;
  background-color: #f1f1f1;
}
.tab-input{
  width:90%;
  text-align: center;
}
.tab-btn-padding{
  padding-bottom: 3px;
  padding-top: 3px;
}
.form-radio{
    display: inline-flex;
    list-style-type: none;
    margin-left: -10%;
}
.errorlist{
  color: red;
}


.modal-header{
    background-color: #faac58;
    background-image: linear-gradient(to bottom, #e26d4b 0%, #f3a857 37%, #da6644 100%);
    color: #000;
    cursor: pointer;
    width: 100%;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
    border-radius: 3px 3px 0px 0px;
    padding: 2px 4px;
    border-left: 3px solid #ff0000;
    min-height: 30px;
}
.modal-content{
height:200%;
width:210%;
}
.modal-dialog{
        position: absolute;
    margin: 120px;
}
.modal-footer {
    padding: 4px;
}


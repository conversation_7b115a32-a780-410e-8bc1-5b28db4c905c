{% extends 'base2.html' %}
{% load crispy_forms_tags %}
{% load custom_template_filters %}
{% load l10n %}
{%load i18n%}
{% block content %}
<div class="container">
  <button type="button" id="sidebarCollapse" class="btn btn-primary ml-auto">
  <i class="fas fa-align-justify"></i>
  </button>
  <div class="row  border border-top-0 border-left-0 border-right-0 mb-3">
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
      <div class="content_text">
        <p class="mb-0">{% translate 'OVERVIEW' %}</p>
      </div>
      <div class="heding">
        <p>{% translate 'Container' %}</p>
      </div>
    </div>
  </div>
  <!--table strat-->
  <div class="row">
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12" >
      <label>{% translate "Show entries" %}:</label>
      <select class="form-control w-30" id="showentries">
        <option>10</option>
        <option>25</option>
        <option>50</option>
        <option>100</option>
        entries
      </select>
    </div>
    <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
      <label>{% translate "Search" %}:</label>
      <input class="form-control mr-sm-2" type="text" placeholder="{% translate 'Search' %}" aria-label="Search" id="mysearch">
    </div>
  </div>
  <div class="table-responsive">
    <table class="table table-striped table-hover table-bordered mt-3 building_site" width="100%" id="containerTable">
      <thead>
        <tr>{% if request.user.is_superuser %}
          <th>{% translate "Action" %}</th> {% endif%}
{#          <th>{{form.name.label}}</th>#}
          <th>{{form.container_number.label}}</th>
          <th>{{form.container_type.label}}</th>
         <th>{{form.check_digit.label}}</th>
          <th>{{form.container_weight.label}} [kg]</th>
          <th>{{form.maximum_gross_weight.label}} [kg]</th>
          <th>{{form.tare_weight.label}} [kg]</th>
         <th>{{form.curb_weight.label}} [kg]</th>
        <th>{{form.total_weight.label}} [kg]</th>
        <th>{{form.last_site.label}} [kg]</th>
        <th>{{ "Nutzlast" }} [kg]</th>
{#          <th>{{form.volume.label}}</th>#}
          <th>{{form.payload_container_volume.label}}</th>
{#          <th>{{form.group.label}}</th>#}
          <th>{{form.next_exam.label}}</th>
{#          <th>{{form.waste_type.label}}</th>#}
          <th>{{form.hazard_warnings.label}}</th>
          
        </tr>
      </thead>
      <tbody class="mt-4">
        {% for data in dataset %}
        <tr class="loadCD" ondblclick="javascript:loadContainerDetails('{{ data.id }}')">
        {% if request.user.is_superuser %}
          <td><a class="loadC" href="javascript:loadContainerDetails('{{ data.id }}')" ><i class="fas fa-pencil-alt text-primary  ml-4"></i></a><a class="confirmdelete" href="{% url 'container_delete' identifier=data.id  %}" ><i class="fas fa-trash-alt ml-1 text-danger"></i></a></td>
          {% endif %}
{#          <td>{{ data.name|default_if_none:" " }}</td>#}
          <td>{{ data.container_number|default_if_none:" " }}</td>
          <td>{{ data.container_type|default_if_none:" " }}</td>
         <td>{{ data.check_digit|default_if_none:" " }}</td>
          <td>{{ data.container_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
          <td>{{ data.maximum_gross_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
          <td>{{ data.tare_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
         <td>{{ data.curb_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
         <td>{{ data.total_weight|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
         <td>{{ data.last_site|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
         <td>{{ data.payload|convert_to_kg|unlocalize|default_if_none:"0" }}</td>
{#          <td>{{ data.volume|default_if_none:"0" }}</td>#}
          <td>{{ data.payload_container_volume|default_if_none:"0" }}</td>
{#          <td>{{ data.group|default_if_none:" " }}</td>#}
          <td>{{ data.next_exam|default_if_none:" " }}</td>
{#          <td>{{ data.waste_type|default_if_none:" " }}</td>#}
          <td>{{ data.hazard_warnings|default_if_none:" " }}</td>

        </tr>
        {% endfor %}
<!--      <tfoot hidden>-->
<!--        <tr>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text"class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--          <th rowspan="1" colspan="1">-->
<!--            <input type="text" class="form-control">-->
<!--          </th>-->
<!--        </tr>-->
<!--      </tfoot>-->
      </tbody>
    </table>
  </div>
</div>
<!--table end-->
<div class="container">
  <div class="row mb-5">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
      <!-- Material form login -->
      <div class="card p-3 mt-4" id="container-form-section">
           {% if request.user.is_superuser %}
        <div class="row">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
            <h5 class="card-header info-color white-text py-3">
              <div class="panel-heading">
                <h4 class="panel-title">
                  {% comment %} <a data-toggle="collapse" data-parent="#accordion" href="#collapse_18"> {% endcomment %}
                    <div class="row">
                      <div class="col-xl-12 col-lg-6 col-md-6 col-sm-12 col-12">
                        <p class="mb-0 pt-2 mr-3 text-color text_color float-left">{% translate 'Container' %}</p>
                        <button type="button" id="new_entry" class="btn btn-blue btn-blue-fill" style="float:right;">Neue Eingabe</button>
                      </div>
                    </div>
                  {% comment %} </a> {% endcomment %}
                </h4>
              </div>
            </h5>
          </div>
        </div>
          {% endif %}
        <div id="collapse_18" class="collapse" >
          <div class="panel-body">
            <form class="form-group" method="POST" enctype="multipart/form-data">
              {% csrf_token %}
              <input type="hidden" name="id" id="id">
              <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
{#                  <div >#}
{#                    <label>{{form.name.label}}</label>#}
{#                    {{ form.name}}{{ form.name.errors}}#}
{#                  </div>#}
                  <div>
                    <label>{{form.container_type.label}}</label>
                    <select name="container_type" id="id_container_type" class="form-control">
                        <option>------</option>
                        <option value="20′">20′</option>
                        <option value="40′">40′</option>
                        <option value="45′HC">45′HC</option>
                        <option value="45′PW">45′PW</option>
                        <option value="48′HC">45′HC</option>
                         <option value="53′HC">53′HC</option>
                    </select>
                      {{form.container_type.errors}}
                  </div>
                     <div>
                    <label>{{ "Prüfziffer" }}</label>
                    {{ form.check_digit }} {{ form.check_digit.errors}}
                  </div>
                  <div>
                    <label>{{form.container_number.label}}</label>
                    {{ form.container_number }} {{ form.container_number.errors}}
                  </div>
{#                  <div>#}
{#                    <label>{{form.volume.label}} </label>#}
{#                    {{ form.volume }} {{ form.volume.errors}}#}
{#                  </div>#}
                  <div>
                    <label>{{form.payload_container_volume.label}}</label>
                    {{ form.payload_container_volume }} {{ form.payload_container_volume.errors}}
                  </div>
                       <div>
                    <label>{{ "Nutzlast (kg)" }}</label>
                    {{ form.payload }} {{ form.check_digit.errors}}
                  </div>
                  <div>
                    <label>{% translate 'Nächste Prüfung' %}</label>
                    {{ form.next_exam }} {{ form.next_exam.errors}}
                  </div>
{#                  <div>#}
{#                    <label>{{form.unit.label}}</label>#}
{#                    {{ form.unit }} {{ form.unit.errors}}#}
{#                  </div>#}
                </div>
                <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12 text-left mt-4">
{#                  <div >#}
{#                    <label>{{form.group.label}}</label>#}
{#                    {{form.group}} {{form.group.errors}}#}
{#                  </div>#}
                  <div>
                    <label>{{form.container_weight.label}}</label>
                    {{ form.container_weight }} {{ form.container_weight.errors}}
                  </div>
                       <div>
                    <label>{{ "Leergewicht (kg)" }}</label>
                    {{ form.curb_weight }} {{ form.check_digit.errors}}
                  </div>

                      <div>
                    <label>{{ "Gesamtgewicht (kg)" }}</label>
                    {{ form.total_weight }} {{ form.check_digit.errors}}
                  </div>

                  <div>
                  <label>{{form.maximum_gross_weight.label}}</label>
                  {{form.maximum_gross_weight}} {{form.maximum_gross_weight.errors}}
                  </div>
                  <div>
                    <label>{{form.tare_weight.label}}</label>
                    {{ form.tare_weight }} {{ form.tare_weight.errors}}
                  </div>
{#                  <div>#}
{#                    <label>{{form.waste_type.label}}</label>#}
{#                    {{ form.waste_type }} {{ form.waste_type.errors}}#}
{#                  </div>#}
                  <div>
                    <label>{{form.hazard_warnings.label}}</label>
                    <select id="id_hazard_warnings" name="hazard_warnings" class="form-control">
                        <option>------------</option>
                        <option value="Explosionsgefährlich">Explosionsgefährlich</option>
                        <option value="Hochentzündlich">Hochentzündlich</option>
                        <option value="Leichtentzündlich">Leichtentzündlich</option>
                        <option value="Brandfördernd">Brandfördernd</option>
                        <option value="Sehr giftig">Sehr giftig</option>
                        <option value="Giftig">Giftig</option>
                        <option value="Gesundheitsschädlich">Gesundheitsschädlich</option>
                        <option value="Ätzend">Ätzend</option>
                        <option value="Reizend">Reizend</option>
                        <option value="Umweltgefährlich">Umweltgefährlich</option>
                    </select> {{ form.hazard_warnings.errors}}
                  </div>

<!--                    <div >-->
<!--                      <label>{{form.last_site.label}}</label>-->
<!--                      {{ form.last_site }} {{ form.last_site.errors}}-->
<!--                    </div>-->
                </div>
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12"><br>
                  <button type="submit" class="btn btn-primary ml-1"><i class="fas fa-save ml-2"></i> {% translate "Save2" %}</button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
{% block scripts %}
<script>
  // DataTable
  $(document).ready(function() {
    $("#id_unit").addClass("form-control");
    var table = $('#containerTable').DataTable(
       {
          "bLengthChange": false,
         initComplete: function () {
             // Apply the search
             this.api().columns().every( function () {
                 var that = this;
                 console.log(that)
// <!--                 $( 'input', this.footer() ).on( 'keyup change clear', function () {-->
// <!--                     if ( that.search() !== this.value ) {-->
// <!--                         that.search( this.value ).draw()-->
// <!--                     }-->
// <!--                 } );-->
             } );
         }
        });


    $("#containerTable_filter").hide()
  
    // custom search filter
    $('#mysearch').on( 'keyup', function () {
        table.search( this.value ).draw();
    } );
  
    //  custom show entries
    $('#showentries').change(function() {
        table.page.len(this.value).draw();
    } );
  
  });

  {% if request.user.is_superuser %}
      $(".loadCD").dblclick(function () {
          window.location = "#container-form-section";
          $("#collapse_18").addClass('show');
      })
      $(".loadC").click(function () {
          window.location = "#container-form-section";
          $("#collapse_18").addClass('show');
      })
  {% endif %}
  
   $("#new_entry").click(function(e){
    //e.preventDefault();
    $("#collapse_18").addClass('show')
    $('input#id_container_weight').val('0');
    $('input#id_maximum_gross_weight').val('0');
    $('input#id_tare_weight').val('0');
    $('select').val('');
    return false;
  })
</script>
{% endblock %}

<!DOCTYPE html>
<html lang="en">
   <head>
       {%block head%}
          {%load static%}
        {%endblock%}
        {% load i18n %}
        {% load l10n %}
      {%load custom_template_filters%}
      <meta charset="utf-8">
      <title>Delivery Note</title>
      <style>
          .Row {
    display: table;
    width: 100%; /*Optional*/
    table-layout: fixed; /*Optional*/
    border-spacing: 40px; /*Optional*/
}
          .Column {
            display: table-cell;
              border-left: 10px;
        }
        table { -pdf-keep-with-next: true; }
         p { margin: 0; -pdf-keep-with-next: true; }
         p.separator { -pdf-keep-with-next: false; font-size: 6pt; }
      </style>
   </head>
   <body>
      <div class="a4_sheet">
         <div class="row" style="padding-top:20%;">
            <br><br><br>
            <table  style="width:100%">
               <tr style="height: 100px;">
                  <td colspan="3" style="width:50%;text-align:left;padding-left:5px;font-size: 14pt;height:50%">

                     {% for head in logo %}
                     {{head}}<br>
                     {% endfor %} <br>
                     {% comment %} <b>{%if request.session.customer %}{% trans request.session.customer %} {%else%}{% trans "Customer" %}{%endif%}:</b><br> {{context}} {% endcomment %}
                     <b>{{customer|default_if_none:" "}}:</b><br> {{context|default_if_none:" "}}

                     {% comment %} {{dataset.customer.salutation}}  {% endcomment %}

                     {{ dataset.customer.name1|space|default_if_none:" " }} {% if dataset.customer.name2|default_if_none:" " %} {{ dataset.customer.name2|space|default_if_none:" " }} {% endif %} <br>

                     {% if dataset.customer.description is not None %}
                     {{ dataset.customer.description|space }}<br>
                     {%endif%}

                     {% if dataset.customer.street is not None %}
                     {{ dataset.customer.street|space }}<br>
                     {%endif%}

                     {% if dataset.customer.pin is not None %}
                     {{ dataset.customer.pin|space }}
                     {%endif%}

                     {% if dataset.customer.place is not None %}
                      {{ dataset.customer.place|space }}<br>
                      {%endif%}
                  </td>
                  <td colspan="3" style="text-align:center;">
                   {% for img in header_image %}
                   {% if img.logo.url is not None %}
                   <img src="{{ img.logo.url }}" style="width:200%;height:10%;"/>
                   {% endif %}
                  {% endfor %}

                  </td>
               </tr>
               <tr style="height: 80px;">
                  <td style="font-size:10pt;text-align:center;width:25%;padding-left:2px;">
                     <b>Wiegeschein<br>RW003
                     Lieferschein</b>
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     lfd. Nr<br><br>
                     {% if dataset.lfd_nr is not None and dataset.lfd_nr != "" %}
                        {{ dataset.lfd_nr }}
                    {% else %}
                        {{ dataset.id }}
                    {% endif %}
                  </td>
                  <td style="padding-left:5px;text-align:center;" >
                     Datum <br><br>
                     {% if tara_date == '1' or dataset.status == "0" %}
                        {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|date:"d.m.y" }}
                        {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|date:"d.m.y" }}
                        {% else %} {{ dataset.secondw_date_time|date:"d.m.y" }} {% endif %}
                      {% elif dataset.status == "1" %}
                        {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|date:"d.m.y" }}
                        {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|date:"d.m.y" }}
                        {% else %} {{ dataset.secondw_date_time|date:"d.m.y" }} {% endif %}
                      {% else %}
                        {{ dataset.secondw_date_time|date:"d.m.y" }}
                      {% endif %}
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     Uhrzeit<br><br>
                     {% if tara_date == '1' or dataset.status == "0" %}
                      {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|time:"H:i" }}
                      {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|time:"H:i" }}
                      {% else %} {{ dataset.secondw_date_time|time:"H:i" }} {% endif %}
                     {% elif dataset.status == "1" %}
                      {% if dataset.vehicle_weight_flag == 1%} {{ dataset.secondw_date_time|time:"H:i" }}
                      {% elif dataset.vehicle_second_weight_flag == 1 %} {{ dataset.firstw_date_time|time:"H:i" }}
                      {% else %} {{ dataset.secondw_date_time|time:"H:i" }} {% endif %}
                     {% else %}
                      {{ dataset.secondw_date_time|time:"H:i" }}
                      {% endif %}
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     Werk<br><br>
                     -----
                  </td>
                  <td style="padding-left:5px;text-align:center;">
                     Zufuhrart<br><br>
                     -----
                  </td>
               </tr>
               <tr style="height: 100px;">
               {% if io.status == 1 %}
                  <td colspan="2" style="width:50%;font-size:10pt;padding-left:5px;">
                     Ab-/Beladestelle Baustelle: <br>
                     {% if dataset.supplier.supplier_name is not None %}
                        {{ dataset.supplier.supplier_name|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.street is not None %}
                        {{ dataset.supplier.street|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.pin is not None %}
                        {{ dataset.supplier.pin|space }},
                     {% endif %}
                     {% if dataset.supplier.place is not None %}
                        {{ dataset.supplier.place|space }} <br>
                     {% endif %}
                  </td>
                  <td colspan="1" style="width:50%;text-align:center;font-size:11pt;padding-bottom:2px;">
                  Richtung : {% if dataset.status == "0" %}
                              <br><br> Eingang
                           {% elif dataset.status == '1' %}
                              <br><br> Ausgang
                        {% elif dataset.status == '2' %}
                              <br><br> Fremdwiegung
                           {% else %}
                              <br><br><br>
                           {% endif %}
                  </td>
                  {% else %}
                  <td colspan="3" style="width:50%;font-size:10pt;padding-left:5px;">
                     Ab-/Beladestelle Baustelle: <br>
                     {% if dataset.supplier.supplier_name is not None %}
                        {{ dataset.supplier.supplier_name|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.street is not None %}
                        {{ dataset.supplier.street|space }} <br>
                     {% endif %}
                     {% if dataset.supplier.pin is not None %}
                        {{ dataset.supplier.pin|space }},
                     {% endif %}
                     {% if dataset.supplier.place is not None %}
                        {{ dataset.supplier.place|space }} <br>
                     {% endif %}
                  </td>
                  {% endif %}
                  <td colspan="3" style="width:50%;font-size:12pt;padding-left: 5px">
                     Kennzeichen Nr. 1 : &nbsp;{{ dataset.vehicle.license_plate|upper }} <br><br>
                      Kennzeichen Nr. 2 : &nbsp;{{ dataset.trailer|upper }} <br><br>
                     AVV- Number : &nbsp;{{ dataset.article.avv_num|default_if_none:"-" }}<br>
                  </td>
               </tr>
            </table>
         </div>
         <div class="row">
            <table style="height:100px;width:100%;">
            {% if show_container == 'true' %}
               <tr style="height:31px;padding-top:5px;border-bottom:1px solid black">
                  <td colspan="2" style="font-size:15px;padding-left: 5px;text-align:left" > Container-Name : {{dataset.container|upper}} </td>
                  <td colspan="2" style="font-size:15px;text-align:left;">Type : {{dataset.container.container_type}}</td>
                  <td colspan='2' style="font-size:15px;text-align:left;">Weight : {{dataset.container.container_weight|unlocalize}} kg</td>

                  <td></td>
               </tr>
               {% endif %}
               <tr style="height:30px;font-size:10pt;padding-top:5px;">
                  <td></td>
                  <td >Datum</td>
                  <td style="">Uhrzeit</td>
                  <td style="width:5%;"></td>
                  <td >Gewicht</td>
                  <td >{{article}}</td>
                  <td>AlibiNr.</td>
               </tr>

               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;padding-left:5px;" >{% if dataset.vehicle_weight_flag == 1 or dataset.vehicle_second_weight_flag == 1 %} {% if dataset.vehicle_weight_flag == 1%} Tarawiegung {% else %} Bruttowiegung {% endif %} {% else %} {% trans "First Weighing" %} {% endif %}</td>
                  <td style="font-size:15px;">{{ dataset.firstw_date_time|date:"d.m.y" }}</td>
                  <td style="font-size:15px;">{{ dataset.firstw_date_time|time:"H:i" }}</td>
                  <td style="font-size:15px;">{% if dataset.vehicle_weight_flag == 1 %} PT {% elif dataset.vehicle_weight_flag == 2 %} H {%endif%}</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.first_weight|format_number }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.first_weight|format_number|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td>{{ dataset.firstw_alibi_nr }}</td>
               </tr>
               <tr style="height:30px;padding-top:5px;">
                  <td style="font-size:15px;padding-left: 5px" >{% if dataset.vehicle_weight_flag == 1 or dataset.vehicle_second_weight_flag == 1 %} {% if dataset.vehicle_second_weight_flag == 1 %} Tarawiegung {% else %} Bruttowiegung {% endif %} {% else %} {% trans "Second Weighing" %} {% endif %} </td>
                  <td style="font-size:15px;">{{ dataset.secondw_date_time|date:"d.m.y" }}</td>
                  <td style="font-size:15px;width:10%;"> {{ dataset.secondw_date_time|time:"H:i" }}</td>
                  <td style="font-size:15px;width:5%;">{% if dataset.vehicle_second_weight_flag %}{% if dataset.vehicle_second_weight_flag == 1 %} PT {% elif dataset.vehicle_second_weight_flag == 2 %} H {%endif%} {%endif%}</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.second_weight|format_number }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{ dataset.second_weight|format_number|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;"></td>
                  <td> {{ dataset.secondw_alibi_nr }}</td>
               </tr>
               <tr style="height:30px;padding-top:5px;">
                  <td colspan="3" style="font-size:15px;padding-left: 5px" >{% trans "Net Weight" %}</td>
                  <td style="font-size:15px;">E</td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{ dataset.net_weight|format_number }}&nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{dataset.net_weight|format_number|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  {% if dataset.material_weight is None%}
                  <td style="font-size:15px;">{{dataset.article.name|space}}</td>
                  {% else %}
                  <td></td>
                  {% endif %}
                  <td></td>
               </tr> {% if show_price == 'true' %}
               <tr style="height:30px;padding-top:5px;">
                  <td colspan="3" style="font-size:15px;padding-left: 10px;text-align:right" >Barbezahler-Preis : </td>
                  <td style="font-size:15px;text-align:right;">&nbsp;€&nbsp;</td>
                  <td style="font-size:15px;text-align:left;">{{dataset.total_price|unlocalize}}</td>
                  <td style="font-size:15px;"></td>
                  <td></td>
               </tr>
               {% endif %}

               {% if dataset.material_weight is not None and dataset.material_weight != 0 %}
               <tr style="height:30px;padding-top:5px;">
                  <td colspan="3" style="font-size:15px;padding-left: 5px; text-align:right;" >{% translate 'Material Weight' %} : </td>
                  <td style="font-size:15px;"></td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{dataset.material_weight|unlocalize}} &nbsp;kg</td>
                  {% else %}
                  <td style="font-size:15px;">{{dataset.material_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;">{{dataset.article.name|space}}</td>
                  <td></td>
               </tr>
               {% endif %}

               {% if dataset.deduction_weight is not None and dataset.deduction_weight != 0 %}
               <tr style="height:30px;padding-top:5px;">
                  <td colspan="3" style="font-size:15px;padding-left: 5px;text-align:right;" >{% translate 'Deduction Value'%} : </td>
                  <td style="font-size:15px;"></td>
                  {% if showt.status == 1 %}
                  <td style="font-size:15px;">{{dataset.deduction_weight|unlocalize}} &nbsp;kg</td>
                  {% else%}
                  <td style="font-size:15px;">{{dataset.deduction_weight|convert_to_tonne|unlocalize}} &nbsp;t</td>
                  {% endif %}
                  <td style="font-size:15px;">{{dataset.deduction.name}}</td>
                  <td></td>
               </tr>
               {% endif %}

            </table>
         </div>
         <br>
          <br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
          <div class="row">
            <table style="height:100px;width:100%">
               <tr style="height:25px;padding-top:5px;text-align:center;">
                  <td style="padding-left: 5px">Unterschrift des Wägers</td>
                  <td style="padding-left: 5px">Unterschrift des Fahrers</td>
                  <td style="padding-left: 5px">Unterschrift des Empfängers
                  </td>
               </tr>
               <tr >
                  <td style="height:25px;font-size:15pt;padding-bottom:5px;text-align:center;"><img src="{{sign.signature.url}}" style="width:100%;height:10%;"/><br> {{user_name}}</td>
                  <td > </td>
                  <td ></td>
               </tr>
            </table>
         </div>
         <div class="row">
         <br>
            E: errechnet, PT: Preset Tara (voreingegebenes Tara) H: Handeingabe. <br>
            Messwerte aus frei programmierbarer Zusatzeinrichtung. Die geeichten Messwerte können eingesehen werden.
            Für Überladungen haftet der Fahrzeuglenker.
         </div>
      </div>
      <input type="hidden" value="20/7204" name="customer code">
   </body>
</html>

<!DOCTYPE html>
<html>
    {%load static%}
    {% load l10n %}
    {% load i18n %}
   <head>
      <meta charset="utf-8">
          <link rel="stylesheet" type="text/css" href="{% static 'display/css/fontawesome.min.css' %}" />
        {% load i18n %}
        {% load l10n %}
   </head>
   <style>

      @page{
         margin:0.5cm;
         margin-top: 0cm;
         
         size:a4 portrait;
         
        

        
         
         @frame footer_frame {           /* Another static Frame */
            -pdf-frame-content: footer_content;
            left: 10pt; width: 575pt; top: 750pt; height: 120pt;
        }
      }
      .font-12{
         font-size:12px
      }
      .pt-2{
         padding-top:2px
      }
      .ta-l{
         text-align:left
      }
      .ta-r{
         text-align:right
      }
      .ta-c{
         text-align:center
      }
   </style>
   <body>

<div style="margin:0px;">
   <table border="0" style="width:100%;margin:0px;">
      <thead>
         <tr style="height:150px">
            <td colspan="8" > {% if heading.invoice_header.url is not None %} <img style="width: 100%; height:300px;" src={{heading.invoice_header.url}} /> {% endif %}</td>
         </tr>
         </thead>
      <tbody>
      
               {% comment %}<p class="font-12" style="font-weight:200;text-align:left">Vielen dank für Ihren Auftrag, wir berechnen Ihnen folgende Lieferung bzw. Leistung:</p>{% endcomment %}
            
         <tr style="height:200px;">
            <th colspan="3" style="padding-top:3px;text-align:left;padding-left:2px;">
               <p style="font-size:18px;text-align:left;padding-right:10px">Gutschrift: {{obj.id}}</p>
               <p> <span style="font-size:14px; font-weight:200;">Kunde: </span> <br> <span style="font-size:13px; font-weight:100;">
                  {{cust.name1|default_if_none:" "}} <br>{{cust.street|default_if_none:" "}} <br> {{cust.pin|default_if_none:" "}} {{cust.place|default_if_none:" "}}</span>
               </p>
               <p><span style="font-size:14px; font-weight:200;">Fahrzeug: </span> <br><span style="font-size:13px; font-weight:100;">
               {{trans_data.vehicle.license_plate}}</span></p>
            </th>
            <th colspan="3" style="padding-top:3px;text-align:center;padding-left:2px;">
               <p style="font-size:20px;font-weight:800;">
                  {% if status == '1' %}
                  Ausgang
                  {% elif status == '0' %}
                  Eingang
                  {% else %}
                  {% endif %}
                  </p>
            </th>
            <th colspan="1" style="padding-top:3px;padding-left:2px;">
               {% comment %}<p style="text-align:left;">Gutschrift</p>{% endcomment %}
               {% comment %}<p style="text-align:left;">Kunden-Nr.</p>{% endcomment %}
               <p style="text-align:left;">Rechnungdatum</p>
               <p style="text-align:left;">Lieferdatum</p>

            </th>
            <th colspan="1" style="padding-top:3px;padding-left:2px;">
               {% comment %}<p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{obj.invoice_no}}</span></p>{% endcomment %}
               {% comment %}<p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{cust.id|default_if_none:"--"}}</span></p>{% endcomment %}
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{obj.time|date:"d-m-Y"}}</span></p>
               <p style="text-align:right;"><span style="font-size:10px; font-weight:100;">{{created|date:"d-m-Y"}}</span></p>
              
            </th>
         </tr>
         

         
         <tr style="height:30px;border-bottom:1px solid grey;">
            <td class="font-12" colspan="2" style="text-align:left;font-weight:500;padding-left:2px;">Artikel</td>
            {% comment %} <td class="font-12" colspan="1" style="text-align:left;font-weight:500">{% translate 'Quantity' %}</td> {% endcomment %}
            <td class="font-12" colspan="2" style="text-align:left;font-weight:500">{% translate 'Weight' %} [kg]</td>
            {% comment %} <td class="font-12" colspan="1" style="text-align:center;font-weight:500">{% translate 'Material Weight' %} </td> {% endcomment %}
            <td class="font-12" colspan="1" style="text-align:left;font-weight:500"> Einzelpreis </td>
            <td class="font-12" colspan="1" style="text-align:center;font-weight:500">{% translate 'Mwst' %}</td>
            <td class="font-12" colspan="2" style="text-align:right;font-weight:500"> Gesamtpreis </td>
         </tr>
         {% for data in data %}
         
         <tr style="height:30px;border-bottom:1px solid grey;">
            <td colspan="2" class="font-12 pt-2" style="padding-left:2px;">{{data.deduction|default_if_none:" "}}</td>
            {% comment %} <td colspan="1" class="font-12 ta-l">{{data.net_weight|default_if_none:"0"}}</td> {% endcomment %}
            <td colspan="2" class="font-12 ta-l">{{data.deduction_weight|default_if_none:"0"}}</td>
            {% comment %} <td colspan="1" class="font-12 ta-c">{{data.material_weight|default_if_none:"0"}}</td> {% endcomment %}
            <td colspan="1" class="font-12 ta-l"> {{data.price_per_item|default_if_none:"0"}} €</td>
            <td colspan="1" class="font-12 ta-c">{{data.vat|default_if_none:"0"}}</td>
            <td colspan="2" class="font-12 ta-r"> {{data.total_price|default_if_none:"0"}} €</td>
         </tr>
        
         {% endfor %}
         <tr style="height:50px;">
            <th colspan="5"></th>
            <th colspan="2" style="text-align:left;border-bottom:1px solid grey;border-width:8px;">
               <p style="font-size:13px;">Gesamtsumme</p>
            </th>
            <th colspan="1" style="text-align:right;border-bottom:1px solid grey;border-width:8px">
               <p style="text-align:right;"><span style="font-size:13px; font-weight:500;">{{total|default_if_none:"0"}} €</span></p>
            </th>
         </tr>
         <tr>
            <td>

            </td>
         </tr>
         <tr style="height:50px; ">
            <td colspan="8">
               <strong style="font-size:12px;">
                  
                  Der Rechnungsbetrag enthält keine Mwst. Die Steuer wird nach § 13b Abs.2 Nr.7 USTG. vom Leistungsempfänger geschuldet.
               </strong>
            </td>
            
         </tr>
         <tr>
            <td><br></td>
         </tr>
         <tr style="height:50px;">
            <td colspan="2"></td>
            <td colspan="6" >
                  
                  <p style="margin:0px;padding:0px;font-size:14px">
                  {{current_date}}  _____________________________________________________
               </p>
            </td>
            
         </tr>
         <tr>
            <td colspan="3"></td>
            <td colspan="5" >
                  
                  <p style="margin:0px;padding:0px;font-size:14px">
                     Bedinungen/Hinweise zur Kenntnis genommen Betrag erhalten.
               </p>
            </td>
            
         </tr>
         
         

      </tbody>
         
   </table>

   </div>
   

<div id="footer_content" >
   <p style="margin:0px;padding:0px;font-size:12px">
      Es wird versichert, dass das vorstehendes Material rechtmäßig erworben wurde und freies Eigentum ist und den steuerlichen Verpflichtungen nachgekommen wird. Einnahmen aus Schrottverkäufen sind einkommenssteuerpflichtig
</p>
<br>
   {% if heading.footer.url is not None %} <img src={{heading.footer.url}} /> {% endif %}

</div>
   
</body>
</html>
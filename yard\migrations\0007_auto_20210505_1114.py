# Generated by Django 3.1.1 on 2021-05-05 09:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('yard', '0006_auto_20210429_0955'),
    ]

    operations = [
        migrations.AlterField(
            model_name='transaction',
            name='article',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.article'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.customer'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='price_per_item',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.supplier'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.vehicle'),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='vehicle_weight_flag',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AlterField(
            model_name='transaction',
            name='yard',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='yard.yard_list'),
        ),
    ]
